/**
 * 监听器实现
 */
export default class SubjectListenerImpl<T extends Observer> implements SubjectListener {
    observers: T[] = [];
    register(observer: T) {
        this.observers.push(observer);
    }
    remove(oberser: T) {
        const index = this.observers.findIndex(item => item === oberser);
        if (index != -1) this.observers.splice(index, 1);
    }
    notify(...args: any) {
        console.warn("监听器通知方法需要重构");
    }
}