import { fileURLToPath } from "url";
import path from "path";
import webpack from "webpack";

const { DefinePlugin } = webpack;

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
function resolve(dir) {
  return path.join(__dirname, "../" + dir);
}
import HtmlWebpackPlugin from "html-webpack-plugin";
import { VueLoaderPlugin } from "vue-loader";

export default {
  mode: "development",
  entry: resolve("demo/index.ts"),
  output: {
    path: resolve("dist"),
    filename: "[name].bundle.js",
  },
  devServer: {
    hot: true,
    open: true,
    static: {
      directory: resolve("demo/public"),
    },
    port: 12000,
  },
  plugins: [
    new HtmlWebpackPlugin({
      title: "sensors data sdk",
      template: resolve("demo/public/index.html"),
      filename: "index.html",
    }),
    new VueLoaderPlugin(),
    new DefinePlugin({
      "process.env.LIB_ENV": JSON.stringify(process.env.LIB_ENV),
    }),
  ],
  module: {
    rules: [
      {
        test: /\.(t|j)s$/,
        exclude: /node_modules/,
        use: {
          loader: "babel-loader",
          options: {
            cacheDirectory: true,
          },
        },
      },
      {
        test: /\.(png|jpg|gif|svg)$/,
        loader: "file-loader",
        options: {
          name: "[name].[ext]?[hash]",
        },
      },
      {
        test: /\.vue$/,
        use: ["vue-loader"],
      },
      {
        test: /\.(css|scss|sass)$/,
        use: [
          "style-loader",
          "css-loader",
          "postcss-loader",
          "sass-loader",
          {
            loader: "style-resources-loader",
            options: {
              patterns: [resolve("demo/styles/globals/*.scss")],
            },
          },
        ],
      },
    ],
  },
  resolve: {
    mainFiles: ["index"],
    extensions: [".ts", ".js"],
    alias: {
      "@": resolve("src"),
      "@d": resolve("demo"),
    },
  },
};
