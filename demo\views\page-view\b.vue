<template>
    <div class="pageview-b">
        我是页面B - 请检测是否有pageview事件上报
        <router-link :to="{name: 'PageViewA'}">A页面</router-link>
        <router-link :to="{name: 'Routing'}">返回路由页</router-link>
    </div>
</template>

<script lang="ts">
import { defineComponent } from "vue";

export default defineComponent({
    name: 'Page-B'
});
</script>

<style scoped lang="scss">
    .pageview-b {
        display: flex;
        flex-direction: column;
    }
</style>