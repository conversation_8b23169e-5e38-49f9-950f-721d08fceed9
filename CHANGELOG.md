## [1.3.52](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/compare/v1.3.51...v1.3.52) (2025-12-19)


### Bug Fixes

* 修复app上报埋点数据title空值 ([1b3d14a](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/change/60))



## [1.3.51](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/compare/v1.3.50...v1.3.51) (2025-12-05)


### Bug Fixes

* 点击上报兼容京东小程序 ([d46303a](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/change/59))



## [1.3.50](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/compare/v1.3.48...v1.3.50) (2025-11-05)


### Bug Fixes

* 修复初始化配置失效 ([1417121](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/change/58))


## [1.3.49](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/compare/v1.3.48...v1.3.49) (2025-10-22)


### Bug Fixes

* 修复uniapp确认订单事件路由链路错误 ([0a6a7ca](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/0a6a7ca960d58b6ab5a98221f69547a33bb0012a))



## [1.3.48](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/compare/v1.3.47...v1.3.48) (2025-09-05)


### Bug Fixes

* 调整逻辑 ([eccb08b](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/change/55))
* 生成匿名id规则优化 ([d8346b6](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/change/55))
* 修改匿名id生成规则 ([45bd5fe](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/change/55))
* 修改匿名id生成规则 ([1e40a17](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/change/55))
* initial_id初始化赋值 ([d27b4a7](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/change/55))



## [1.3.47](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/compare/v1.3.46...v1.3.47) (2025-09-04)


### Bug Fixes

* add trim fn to filter distinct id ([4944bf4](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/4944bf4c975e2cf62cf7881721b6774471f9eca4))



## [1.3.46](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/compare/v1.3.45...v1.3.46) (2025-09-02)


### Bug Fixes

* get value for empty distinctid ([5ee407c](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/5ee407c953b4c3d23f30fd2453579f174a37b8a5))



## [1.3.45](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/compare/v1.3.44...v1.3.45) (2025-09-02)


### Bug Fixes

* 更新支付宝小程序来源渠道映射 ([63b8ee4](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/63b8ee43df5a1565449c74adab1893cfb9e5a5f4))



## [1.3.44](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/compare/v1.3.43...v1.3.44) (2025-08-05)


### Bug Fixes

* 添加可选链操作符以防止空值错误 ([c813990](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/c81399038e0003694e347fd4622e4d24321b1ad6))



## [1.3.43](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/compare/v1.3.42...v1.3.43) (2025-08-02)


### Features

* app capture screen check ([3c7ada7](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/3c7ada745b971b9650968d89d95bbd28b66527d0))
* uniapp sensors impl add setWiFiInfo fn ([cd928a5](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/cd928a54ee517c32c51a25067f9a86b5c7089cba))



## [1.3.42](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/compare/v1.3.41...v1.3.42) (2025-08-01)


### Features

* 支持京东小程序上报 ([f9d4073](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/f9d4073be4e4d0f1877e1cbe13e03101090eb2c5))



## [1.3.41](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/compare/v1.3.40...v1.3.41) (2025-07-31)


### Bug Fixes

* 兼容抽屉下单不经过affirm页面无法归因订单 ([2c19d4b](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/2c19d4b7855f25c9d80d7924fadf564163c1c487))


### Features

* 程序启动事件上报启动参数 ([82f1199](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/82f1199a21cb7b292d51c67123e1f033bc37116e))



## [1.3.40](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/compare/v1.3.39...v1.3.40) (2025-07-21)


### Bug Fixes

* tabbar点击上报事件兼容api调用方式 ([9ed7ca1](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/9ed7ca1606bc650d0a62edebf744ee3f35c4811f))



## [1.3.39](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/compare/v1.3.38...v1.3.39) (2025-06-19)


### Features

* 修复移除生命周期方法无效问题 ([1d1d448](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/1d1d448b33dbe90fa6805ebddfd45da9ad0b80e0))



## [1.3.38](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/compare/v1.3.37...v1.3.38) (2025-05-21)


### Features

* 打包时就进行环境判断 ([b271a02](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/b271a02b6e1b9ffb6f513f8db4aa3c8159a331b9))



## [1.3.37](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/compare/v1.3.36...v1.3.37) (2025-05-19)


### Features

* 上报wifi信息字段 ([cea5497](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/cea5497c7f5811b8f0bb7a9624ad2bc9649dbf17))



## [1.3.36](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/compare/v1.3.35...v1.3.36) (2025-05-07)


### Bug Fixes

* 对webview环境判断提前 ([ebda5d2](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/ebda5d2fbd6b66f4b2cb8dc3a555d7f663eff504))
* 去掉多余代码 ([76e3062](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/76e3062ebe9f58873095ca03fe948489d201178d))



## [1.3.35](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/compare/v1.3.34...v1.3.35) (2025-05-06)


### Bug Fixes

* 更新发包地址 ([9466f09](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/9466f09536f914f48672f90964d5cb532175a346))


### Features

* 修改 offUserCaptureScreen ([385b090](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/385b090e07439220c2eb523ba65dbed646da3a9e))



## [1.3.34](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/compare/v1.3.33...v1.3.34) (2025-04-30)


### Features

* 添加用户截屏事件监听 ([a9d35c1](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/a9d35c1887db5ee55fcdd0e592209e8212f2e26c))
* uniapp端增加监听截图 ([e33e503](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/e33e503b66cccba074c6ad6bbba8f34be4080beb))



## [1.3.33](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/compare/v1.3.32...v1.3.33) (2025-03-13)


### Features

* 修改page_version类型 ([5a20785](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/5a20785cf9258a642597c73061a784c8c7930f5a))
* 增加uniapp js的page_version数组处理 ([d3626a9](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/d3626a98092d9e7290cad9147e032b0ad51e3d74))



## [1.3.32](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/compare/v1.3.31...v1.3.32) (2025-03-04)


### Features

* 增加 page_version 数组上报 ([4513af6](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/4513af69a081fcfec269e36d819effb895ef0b87))



## [1.3.31](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/compare/v1.3.30...v1.3.31) (2025-03-03)


### Features

* 分享回流事件补充 ([3311833](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/331183388d555db9a2796a19bf5379c516fc2c0f))



## [1.3.30](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/compare/v1.3.29...v1.3.30) (2025-02-26)


### Features

* 钉钉端曝光事件移除 ([770d31f](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/770d31fce99e770bad06b8d7f3cff7fb7015a813))



## [1.3.29](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/compare/v1.3.28...v1.3.29) (2025-02-26)


### Features

* 增加获取生命周期属性的方法 ([6047d91](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/6047d91a74501705f3e7099000b36768c099ddc5))



## [1.3.28](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/compare/v1.3.27...v1.3.28) (2025-02-25)


### Features

* 增加钉钉端曝光事件处理 ([61e577f](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/61e577f1ebdf202c3333aa2f539ab634e1932c76))



## [1.3.27](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/compare/v1.3.26...v1.3.27) (2025-02-22)


### Features

* 增加对支付宝小程序环境webview页面判断 ([73bc573](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/73bc5731b840fa3d96fb8780a7858490c01137b1))



## [1.3.26](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/compare/v1.3.25...v1.3.26) (2025-02-19)


### Features

* uniapp各端曝光事件 ([2872bef](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/2872bef765f458c5b02592457a88d6fe7392a92c))



## [1.3.25](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/compare/v1.3.24...v1.3.25) (2025-02-12)


### Features

* 去掉空值判断 自定义传入属性如果值为null等空值也能覆盖掉之前的值 ([8e3973f](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/8e3973f3f75cb6a5514d9658a9ab583579ab365c))



## [1.3.24](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/compare/v1.3.23...v1.3.24) (2025-01-22)


### Bug Fixes

* 补充遗漏的extraRequestHeaders ([6b7588d](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/6b7588d1fff7a2d4b737fac9e549ae69d756b98e))



## [1.3.23](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/compare/v1.3.22...v1.3.23) (2025-01-21)


### Features

* 增加额外请求头 ([bc23f31](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/bc23f31a749c7a431dbb171a72fb2a0df3963c22))



## [1.3.22](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/compare/v1.3.21...v1.3.22) (2025-01-13)


### Bug Fixes

* 修复判断获取平台环境 ([e54624d](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/e54624d52bb04e375841c3bfffef23df7df73bda))



## [1.3.21](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/compare/v1.3.20...v1.3.21) (2025-01-13)


### Features

* 新增前端埋点时长工具函数 ([cdb85d2](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/cdb85d28e26dce722f45de2e5806aeb6b6c63b77))



## [1.3.20](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/compare/v1.3.19...v1.3.20) (2025-01-09)


### Features

* 补充js遗漏的onBeforePageLoad ([8b08ed8](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/8b08ed8102fb445e598e458f736f6a6cdc93a40d))



## [1.3.19](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/compare/v1.3.18...v1.3.19) (2025-01-09)


### Features

* 增加session_id 修复initial_id ([b84991c](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/b84991c3884256dc731eed939b63bc57eab9d928))



## [1.3.18](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/compare/v1.3.17...v1.3.18) (2024-12-25)


### Features

* 调整支付宝上报限制 ([fc92eff](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/fc92effed8b118208af6f3942bc2c8256b853922))



## [1.3.17](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/compare/v1.3.16...v1.3.17) (2024-12-16)


### Bug Fixes

* 修复运行时环境判断错误 ([451302d](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/451302d5b43ad81653e2a6d297061fee07756d1d))


### Features

* 防止控制台报错信息过多 ([f7f6e30](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/f7f6e30f4e39672c6c17fc84adaded31fb1e4061))



## [1.3.16](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/compare/v1.3.15...v1.3.16) (2024-12-12)


### Bug Fixes

* 修复运行时环境判断报错 ([e6105bb](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/e6105bb60af70e413617184d918c00657bebde06))


### Features

* oppo端增加匿名id处理 ([863b038](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/863b038a5fdff23f4a2035db3dbb4fd3b4d65d9c))



## [1.3.15](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/compare/v1.3.14...v1.3.15) (2024-12-06)


### Bug Fixes

* 完善微信小程序分享逻辑 ([745aa5d](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/745aa5d33f05a0e48c9dc030db8d12a2363ef583))
* 修复渠道来源上报错误 ([5cc43b1](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/5cc43b1061ce01adcfc59efaa44efc67842d617f))



## [1.3.14](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/compare/v1.3.13...v1.3.14) (2024-11-22)


### Bug Fixes

* 插件页面获取不到路由报错 ([d9e17b1](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/d9e17b199d1d3476fed9779960f7cc5ca15051d2))
* 插件页面控制台报错问题 ([339761d](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/339761d8a19fc832cd83bdb9d895a965a5d62961))
* 更改npm构建配置 ([7fe4fb0](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/7fe4fb0d06d8208889a1049481b7bae7f374943f))
* 修复微信小程序分享错误 ([756fd14](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/756fd1418ec406289fca2de7e9237de6ed69f889))
* 修复uniapp插件页面控制台报错 ([d05ba47](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/d05ba47efe166d94960799ff617e1a47200611da))



## [1.3.13](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/compare/v1.3.11...v1.3.13) (2024-11-20)


### Bug Fixes

* 修复运行环境判断 ([b142086](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/b142086ee6bccf7e46965029299727a98c9a0350))
* 引入插件时控制台上报报错 ([aa5aa03](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/aa5aa03019b7a1f8593d3a681dd16ca6d345f61c))
* 优化 ([b1012d6](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/b1012d6649bd304d530c33d6ef98bd7584eea03e))
* 运行时环境判断兼容 ([3d05b50](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/3d05b508513893c2ff7b34c71963b13fb05e5c6a))


### Features

* 华为、小米品牌小程序设置匿名id ([a84125f](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/a84125f9bd54a24eb8357f6216d6761073b1d8be))



## [1.3.11](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/compare/v1.3.10...v1.3.11) (2024-11-19)


### Features

* 调整自定义属性优先级 ([53cf58e](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/53cf58e55100a2fd4de0cbbdeef31efe769b283b))
* 所有端增加一个初始id 记录更改前的匿名id ([04b1dd8](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/04b1dd836b00d35239b02eed95f0e7a3737bca54))



## [1.3.10](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/compare/v1.3.9...v1.3.10) (2024-11-15)


### Bug Fixes

* 分享拦截兼容处理 ([b55272c](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/b55272c5d4cbd07e48d87e75af08711bf35b9474))
* 拓展字段类型 ([5103bae](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/5103bae0de8514a942cb90ab05fe66a427b9cbac))
* 修复正则匹配错误 ([304f97b](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/304f97bcd2e596d41268a35570d1b1d91be4634e))



## [1.3.9](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/compare/v1.3.8...v1.3.9) (2024-11-12)


### Bug Fixes

* 分享事件path参数去重 ([d62cffe](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/d62cffe895fac255acec142674c76517a1ba3f29))


### Features

* 处理动态属性上报 ([004da26](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/004da261541a1a50b55ca4acf942e809caeeadcf))



## [1.3.8](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/compare/v1.3.7...v1.3.8) (2024-11-09)


### Bug Fixes

* 修复uni-app分享事件重写错误 ([2a9a668](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/2a9a668793ec5f6cbbab585abf473491bf71525e))



## [1.3.7](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/compare/v1.3.6...v1.3.7) (2024-11-07)


### Features

* queryAttr增加限制 ([47b66f4](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/47b66f421055aa266a083d51ccc4dca5552e77d1))



## [1.3.6](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/compare/v1.3.4...v1.3.6) (2024-11-06)


### Bug Fixes

* 修复登录属性 ([33fe872](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/33fe872fe7fefc8505016bc68b7364bec3ca763e))
* 修改通用id更新逻辑 ([7321e65](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/7321e659fa19ab4f8f34f10b6a69e8827e5f2f53))


### Features

* 增加honor品牌小程序设置匿名id逻辑 ([91440be](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/91440be65ac760ac7d735459460fab3f9a646a77))
* vivo端口匿名用户id逻辑修改 ([c5e6ba0](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/c5e6ba0789b2976177175e87e67bb244e58858f5))



## [1.3.4](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/compare/v1.3.2...v1.3.4) (2024-10-22)


### Features

* 获取参数增加判断等 ([b0ce35b](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/b0ce35b6c375b85783986d5c53fcccf3823f0b55))
* 轮询收藏接口10分钟一次 ([3a0f7c0](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/3a0f7c0a04f777f3dedd063e924db7d50cde5b96))
* 轮询收藏接口10分钟一次 ([5595551](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/559555193733de0026a54735ce859771a34e6c27))
* 增加sort数组上报 ([017259d](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/017259d3482e46ee2237e89f3d68f524163a98d2))



## [1.3.2](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/compare/v1.3.1...v1.3.2) (2024-10-11)


### Bug Fixes

* 完善逻辑 ([fd4e753](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/fd4e75378594fd44ff685af6ab24912a72bab484))


### Features

* 增加快手小程序的兼容 ([de8b26b](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/de8b26bbf18b7a31d3697ae542fb721144e713eb))



## [1.3.1](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/compare/v1.3.0...v1.3.1) (2024-09-29)


### Bug Fixes

* 修复跳转插件页面时报错问题 ([32979f1](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/32979f103bdce866cf84d0af638626b71a284fa7))



# [1.3.0](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/compare/v1.2.9...v1.3.0) (2024-09-23)


### Features

* 埋点增加universal_id属性 ([1692297](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/16922970f5b757b55237ec96612c08e381026b68))
* web端增加通用id请求方法 ([3eadac2](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/3eadac2f0af62333786ee71977ed3569330fe5a8))



## [1.2.9](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/compare/v1.2.8...v1.2.9) (2024-09-12)


### Bug Fixes

* 修复渠道来源上报 ([375dc9f](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/375dc9ff31fd6b9360c39d5e8844d1e26e1667aa))


### Features

* 添加自定义下载渠道 ([ccce494](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/ccce494b6fb6e3acb7ded894773791f7cd7cdbbb))



## [1.2.8](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/compare/v1.2.7...v1.2.8) (2024-08-10)


### Features

* 支付宝端添加初始id ([8fc6529](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/8fc6529083e4183eb911232be1a3328271b0eb19))



## [1.2.7](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/compare/v1.2.4...v1.2.7) (2024-08-08)


### Bug Fixes

* 修复渠道来源上报错误 ([cdce739](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/cdce7392bb10fad212d38b3064c326cf246ab24c))
* 修复uni-app页面停留时间错误 ([9d7d25f](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/9d7d25fafdbc62b5b1010928f15414c3ea078a22))


### Features

* 补充uniapp-h5的坑位字段 ([fc742f4](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/fc742f49984ab3de5f7b6f4430308cd44368cd1a))
* 处理匿名id逻辑 ([152b4e2](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/152b4e2814ede7b46019032d234f14169eb741eb))
* 匿名id替换逻辑 ([bd3f9ed](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/bd3f9ed3213556c988db9ea88bd6f8752803d61c))
* 添加indexModuleKey和index_module_key_list ([54d28f3](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/54d28f314572bf25e28dcb9d015dfe3e86e42b7b))
* 微信端增加unionid替换方法 ([422baba](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/422babaebefd6c24778452b6449ff7ea898912d8))
* 支付宝端未修改匿名id前不上报数据 ([a61cd3c](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/a61cd3cf2bb263797b69a5d300c73063ccfda1ae))



## [1.2.4](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/compare/v1.1.19...v1.2.4) (2024-07-17)


### Bug Fixes

* 点击事件参数缺失bug ([0caac59](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/0caac5977012b1e2c8995fe80cb3fbc8fdaf9ba4))
* 调整构建辅助插件逻辑 ([07bdc05](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/07bdc05cb93c3256fc0a266b8c7f917a8d622c83))
* 更正版本号 ([ebf026d](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/ebf026d9d0f67e30b935c52cbfb7604504f7fe6c))
* 完善分享回流上报属性 ([1d9591e](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/1d9591e1cdfee7cdfd9ed7c4d84a6dd9c0811009))
* 完善事件类型判断 ([7555122](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/755512270a4d9c784f5ce943cbeec1b0a724cb38))
* 修复支付宝小程序底部导航栏点击上报url错误 ([0645214](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/06452149b63dc92ffb981f49cdcea4ad09f237ef))
* 修复支付宝小程序页面停留时间错误 ([416c8ce](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/416c8ce520cb0599bf121e95431262abbf5e1041))
* 修复APP点击上报属性缺少 ([ad2cef7](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/ad2cef7aa64780510a4962617e23ed40946cd5fc))
* 修复app端点击拦截方法报错 ([35ba6e6](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/35ba6e69f5ad31977b5875cf9e6c2d4c9db76886))
* 修复app端点击事件不上报 ([42f95b1](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/42f95b18032ae12a23b80b2d5f0cc270fff8ced8))
* 修复this指向问题 ([5f32348](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/5f32348371f8af3f8f2da9b3f58dcf4dff548085))
* 修复uniapp h5端使用浏览器端方法时的路径问题 ([e7a66c9](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/e7a66c918725729e1335b534f98d70208ee4f40d))
* 修复uniapp小程序端点击事件上报问题 ([f7d04f2](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/f7d04f246fb1cb9b0125c573754536a0bc3726ff))
* 修改构建辅助插件 ([0b96931](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/0b969315afd1708383c9790125339738c6563bcc))
* 修改页面突变器非单页面应用实际未生效问题 ([f92c401](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/f92c401984dc65378d505b215a87e6509f49c5c5))
* 增加头条小程序函数劫持 ([9fc6d05](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/9fc6d058fbfba0f4ef7dd4b61fd78bc9d38cc0ff))


### Features

* 兼容钉钉小程序 ([5998903](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/5998903cccad47d487d6d25c74d8cf323d37f609))
* 兼容h5 闲鱼小程序 坑位点击上报以及底部tab ([b3b86a7](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/b3b86a794bb943d49673df26799af07cbcbc195a))
* 仅针对商家后台的更改 ([9c44bd5](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/9c44bd557608a918fb9d9b3909ea75d7eb8cd531))
* 浏览器端兼容uniapp web端调整 ([4dc98c8](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/4dc98c8fe059d22700ec2fe329195209ea9cb0ec))
* 判断条件调整 ([c2a1e97](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/c2a1e97547f617e3d7a2a65714dff1acb0ec8ec2))
* 商家后台专用 ([95d6f36](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/95d6f3621158f2ce45801eeb547a083f880e96cf))
* 同步商家后台改动 ([e272e7b](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/e272e7bfe20cc83e57359eeb92e45b572555a999))
* 新增底部导航栏点击上报 ([20ba642](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/20ba6427328acf48cd5cecd201af24e0568794a0))
* 新增新旧地址转换对象 ([6ef610d](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/6ef610d01148f4f250b6b3eb27a4214d02f26b21))
* 新增tabbar上报参数 ([52f9085](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/52f90855daa3ae69842d3ffdb2b1fc8eca422353))
* 针对商家后台处理 ([5a3f890](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/5a3f8903bdd37368e20c2a6bf8e6ac01791b56e2))
* beforeshow缓存判断调整 ([6f5f0d5](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/6f5f0d5ff386bc468331b5d2f33893b4ef7e0688))
* js-request新增额外参数请求 ([83af2e0](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/83af2e088fb882bca66dfb00244199d8e547f3a0))
* page_list添加对trackUrlMap的判断 ([d5d8c6f](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/d5d8c6f5b34bc0d3d556e5919ed2f23dc1d46065))
* uni-app区分最近一次站外流量来源类型 ([0700dd3](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/0700dd396f63691cff7cbe20de794290eba2636d))


### Reverts

* 回退添加额外参数的更改 ([11a6871](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/11a6871415d214e9a5e79720bb732136c834b3ae))



## [1.1.19](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/compare/dadb6dddfd937c1191f71182d5f8b062f377224a...v1.1.19) (2023-11-18)


### Bug Fixes

* 收藏api增加兼容 ([74a1f45](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/74a1f45f60d66c89a2af1e4683017a07b69104c8))
* 修改字段为小驼峰命名 ([d21070c](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/d21070c310d3d512dda72b759aa92d634411af96))
* uniapp 运行异常 ([5e16ce3](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/5e16ce3d08ffdf4da3c994336bfc4c2298c8e984))


### Features

* 打包 ([a87c8d5](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/a87c8d57ac4f84946e281d4880e622dc03fb20f8))
* 单页面才采集程序启动和关闭 ([4bf976c](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/4bf976c1ea23c76cf661743f9e350a4919e294f6))
* 调整bindServerUrl可为空，为空则等待登陆之后才回开始上报数据 ([743280b](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/743280bb8e30adf071a7bd45b155a49c4ab266d0))
* 浏览器端单页面也添加程序启动埋点 ([b5cf391](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/b5cf39156b38d01b781306175e7f5049fa36780f))
* 浏览器端埋点优化 ([b92fb6c](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/b92fb6c34d6fc9bdf9bd827bc7da45e75aa138d9))
* 浏览器端优化 ([10e861a](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/10e861a01b174295e7cb9186242ad664599b98cf))
* 浏览器条件判断开始程序启动和程序关闭 ([686cf71](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/686cf7163327d5ce7026bcecf0813d92ab039763))
* 切换账号的时候先把缓存数据上报 ([a1e7633](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/a1e7633baafd89338f06e1c2710bf68ca305b5de))
* 去掉isTheLast参数 ([11a40bf](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/11a40bfe79ecaa5c3cd0b6687841e4ebf909ef71))
* 添加$is_collected属性 ([2e8bcb6](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/2e8bcb63ca5f6530c3bd55bdd3fa6160ac1bd4cc))
* 添加测试用例 ([782c72e](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/782c72ee2d48cc4f6255341ef02a14011acf8921))
* 添加坑位统计 ([bfb0e22](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/bfb0e22471c405d66bc392cb4cfa65135c6d4718))
* 添加浏览器程序卸载上报 ([273a385](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/273a385a2ea74399e14ac7abbab9e63b3a1af6e1))
* 添加上报可深克隆 ([58d01c9](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/58d01c992ffae6f0fef88a8223ff2b1f18f83ccf))
* 添加上报字段$source_terminal ([42dfb3c](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/42dfb3c174dcb89bc259cbeeaceb5c9117fb4693))
* 添加生命周期属性 ([6657e8c](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/6657e8caf32527f4846a016f12f5d1feae03c6a4))
* 添加拖动监听器 ([00682e8](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/00682e8d8f9cfc4d7f5ceb8c9f7188f08cab6469))
* 添加小程序页面点击监听 ([33dd640](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/33dd6401b56ef406c0b557fa10c01c04965863f7))
* 添加页面参数上报 ([d32ca72](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/d32ca72b6b45e8a243b1e6409c814fd63faf4a8e))
* 添加页面关闭事件 ([30d5941](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/30d59414e967d5bba178a51add23c6ff597511b6))
* 添加页面接收到statistical_from参数 ([23d5d8f](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/23d5d8f48f2cdf914e27e66d459c3b60e70fdb95))
* 添加页面停留，页面点击，页面显示监听 ([68a16ae](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/68a16ae0d035e21c9754383891524a8e151924a4))
* 添加uniapp坑位埋点 ([66c71a0](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/66c71a0a3ac81ae63a10aed2887cb22a0628e07e))
* 完善 ([dcc29c6](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/dcc29c62fc61972adad62bf76105147ffbc1cee4))
* 完善 ([36c379d](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/36c379db8e04ff74ebbd72a831194c24f58a38fe))
* 完善 ([5bc3f54](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/5bc3f545697d6f1478892eb1933e33c1e778a821))
* 完善 ([aad5bbb](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/aad5bbb3337364f17119b97df4f8c720333d0921))
* 完善显现监听 ([57c8ae2](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/57c8ae20533b030bcdf6ac58e5c1a6236034e968))
* 完善sdk ([ec030ca](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/ec030ca19fb4dd159ad20260e7c7287b2355c28f))
* 修复测试demo启动问题 ([dadb6dd](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/dadb6dddfd937c1191f71182d5f8b062f377224a))
* 修复分享bug ([e8f2042](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/e8f2042fdd7859c099e6250333c6bc1eeb951304))
* 修改备注名称 ([a2f7096](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/a2f70963cb29959804f88719c2ce878f18d34383))
* 修改来源终端字段 ([2e0cb6a](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/2e0cb6aebea19323a833e0394d4876dc3ee5e628))
* 修改sendBeacon发送application/x-www-form-urlencoded ([0bb3733](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/0bb3733d0b09ff3908917007f25028239cbddfe8))
* 修改uniapp获取系统信息时间 ([a1ecef1](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/a1ecef1a5b411ec9afdf5a1497ad3db2b7556ec4))
* 优化 ([cefc63d](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/cefc63dd12434c6939e02194c227f8e143aae97b))
* 优化 ([445dde9](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/445dde92f19f01f7f8561fe7342c9034a91a7398))
* 优化 ([182c6b5](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/182c6b5f1ec9bcbcd9a7f1647b6944eaec83a4d8))
* 优化 ([1454708](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/1454708fcea7bb3f5da122ac992c8887c510f99b))
* 优化 ([44b9e93](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/44b9e93b933a11fa38a89c28a10dcfa5d34811b8))
* 优化 ([98e0e1c](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/98e0e1cf83351b8d6f0dd87934c9a6cbd77615ac))
* 优化 ([21d041f](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/21d041f1d95dbc2900b96ff7c2286f3ae6cc3a77))
* 优化代码 ([1c5094b](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/1c5094b46d93466e95ad98994e5f8e3e4f13a385))
* 优化单页面页面关闭上报 ([a407f44](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/a407f44d1a111f9b1a22bca71864a2491a6d80d4))
* 优化浏览器端埋点 ([9f20946](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/9f209465a5956c0c46cd0c06c1349963c018af7e))
* 优化uniapp终端判断 ([467b8fc](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/467b8fcebd3f75ad08c86bab511ecb844bf09305))
* 支付宝、uni-app上报坑位链路以及路由栈 ([0978e30](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/0978e30c2e4f84a56d5f9ef2338d2a6a3d055a08))
* 支付宝端增加曝光自定义事件名 ([06de0a4](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/06de0a498248c298ba4e8bd90a19297c4a853686))
* 支付宝小程序上报支付宝版本号 ([f4495ce](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/f4495ce45be08fc1e3995de74f45cdbf5454e7ee))
* download channel ([0603d0e](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/0603d0e41f14e1a69a8a68020c300480aed853e2))
* uni-app埋点兼容支付宝小程序 ([5919d5c](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/5919d5ccb1e0056986f510678d55f2530806e40d))
* uni-app新增宿主版本号字段 ([baa3d81](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/baa3d811354aafe0bdeb05972218c89021793b2c))
* uniapp新增下载渠道标识 ([9f46280](https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk/commits/9f46280659258da5e63171e5069ca8d28bc0cf49))



