import SubjectListenerImpl from "@/runtime-core/subject-listener-impl";
import AlipayMiniSensorsParaImpl from "../alipay-mini-sensors-para-impl";

/**
 * 页面和组件onLoad监听器 支付宝小程序
 */
export default class AlipayMiniAllLoadListener extends SubjectListenerImpl<AllLoadObserver> implements AllLoadListener, AlipayMiniSysComponentOvserver {
    onPage(page: any) {
        this.listenerOnLoad(page);
    }
    onComponent(component: any) {
        this.listenerOnLoad(component);
    }
    init(data: AlipayMiniSensorsParaImpl) {
        data.sysPageListener.register(this);
        data.sysComponentListener.register(this);
    }
    /**
     * 监听Page onLoad方法
     */
    listenerOnLoad(page: any) {
        const oldOnLoad = page.onLoad || function () {};
        const that = this;
        page.onLoad = function (options: any) {
            try {
                that.notify(options);
            } catch(err) {
                console.warn('AlipayMiniPageLoadListener -> listenerPageOnLoad', err);
            }
            oldOnLoad.call(this, ...arguments);
        }
    }
    notify(options: any) {
        this.observers.forEach(observer => observer.onLoad(options));
    }
}