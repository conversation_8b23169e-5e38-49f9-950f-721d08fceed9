import SubjectListenerImpl from "@/runtime-core/subject-listener-impl";
import AlipayMiniSensorsParaImpl from "../alipay-mini-sensors-para-impl";

/**
 * 曝光监听器 支付宝小程序
 */
export default class AlipayMiniExposureListener extends SubjectListenerImpl<ExposureObserver> implements ExposureListener, AlipayMiniSysPageObserver, AlipayMiniSysComponentOvserver {
    init(data: AlipayMiniSensorsParaImpl) {
        data.sysPageListener.register(this);
        data.sysComponentListener.register(this);
    }
    onPage(page: any) {
        this.listenerPageExposure(page);
    }
    onComponent(component: any) {
        this.listenerComponentExposure(component);
    }
    /**
     * 监听页面组件曝光
     */
    listenerPageExposure(page: any) {
        const oldOnFirstAppear = page.onFirstAppear || function () {};
        const that = this;
        page.onFirstAppear = function (e: any) {
            try {
                that.notify(e);
            } catch(err: any) {
                console.warn("AlipayMiniExposureListener -> listenerPageExposure", err);
            }
            oldOnFirstAppear.call(this, ...arguments);
        };
    }
    /**
     * 监听组件曝光
     */
    listenerComponentExposure(component: any) {
        const methods = component.methods || {};
        const oldOnFirstAppear = methods.onFirstAppear || function () {};
        const that = this;
        methods.onFirstAppear = function (e: any) {
            try {
                that.notify(e);
            } catch(err: any) {
                console.warn("AlipayMiniExposureListener -> listenerComponentExposure", err);
            }
            oldOnFirstAppear.call(this, ...arguments);
        };
        component.methods = methods;
    }
    notify(e: any): void {
        this.observers.forEach(observer => observer.onExposure(e));
    }
}