import SubjectListenerImpl from "@/runtime-core/subject-listener-impl";
import UniappSensorsParaImpl from "../uniapp-sensors-para-impl";

/**
 * Uniapp Vue 组件监听器
 */
export default class UniappSysVueComponentListener extends SubjectListenerImpl<UniappSysVueComponentObserver> implements UniappSysAppObserver {
    init(data: UniappSensorsParaImpl) {
        data.sysAppListener.register(this);
    }
    /**
     * 监听app触发
     * @param app vue 对象
     */
    onApp(app: any) {
        this.listenerAppPlusSysComponent(app);
    }
    /**
     * 监听系统组件
     */
    listenerAppPlusSysComponent(app: any) {
        if (app._context && app._context.emitsCache) {
            const that = this;
            const oldSet = app._context.emitsCache.set;
            app._context.emitsCache.set = function (component: any) {
                try {
                    that.notify(component);
                } catch(err) {
                    console.warn("UniappSysComponentListener -> listenerAppPlusSysComponent", err);
                }
                return oldSet.call(app._context.emitsCache, ...arguments);
            }
        }
    }
    notify(component: any) {
        this.observers.forEach(observer => observer.onVueComponent(component));
    }
}