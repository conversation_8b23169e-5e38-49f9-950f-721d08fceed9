import SubjectListenerImpl from "@/runtime-core/subject-listener-impl";
import AlipayMiniSensorsParaImpl from "../alipay-mini-sensors-para-impl";

/**
 * 支付宝小程序 拖动监听器
 */
export default class AlipayMiniTouchListener extends SubjectListenerImpl<TouchObserver> implements TouchListener, AlipayMiniSysPageObserver, AlipayMiniSysComponentOvserver {
    init(data: AlipayMiniSensorsParaImpl): void {
        data.sysPageListener.register(this);
        data.sysComponentListener.register(this);
    }
    onPage(page: any) {
        this.listenerPageOnTouch(page);
    }
    onComponent(component: object): void {
            this.listenerComponentOnTouch(component);
    }
    listenerPageOnTouch(page: any) {
        const oldOnTouch = page.onTouch || function() {};
        const that = this;
        page.onTouch = function (e: any) {
            try {
                that.notify(e);
            } catch(err) {
                console.warn("AlipayTouchListener -> listenerOnTouch", err);
            }
            oldOnTouch.call(this, ...arguments);
        }
    }
    listenerComponentOnTouch(component: any) {
        const methods = component.methods || {};
        const oldOnTouch = methods.onTouch || function () {};
        const that = this;
        methods.onTouch = function (e: any) {
            try {
                that.notify(e);
            } catch(err) {
                console.warn("AlipayTouchListener -> listenerOnTouch", err);
            }
            oldOnTouch.call(this, ...arguments);
        }
    }
    notify(e: any): void {
        this.observers.forEach(observer => observer.onTouch(e));
    }
}