/**
 * 请求处理器
 */
 interface RequestHandler {
    serverUrl: string;
    needCookies?: boolean;
    dataSendTimeout: number;  // 上报请求超时时间
    extraRequestHeaders: Record<string, any>; // 额外请求头
    send(data: PointData[], isLast?: boolean): Promise<any> | void;
    sendSing(data: PointData): Promise<any> | void;
    sendRequest(url: string, data: object): Promise<any> | void;
    getUniversalId(url: string, data: {
        channel_user_id: string;
        channel_type: string;
        user_id?:number;
    }): Promise<any>;
    addExtraRequestHeaders(headers: Record<string, any>): void;
}

/**
 * 系统信息获取器
 */
 interface SystemInfoGetter {
    init(data: SensorsPara): void;
    getTerminial(): TerminalEnumType;
    getViewportWidth(): number;
    getViewportHeight(): number;
    getScreenHeight(ele?: Element): number;
    getScrollTop(ele?: Element): number;
    getUrl(): string;
    getUrlPath(): string;
    getTitle(...args: any): string;
    getReferrer(): string;
    getLatestTrafficSourceType(): string;
    getLatestReferrer(): string;
    getReportIncident(e: Event, funcName?: string): ReportIncident | void;
    generateRandomId(): string
    getPageDurationTime(): number;
    getLaunchQuery(): Record<string, string | number | boolean>;
    getOnLoadQuery(): Record<string, string | number | boolean>;
    getTimestamp(): number;
    getLaunchTime(): number;
    getIsCollected?: () => 0 | 1;
}

/**
 * 缓存处理器
 */
 interface StorageHandler {
    get<T>(key: string): T | undefined;
    set(key: string, value: string | object): void;
    remove(key: string): void;
}

// md5
declare module 'md5-es';
declare module 'sha256-es';

// 重载方法参数
interface OverloadParams {
    options: any; // 方法参数
    result: any; // 方法结果
}

/**
 * 邀请信息
 * dc: data center
 */
interface InviteInfo {
    dc_st: number; // 分享时间 st: timestamp
    dc_did: string; // 分享人的匿名id did: distinct_id
    dc_uid?: string; // 分享人的用户id uid: user_id
    dc_acp?: string; // 分享的活动id acp: active page
}