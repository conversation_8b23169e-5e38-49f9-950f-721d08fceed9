<template>
    <div class="page-click">
        <div class="group" data-sensors="水果">
            <div data-sensors="选中苹果">苹果</div>
            <div data-sensors="选中梨">梨</div>
            <div data-sensors="选中火龙果">火龙果</div>
            <div data-sensors="选中香蕉">香蕉</div>
            <div data-sensors="选中番茄">番茄</div>
        </div>
        <div class="group" data-sensors="动物">
            <div class="cat" data-sensors="选中猫">猫</div>
            <div data-sensors="选中狗">狗</div>
            <div data-sensors="选中袋鼠">袋鼠</div>
            <div data-sensors="选中熊猫">熊猫</div>
            <div data-sensors="选中鸭嘴兽">鸭嘴兽</div>
        </div>
        <router-link :to="{name: 'Routing'}">返回路由页</router-link>
    </div>
</template>

<script lang="ts">
</script>

<style scoped lang="scss">
.page-click {
    display: flex;
    justify-content: center;
    margin-top: 10%;
}

.group > div {
    cursor: pointer;
    color: #1890ff;
    text-decoration: underline;
}
.group > div + div {
    margin-top: 10px;
}
.group + .group {
    margin-left: 30px;
}
</style>