import SubjectListenerImpl from "@/runtime-core/subject-listener-impl";
import UniappSensorsParaImpl from "../uniapp-sensors-para-impl";

/**
 * 页面滚动监听器 uniapp
 */
export default class UniappScrollListener extends SubjectListenerImpl<UniappScrollObserver> implements UniappSysPageObserver {
    init(data: UniappSensorsParaImpl) {
        data.sysPageListener.register(this);
    }
    onPage(page: any) {
        this.listenerOnPageScroll(page);
    }
    /**
     * 监听组件滑动
     */
     listenerOnPageScroll(page: any) {
        const oldPageScroll = page.onPageScroll || function () {};
        const that = this;
        page.onPageScroll = function (e: UniappScrollInfo) {
            try {
                that.notify(e);
            } catch(err) {
                console.warn("UniappSysVuePageListener -> listenerOnPageScroll", err);
            }
            return oldPageScroll.call(this, ...arguments);
        }
    }
    notify(e: UniappScrollInfo) {
        this.observers.forEach(observer => observer.onScrollChange(e))
    }
}