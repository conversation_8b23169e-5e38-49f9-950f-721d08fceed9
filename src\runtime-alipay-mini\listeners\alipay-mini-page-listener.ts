import SubjectListenerImpl from "@/runtime-core/subject-listener-impl";
import AlipayMiniSensorsParaImpl from "../alipay-mini-sensors-para-impl";

enum NotifyType {
    show,
    hide
}

/**
 * 页面跳转监听器 支付宝小程序端
 */
export default class AlipayMiniPageListener extends SubjectListenerImpl<PageObserver> 
    implements PageListener, AlipayMiniSysPageObserver, PageUnloadObserver
{
    isAlreadyHide = false;
    onPage(page: any) {
        this.listenerPageOnShow(page);
        this.listenerPageOnHide(page);
    }
    init(data: AlipayMiniSensorsParaImpl) {
        data.sysPageListener.register(this);
        data.pageUnloadListener.register(this);
    }
    /**
     * 监听Page onShow方法
     */
    listenerPageOnShow(page: any) {
        const oldOnShow = page.onShow || function () {};
        const that = this;
        page.onShow = function () {
            try {
                that.notify(NotifyType.show);
                that.isAlreadyHide = false;
            } catch(err) {
                console.warn('AlipayMiniPageListener -> listenerPageOnShow', err);
            }
            oldOnShow.call(this, ...arguments);
        }
    }
    /**
     * 页面隐藏
     */
    listenerPageOnHide(page: any) {
        const oldOnHide = page.onHide || function () {};
        const that = this;
        page.onHide = function () {
            try {
                if (!that.isAlreadyHide) {
                    that.notify(NotifyType.hide);
                    that.isAlreadyHide = true;
                }
            } catch(err) {
                console.warn('AlipayMiniPageListener -> listenerPageOnHide' + err);
            }
            oldOnHide.call(this, ...arguments);
        }
    }
    onPageUnload() {
        if (!this.isAlreadyHide) {
            this.notify(NotifyType.hide);
            this.isAlreadyHide = true;
        }
    }
    notify(type: NotifyType) {
        this.observers.forEach(observer => {
            type === NotifyType.show && observer.onBeforeShow && observer.onBeforeShow();
            type === NotifyType.hide && observer.onBeforeHide && observer.onBeforeHide();
        });
        this.observers.forEach(observer => {
            type === NotifyType.show && observer.onPageShow && observer.onPageShow();
            type === NotifyType.hide && observer.onPageHide && observer.onPageHide();
        });
        this.observers.forEach(observer => {
            type === NotifyType.show && observer.onAfterPageShow && observer.onAfterPageShow();
            type === NotifyType.hide && observer.onAfterPageHide && observer.onAfterPageHide();
        });
    }
}