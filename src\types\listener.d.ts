/**
 * 观察者模式 主题
 */
interface SubjectListener {
  register(observer: Observer): void;
  remove(oberser: Observer): void;
  notify(...args: any): void;
}

/**
 * 观察者
 */
interface Observer {}

/**
 * 页面跳转监听器
 */
interface PageListener extends SubjectListener {
  observers: PageObserver[];
  init(data: SensorsPara): void;
}

/**
 * 页面跳转订阅者
 */
interface PageObserver extends Observer {
  onBeforeShow?: () => void;
  onPageShow?: () => void;
  onAfterPageShow?: () => void;
  onBeforeHide?: () => void;
  onPageHide?: () => void;
  onAfterPageHide?: () => void;
}

/**
 * 页面和组件加载监听器
 */
interface AllLoadListener extends SubjectListener {
  observers: AllLoadObserver[];
  init(data: SensorsPara): void;
}

/**
 * 页面和组件加载订阅者
 */
interface AllLoadObserver extends Observer {
  onAllLoad(options: any): void;
}

/**
 * 页面加载监听器
 */
interface PageLoadListener extends SubjectListener {
  observers: PageLoadObserver[];
  init(data: SensorsPara): void;
}

/**
 * 页面加载订阅者
 */
interface PageLoadObserver extends Observer {
  onBeforePageLoad?: (options: any) => void;
  onPageLoad?: (options: any) => void;
}

/**
 * 页面卸载监听器
 */
interface PageUnloadListener extends SubjectListener {
  observers: Observer[];
  init(data: SensorsPara): void;
}

/**
 * 页面卸载订阅者
 */
interface PageUnloadObserver extends Observer {
  onPageUnload?: () => void;
  onAfterPageUnload?: () => void;
}

/**
 * 页面点击监听器
 */
interface ClickListener extends SubjectListener {
  observers: ClickObserver[];
  init(data: SensorsPara): void;
}

/**
 * 页面点击订阅者
 */
interface ClickObserver extends Observer {
  onClickChange(e: MouseEvent, funcName?: string): void;
}

/**
 * 页面停留监听器
 */
interface StayListener extends SubjectListener {
  observers: StayObserver[];
  init(data: SensorsPara): void;
  resetInit(): void; // 恢复初始化
}

/**
 * 页面停留订阅者
 */
interface StayObserver extends Observer {
  onStayChange(ele?: Element): void;
}

/**
 * 程序销毁监听器
 */
interface UnloadListener extends SubjectListener {
  observers: UnloadObserver[];
  init(data: SensorsPara): void;
}

/**
 * 程序销毁订阅者
 */
interface UnloadObserver extends Observer {
  onUnload(): void;
}

/**
 * 程序启动监听器
 */
interface LaunchListener extends SubjectListener {
  observers: LaunchObserver[];
  init(data: SensorsPara): void;
}

/**
 * 程序启动订阅者
 */
interface LaunchObserver extends Observer {
  onBeforeLaunch?: (options: any) => void;
  onLaunch?: (options: any) => void;
}

/**
 * 分享监听器
 */
interface ShareListener extends SubjectListener {
  observers: ShareObserver[];
  init(data: SensorsPara): void;
}

/**
 * 分享订阅者
 */
interface ShareObserver extends Observer {
  onShare(options: OverloadParams): void;
}

/**
 * 曝光监听器
 */
interface ExposureListener extends SubjectListener {
  observers: ExposureObserver[];
  init(data: SensorsPara): void;
}

/**
 * 曝光订阅者
 */
interface ExposureObserver extends Observer {
  onExposure(e: any): void;
}

/**
 * 显现监听器
 */
interface AppearListener extends SubjectListener {
  observers: AppearObserver[];
  init(data: SensorsPara): void;
}

/**
 * 显现订阅者
 */
interface AppearObserver extends Observer {
  onAppear(e: any): void;
}

/**
 * 拖动监听器
 */
interface TouchListener extends SubjectListener {
  observers: TouchObserver[];
  init(data: SensorsPara): void;
}

/**
 * 拖动订阅者
 */
interface TouchObserver extends Observer {
  onTouch(e: any): void;
}

/**
 * 来源监听者
 */
interface SourceListener extends SubjectListener {
  observers: SourceObserver[];
  init(data: SensorsPara): void;
}

/**
 * 来源参数
 */
type SourceArg = {
  id: string;
  latestTrafficSourceType: string;
  latestReferrer: string;
};

/**
 * 来源订阅者
 */
interface SourceObserver extends Observer {
  onSource(arg: SourceArg): void;
}

interface TabItemListener extends SubjectListener {
  observers: TabItemObserver[];
  init(data: SensorsPara): void;
}
interface TabItemObserver extends Observer {
  onTabItemClick(pagePath: string): void;
}

/**
 * 截图监听器
 */
interface CaptureScreenListener extends SubjectListener {
  observers: CaptureObserver[];
  init(data: SensorsPara): void;
  notifyFunc: (e: any) => void;
}

/**
 * 截图订阅者
 */
interface CaptureObserver extends Observer {
  onCaptureScreen(e: any): void;
}
