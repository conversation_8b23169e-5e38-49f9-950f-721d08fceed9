/**
 * 默认传感器
 */
export default class DefaultSensors implements Sensors {
    // 配置数据
    para: SensorsPara;

    constructor() {
        this.para = null as any;
    }

    /**
     * 提交事件 
     */
    track(data: ReportIncident) {

    };

    /**
     * 初始化
     */
    init(data: InitData) {

    };

    /**
     * 登录
     */
    login(userId: string) {

    };

    /**
     * 自定义匿名 ID
     * 默认情况下，SDK 会生成匿名 ID 并可以保证该 ID 的唯一性，如果需要替换神策默认分配的匿名 ID ，可以在配置初始化 SDK 参数之后（init 接口调用之前）立即调用 identify 方法进行替换。
     */
    identify(id: string) {

    };

    /**
     * 设置公共属性
     */
    appendCommProperties(data: any) {

    };

    /**
     * 替换公共属性
     */
    replaceCommProperties(data: any, group: string) {

    }

    /**
     * 删除公共属性
     */
    removeCommProperties(...args: string[]) {

    };

    /**
     * 删除公共属性组
     */
    removeCommPropertiesGroup(...args: string[]) {

    }

    /**
     * 设置页面属性
     */
    appendPageProperties(data: any) {

    };

    /**
     * 删除页面属性
     */
    removePageProperties(...args: string[]) {

    };

    /**
     * 附加公共属性到页面
     */
    appendCommPropertiesToPage(...args: string[]): void {
        
    }

    /**
     * 附加生命周期属性
     */
    appendCycleProperties(data: Record<string, any>): void {
        
    }

    /**
     * 删除生命周期属性
     */
    removeCycleProperties(...args: string[]) {

    }

    /**
     * 获取生命周期属性
     */
    getCycleProperties(): Record<string, any> {
        return {}
    }
}