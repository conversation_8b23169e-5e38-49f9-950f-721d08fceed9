import { fileURLToPath } from "url";
import path from "path";
import TerserPlugin from "terser-webpack-plugin";
import CopyDistBundlePlugin from "../plugins/copy-dist-bundle.js";
import webpack from "webpack";

const { DefinePlugin } = webpack;

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
function resolve(dir) {
  return path.join(__dirname, "../" + dir);
}

export default {
  mode: "production",
  entry: resolve("src/main-browser.ts"),
  output: {
    path: resolve("dist/web"),
    filename: "data.analysis.sdk.min.umd.js",
    library: {
      name: "hoo",
      type: "umd",
      export: "default", // 添加这个解决es6的export default导出属性要加default才能访问到问题
    },
    globalObject: "this", //添加这个才能支持node的commonjs
  },
  module: {
    rules: [
      {
        test: /\.tsx?$/,
        loader: "babel-loader",
        options: {
          presets: [
            "@babel/preset-env",
            [
              "@babel/preset-typescript", // 引用Typescript插件
              {
                allExtensions: true, // ?支持所有文件扩展名
              },
            ],
          ],
        },
      },
    ],
  },
  resolve: {
    extensions: [".ts", ".js"],
    alias: {
      "@": resolve("src"),
    },
  },
  optimization: {
    minimize: true,
    minimizer: [
      new TerserPlugin({
        exclude: /\.(t|j)s$/,
      }),
    ],
  },
  performance: {
    hints: false,
  },
  plugins: [
    new CopyDistBundlePlugin(),
    new DefinePlugin({
      "process.env.LIB_ENV": JSON.stringify(process.env.LIB_ENV),
    }),
  ],
};
