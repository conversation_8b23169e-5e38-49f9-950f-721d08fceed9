/*
 * @Author: itnewdata <EMAIL>
 * @Date: 2022-02-16 22:25:44
 * @LastEditors: itnewdata <EMAIL>
 * @LastEditTime: 2023-03-09 21:04:55
 * @FilePath: \data_analysis_sdk\src\runtime-browser\js-sensors-para-impl.ts
 * @Description: 传感器配置
 */
import SensorsParaImpl, { SensorsParaConstructorArgs } from "@/runtime-core/sensors-para-impl";
import JsSystemInfoGetter from "./js-system-info-getter";
import {  judgingEnvironment } from "@/utils";
import { libEnum } from "@/runtime-core/enum";
import UniappSysAppListener from "@/runtime-uniapp/listeners/uniapp-sys-app-listener";
import UniappSysVueComponentListener from "@/runtime-uniapp/listeners/uniapp-sys-vue-component-listener";
import UniappSysPageListener from "@/runtime-uniapp/listeners/uniapp-sys-page-listener";


interface AlipayMiniSensorsParaConstructorArgs extends SensorsParaConstructorArgs {
    suddenChangeListener: SuddenChangeListener;
    sysAppListener?:  UniappSysAppListener
    sysVueComponentListener?: UniappSysVueComponentListener
    sysPageListener?: UniappSysPageListener;
    tabItemListener?: TabItemListener
}

/**
 * 传感器配置数据 - 缓存数据 浏览器端
 */
class JsSensorsParaStore implements SensorsParaStore {
    distinct_id = ''; // 匿名ID
    open_id = '';
    session_id: string = ''; // 会话ID
    commProperties = {};  // 公共参数
    commGroupProperties = {};
    $latest_traffic_source_type?: string;
    $latest_referrer?: string; // 最近一次站外前向地址
}

const lib = judgingEnvironment();

/**
 * 传感器配置数据 浏览器端
 */
export default class JsSensorsParaImpl extends SensorsParaImpl {
    store = new JsSensorsParaStore();
    suddenChangeListener: SuddenChangeListener;
    sysAppListener?: UniappSysAppListener;
    sysVueComponentListener?: UniappSysVueComponentListener;
    sysPageListener?: UniappSysPageListener;
    autoTrack = {
        appLaunch: true,
        appHide: true,
        pageShow: true,
        pageLeave: true,
        pageClick: true,
        pageStay: true,
        pageShare: true,
        pageExposure: true,
        pageAppear: true
    };
    constructor(props: AlipayMiniSensorsParaConstructorArgs) {
        super(props);
        this.sysAppListener = props.sysAppListener;
        this.sysVueComponentListener = props.sysVueComponentListener;
        this.sysPageListener = props.sysPageListener
        this.tabItemListener = props.tabItemListener
        this.suddenChangeListener = props.suddenChangeListener;
    }
    init(data: InitData) {
        super.init(data);
        if(lib === libEnum.UNIAPP_H5) {
            this.sysVueComponentListener?.init(this as any);
            this.sysPageListener?.init(this as any)
            this.tabItemListener?.init(this as any)
        }
        const systemInfoGetter = this.systemInfoGetter as JsSystemInfoGetter;
        if (!this.store.$latest_traffic_source_type || systemInfoGetter.getReferrer().indexOf(systemInfoGetter.getHost()) === -1) {
            this.store.$latest_traffic_source_type = systemInfoGetter.getLatestTrafficSourceType();
            this.store.$latest_referrer = systemInfoGetter.getReferrer();
        }
        this.suddenChangeListener.init(this);
    }
    getTrackData(data: ReportIncidentInfo) {
        const pointData = super.getTrackData(data);
        const systemInfoGetter = this.systemInfoGetter as JsSystemInfoGetter;
        pointData.properties.$title = systemInfoGetter.getTitle(data.key),
        pointData.properties.$referrer = systemInfoGetter.getReferrer();
        pointData.properties.$url_path = systemInfoGetter.getUrlPath();
        pointData.properties.$user_agent = systemInfoGetter.getUserAgent();

        
        pointData.properties.page_list = systemInfoGetter.getPageList();
        pointData.properties.page_version = systemInfoGetter.getPageVersion()

        pointData.properties.position_list = systemInfoGetter.getPositionList();
        return super.formatTrackData(pointData);
    }
}