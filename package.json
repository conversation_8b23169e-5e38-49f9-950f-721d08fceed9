{"name": "@rrzu/data-analysis", "version": "1.3.52", "description": "人人租-埋点sdk", "main": "index.ts", "scripts": {"serve": "webpack-dev-server --config build/webpack.serve.js --mode development --devtool eval-source-map --progress", "build:umd": "cross-env NODE_ENV='production' LIB_ENV='web' webpack --config build/webpack.prod.umd.js", "build:es6": "cross-env NODE_ENV='production' LIB_ENV='web' webpack --config build/webpack.prod.es6.js", "build:alipay": "cross-env NODE_ENV='production' LIB_ENV='alipay' webpack --config build/webpack.prod.alipay.mini.js", "build:uniapp": "cross-env NODE_ENV='production' LIB_ENV='uniapp' webpack --config build/webpack.prod.uniapp.js", "build:umd-dev": "cross-env NODE_ENV='develop' LIB_ENV='web' webpack --config build/webpack.dev.umd.js", "build:es6-dev": "cross-env NODE_ENV='develop' LIB_ENV='web' webpack --config build/webpack.dev.es6.js", "build:alipay-dev": "cross-env NODE_ENV='develop' LIB_ENV='alipay' webpack --config build/webpack.dev.alipay.mini.js", "build:uniapp-dev": "cross-env NODE_ENV='develop' LIB_ENV='uniapp' webpack --config build/webpack.dev.uniapp.js", "copy:config": "node copy-package", "build:latest": "npm run build:umd && npm run build:es6 && npm run build:alipay && npm run build:uniapp", "build:beta": "npm run build:umd-dev && npm run build:es6-dev && npm run build:alipay-dev && npm run build:uniapp-dev", "publish:data-analysis:latest": "npm run build:latest && npm run copy:config && npm publish ./dist --tag=latest --no-git-checks --registry=http://fe.rrzu.com/npm", "publish:data-analysis:beta": "npm run build:latest && npm run copy:config && npm publish ./dist --tag=beta --no-git-checks --registry=http://fe.rrzu.com/npm", "changelog": "npx conventional-changelog -p angular -i CHANGELOG.md -s -r 0", "distConfig:init": "node create/dist-config", "release": "npm version patch && npm run changelog && git add CHANGELOG.md && git commit -m 'docs: update changelog'"}, "repository": {"type": "git", "url": "https://codeup.aliyun.com/5f11945adf9df74e36afaa16/rrzuji/data_analysis_sdk"}, "keywords": ["埋点sdk"], "author": "liqiakun", "license": "ISC", "devDependencies": {"@babel/core": "7.16.0", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/polyfill": "^7.12.1", "@babel/preset-env": "7.16.4", "@babel/preset-typescript": "7.16.0", "@types/fingerprintjs2": "^2.0.0", "babel-loader": "8.2.3", "conventional-changelog-cli": "^5.0.0", "cross-env": "^7.0.3", "css-loader": "^6.5.1", "fs-extra": "^11.2.0", "html-webpack-plugin": "^5.5.0", "postcss-loader": "6.2.1", "sass": "^1.44.0", "sass-loader": "^12.3.0", "style-loader": "3.3.1", "style-resources-loader": "1.4.1", "terser-webpack-plugin": "5.3.0", "ts-loader": "9.2.5", "typescript": "^4.6.0", "vue-loader": "16.8.3", "vue-router": "^4.0.3", "vue-style-loader": "^4.1.3", "vue-template-compiler": "^2.6.14", "webpack": "5.64.4", "webpack-cli": "4.10.0", "webpack-dev-server": "4.6.0"}, "dependencies": {"core-js": "3.19.1", "fingerprintjs2": "2.1.4", "md5-es": "^1.8.2", "nanoid": "3.1.30", "sha256-es": "^1.8.2", "vue": "3.2.23"}, "type": "module"}