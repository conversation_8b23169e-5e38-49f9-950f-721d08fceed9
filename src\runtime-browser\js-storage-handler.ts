import { isObject } from "@/utils";

// 对象存储标识
const objectMark = "ThisDataIsTheObject|";

/**
 * 缓存处理器 浏览器
 */
export default class JsStorageHandler implements StorageHandler {
    get(key: string) {
        const result = window.localStorage.getItem(key);
        if (!result) return result;
        return new RegExp('^'+objectMark).test(result) ? JSON.parse(result.slice(objectMark.length)) : result;
    };
    set(key: string, value: string | object) {
        if (isObject(value) || Array.isArray(value)) value = objectMark + JSON.stringify(value);
        window.localStorage.setItem(key, value as string);
    };
    remove(key: string) {
        window.localStorage.removeItem(key);
    }
}