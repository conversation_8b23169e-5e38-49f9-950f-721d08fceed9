<template>
    <div class="pageview-a">
        我是页面A - 请检测是否有pageview事件上报
        <router-link :to="{name: 'PageViewB'}">B页面</router-link>
        <router-link :to="{name: 'Routing'}">返回路由页</router-link>
    </div>
</template>

<script lang="ts">
import { defineComponent } from "vue";

export default defineComponent({
    name: 'Page-A'
});
</script>

<style scoped lang="scss">
    .pageview-a {
        display: flex;
        flex-direction: column;
    }
</style>