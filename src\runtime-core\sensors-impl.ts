import { debounce, isObject, deepClone, judgingEnvironment } from "@/utils";
import { useUtils } from "@/use-utils/index";
import { eventEnum, libEnum, terminalEnum } from "./enum";
import UniappSysAppListener from "@/runtime-uniapp/listeners/uniapp-sys-app-listener";
import UniappSysVueComponentListener from "@/runtime-uniapp/listeners/uniapp-sys-vue-component-listener";
type sendType = () => Promise<any>;

/**
 * 黑名单 该匿名id不上报
 */
const blacklist: Map<string, boolean> = new Map([
  ["2088532636361975", true],
  ["2088342505212574", true],
]);

const lib = judgingEnvironment();

/**
 * 传感器构造函数入参
 */
export interface SensorsImplConstructorArgs {
  para: SensorsPara;
  dataPoll: DataPoll;
  requestHandler: RequestHandler;
  pageListener: PageListener;
  clickListener: ClickListener;
  stayListener: StayListener;
  systemInfoGetter: SystemInfoGetter;
  pageLoadListener: PageLoadListener;
  launchListener: LaunchListener;
  unloadListener: UnloadListener;
  exposureListener: ExposureListener;
  appearListener: AppearListener;
  shareListener: ShareListener;
  touchListener: TouchListener;
  sourceListener?: SourceListener;
  tabItemListener?: TabItemListener;
  sysAppListener?: UniappSysAppListener;
  sysVueComponentListener?: UniappSysVueComponentListener;
  captureScreenListener?: CaptureScreenListener;
}

/**
 * 传感器实现
 */
export default class SensorsImpl
  implements
    Sensors,
    PageObserver,
    ClickObserver,
    StayObserver,
    LaunchObserver,
    UnloadObserver,
    ShareObserver,
    SourceObserver,
    ExposureObserver,
    AppearObserver,
    TouchObserver
{
  para: SensorsPara;
  sysAppListener?: UniappSysAppListener;
  protected dataPoll: DataPoll;
  protected requestHandler: RequestHandler;
  protected stayListener: StayListener;
  protected systemInfoGetter: SystemInfoGetter;
  useUtils: typeof useUtils = useUtils;
  constructor(args: SensorsImplConstructorArgs) {
    this.para = args.para;
    this.dataPoll = args.dataPoll;
    this.sysAppListener = args.sysAppListener;
    this.requestHandler = args.requestHandler;
    args.pageLoadListener.register(this);
    args.pageListener.register(this);
    args.clickListener.register(this);
    args.stayListener.register(this);
    this.stayListener = args.stayListener;
    this.systemInfoGetter = args.systemInfoGetter;
    args.launchListener.register(this);
    args.unloadListener.register(this);
    args.exposureListener.register(this);
    args.appearListener.register(this);
    args.shareListener.register(this);
    args.touchListener.register(this);
    args.sourceListener && args.sourceListener.register(this);
    args.tabItemListener && args.tabItemListener.register(this);
    args.captureScreenListener?.register(this);
    this.send = debounce(this.sendPointData, this.para.batchSendTimeout, this);
  }

  install(app: any) {
    if (lib === libEnum.UNIAPP_H5) {
      this.sysAppListener?.notify(app);
    }
  }

  init(data: InitData) {
    try {
      this.para.init(data);
      this.send = debounce(this.sendPointData, this.para.batchSendTimeout, this);
    } catch (err) {
      console.warn(err);
    }
  }
  protected send?: sendType;
  // 附加上报事件信息到数据池
  protected appendPointData(data: ReportIncidentInfo): PointData | undefined {
    const pointData = this.para.getTrackData(data);
    // 兼容抽屉下单不经过affirm页面无法归因订单
    const {
      event_name,
      properties: {
        page_list,
        position_list,
        index_module_key_list,
        module_position_sort_list,
      },
    } = pointData;
    if (event_name === "$ConfirmOrder") {
       const LIB_ENV = process.env.LIB_ENV;
      if(LIB_ENV === "alipay"&&!page_list?.includes("pages/order/affirm/affirm")){
        page_list?.push("pages/order/affirm/affirm");
        position_list?.push("");
        index_module_key_list?.push("");
        module_position_sort_list?.push("");
      }else if(LIB_ENV === "uniapp"&&!page_list?.includes("pages/order-affirm/order-affirm")){
        page_list?.push("pages/order-affirm/order-affirm");
        position_list?.push("");
        index_module_key_list?.push("");
        module_position_sort_list?.push("");
      }
    }
    // 黑名单直接返回，不进数据池
    if (blacklist.get(pointData.distinct_id)) return;
    this.dataPoll.append(pointData);
    return pointData;
  }
  // 上报事件 转换为 上报信息数组
  protected getReportIncidentInfo(data: ReportIncident) {
    if (typeof data.key === "string") return [data];
    const list = [];
    for (const key of data.key) {
      const reportIncidentInfo = deepClone(data);
      reportIncidentInfo.key = key;
      list.push(reportIncidentInfo);
    }
    return list;
  }
  /**
   * 上报事件
   * @param data 事件数据
   * @param isSend 是否需要发送
   * @param isDebounce 是否需要等待批量发送
   * @returns 该次上报的列表
   */
  track(data: ReportIncident, isSend: boolean = true, isDebounce: boolean = true): PointData[] {
    return this.upload(data, {
      isSend,
      isDebounce,
    });
  }
  /**
   * 上报数据
   * @param data 上报数据
   * @returns
   */
  upload(
    data: ReportIncident,
    option: {
      isSend?: boolean;
      isDebounce?: boolean;
      isLast?: boolean;
    }
  ): PointData[] {
    const list: PointData[] = [];
    try {
      for (const reportIncidentInfo of this.getReportIncidentInfo(data)) {
        const pointData = this.appendPointData(reportIncidentInfo);
        pointData && list.push(pointData);
      }
      option.isSend && (option.isDebounce ? this.send && this.send() : this.sendPointData({ isLast: option.isLast }));
    } catch (err) {
      console.warn("SensorsImpl -> track", err);
    }
    return list;
  }
  /**
   * 上报数据-防抖
   */
  _debounceUpload = debounce(this.upload, 500, this);
  /**
   * 上报数据
   * @param islone 是否需要深克隆一份数据上报
   * @param isLast 是否程序关闭之前的上报
   */
  sendPointData(
    option: {
      isClone?: boolean;
      isLast?: boolean;
    } = { isClone: false, isLast: false }
  ) {
    // 如果绑定用户接口没有，则需要有用户id之后才开始上报数据
    if (!this.para.bindServerUrl && !this.para.store.user_id) {
      return;
    }

    const terminal = this.systemInfoGetter.getTerminial();
    const { distinct_id } = this.para.store;

    if (terminal === terminalEnum.WX_MINI && !distinct_id.trim()) {
      return;
    }

    const alipayTerminalMap = [
      terminalEnum.ALIPAY_TP_VIVO,
      terminalEnum.ALIPAY_HONOR,
      terminalEnum.ALIPAY_HUAWEI,
      terminalEnum.ALIPAY_XIAOMI,
      terminalEnum.ALIPAY_OPPO,
    ];
    if (alipayTerminalMap.includes(terminal) && !distinct_id) {
      return;
    }

    // 批量上报
    let { keys, list } = this.dataPoll.getList();
    if (list.length === 0) return;
    if (option.isClone) list = deepClone(list); // 深克隆可以表面对象修改
    list.forEach(item => {
      if (!item.distinct_id.trim()) {
        item.distinct_id = distinct_id;
      }
    });
    const result = this.requestHandler.send(list, option.isLast);
    result &&
      result
        .then((res: any) => {
          this.para.showLog && console.log("上报数据", list);
          this.dataPoll.remove(keys);
        })
        .catch((err: any) => {
          this.dataPoll.reset(keys);
          this.para.showLog && console.log("上报失败", err);
        });
  }
  onPageLoad() {
    try {
      const options = this.systemInfoGetter.getOnLoadQuery();
      if (options.dc_st && (options.dc_uid || options.dc_did)) {
        const pointData = this.appendPointData({
          eventType: eventEnum.PAGE_INVITE,
          key: eventEnum.PAGE_INVITE,
        });
        //页面回流接收后多发一个事件方便统计邀请人邀请的数量
        const invitePointData = this.appendPointData({
          key: "$PageInviteReceive",
        });
        if (invitePointData) {
          invitePointData.properties = {
            ...invitePointData.properties,
            dynamic_int_key_1: "page_invite_origin_user_id",
            dynamic_int_value_1: invitePointData.user_id,
          };
          invitePointData.user_id = Number(options.dc_uid);
        }
        pointData && (pointData.event_millitime = this.systemInfoGetter.getLaunchTime());
        this.send && this.send();
      }
    } catch (err) {
      console.warn(err);
    }
  }
  /**
   * 页面显示
   */
  onPageShow() {
    try {
      if (this.para.autoTrack.pageShow) {
        this.track({
          eventType: eventEnum.PAGE_VIEW,
          key: eventEnum.PAGE_VIEW,
        });
      }
      // 切换页面，初始化停留事件
      this.para.autoTrack.pageStay && this.stayListener.resetInit();
    } catch (err) {
      console.warn(err);
    }
  }
  /**
   * 页面隐藏
   */
  onPageHide() {
    try {
      if (this.para.autoTrack.pageLeave)
        this.track({
          eventType: eventEnum.PAGE_LEAVE,
          key: "$PageLeave",
        });
    } catch (err) {
      console.warn(err);
    }
  }
  /**
   * 页面点击
   */
  onClickChange(e: any, funcName?: string) {
    try {
      const reportIncident = this.systemInfoGetter.getReportIncident(e, funcName);
      if (reportIncident && this.para.autoTrack.pageClick)
        this.track(reportIncident, reportIncident.key !== eventEnum.PAGE_CLICK);
    } catch (err) {
      console.warn(err);
    }
  }
  onTabItemClick(pagePath: string) {
    const {
      autoTrack: { pageClick },
      tabBarPositionMap,
    } = this.para;
    if (pageClick && tabBarPositionMap && tabBarPositionMap[pagePath]) {
      setTimeout(() => {
        this.track({
          key: "$TabItemClick",
          eventType: eventEnum.PAGE_CLICK,
          customProperties: {
            positionSign: tabBarPositionMap[pagePath],
            positionSignId: tabBarPositionMap[pagePath],
          },
        });
      }, 0);
    }
  }
  /**
   * 页面停留
   */
  onStayChange(ele?: Element) {
    try {
      if (this.para.autoTrack.pageStay) {
        const event: any = {
          eventType: eventEnum.PAGE_STAY,
          key: eventEnum.PAGE_STAY,
        };
        if (ele)
          event.customProperties = {
            $screen_height: this.systemInfoGetter.getScreenHeight(ele),
            $screen_top: this.systemInfoGetter.getScrollTop(ele),
          };
        this.track(event, false);
      }
    } catch (err) {
      console.warn(err);
    }
  }
  /**
   * 程序启动
   */
  onLaunch(options: Record<string,any>) {
    try {
      //上报事件
      this.para.autoTrack.appLaunch &&
        this.track({
          eventType: eventEnum.MP_LAUNCH,
          key: eventEnum.MP_LAUNCH,
          customProperties:{
            dynamicStrProps:{
              launchParams:JSON.stringify(options)
            }
          }
        });
      // 清理最近一次站外流量来源类型
      this.para.clearLatestTrafficSource();
    } catch (err) {
      console.warn(err);
    }
  }
  /**
   * 程序销毁
   */
  onUnload() {
    try {
      this.para.autoTrack.appHide &&
        this.upload(
          {
            eventType: eventEnum.MP_HIDE,
            key: eventEnum.MP_HIDE,
          },
          { isSend: true, isLast: true }
        );
    } catch (err) {
      console.warn(err);
    }
  }
  /**
   * 给页面分享添加私有携参
   */
  private _addParamsToPageShare(params: OverloadParams) {
    const { result } = params;
    if (!result.path) {
      result.path = this.systemInfoGetter.getUrl();
    }
    if (result.path.includes("dc_st")) return result;
    // 邀请参数
    const info: InviteInfo = {
      dc_st: Date.now(),
      dc_did: this.para.store.distinct_id,
    };
    if (this.para.store.user_id) info.dc_uid = this.para.store.user_id;
    const activityId = this.para.getCommProperties("activityId", "clue");
    if (activityId) info.dc_acp = activityId;
    const paramStr = Object.keys(info)
      .map((key) => `${key}=${(info as any)[key]}`)
      .join("&");
    if (!result.path) {
      const onLoadQuery = this.systemInfoGetter.getOnLoadQuery() || {};
      result.path = `${this.systemInfoGetter.getUrl()}?${Object.keys(onLoadQuery)
        .map((key) => `${key}=${onLoadQuery[key]}`)
        .join("&")}&${paramStr}`;
    } else if (result.path.indexOf("?") !== -1) {
      result.path += `&${paramStr}`;
    } else {
      result.path += `?${paramStr}`;
    }
    return result;
  }
  /**
   * 页面分享
   */
  onShare(params: OverloadParams) {
    try {
      this._addParamsToPageShare(params);
      if (this.para.autoTrack.pageShare) {
        const pointData = this.appendPointData({
          eventType: eventEnum.PAGE_SHARE,
          key: eventEnum.PAGE_SHARE,
        });
        if (pointData) {
          pointData.properties.$referrer = this.systemInfoGetter.getUrl();
          pointData.properties.$url = params.result.path;
        }
        this.sendPointData();
      }
    } catch (err) {
      console.warn(err);
    }
  }
  /**
   * 用户登录
   */
  async login(userId: string) {
    try {
      if (userId === undefined || userId === null || userId === "" || userId == "0")
        return console.warn("记录登录用户ID不能为空");
      this.para.store.is_login = true;
      const { union_id, open_id } = this.para.store;
      await this.dataPoll.updateUniversalId(union_id || open_id, userId);
      /**
       * 这里有可能传数值和字符串 特意用的==
       */
      if (userId == this.para.store.user_id) return;
      // 切换账号的时候先把缓存数据上报
      if (this.para.store.user_id) {
        this.sendPointData();
      }
      this.para.login(userId);
      this.dataPoll.modifyUserId(userId);
      this.track({
        eventType: eventEnum.LOGIN,
        key: eventEnum.LOGIN,
      });
      this.userBind();
    } catch (err) {
      console.warn(err);
    }
  }
  /**
   * 组件曝光
   */
  onExposure(e: any): void {
    try {
      const reportIncident = this.systemInfoGetter.getReportIncident(e);
      if (reportIncident && this.para.autoTrack.pageExposure) this.track(reportIncident, false);
    } catch (err) {
      console.warn(err);
    }
  }
  /**
   * 组件显现
   */
  onAppear(e: any): void {
    try {
      const reportIncident = this.systemInfoGetter.getReportIncident(e);
      if (reportIncident && this.para.autoTrack.pageAppear) this.track(reportIncident, false);
    } catch (err) {
      console.warn(err);
    }
  }
  /**
   * 组件拖动事件
   */
  onTouch(e: any): void {
    try {
      const reportIncident = this.systemInfoGetter.getReportIncident(e);
      if (reportIncident) this.track(reportIncident, false);
    } catch (err) {
      console.warn(err);
    }
  }
  /**
   * 用户截屏
   */
  onCaptureScreen(): void {
    try {
      this.track({ eventType: eventEnum.CAPTURESCREEN, key: eventEnum.CAPTURESCREEN });
    } catch (err) {
      console.warn(err);
    }
  }

  /**
   * 监听跳转来源
   */
  onSource(arg: SourceArg) {
    if (arg.id === undefined || arg.id === null || arg.id === "") return console.warn("来源匿名ID不能为空");
    if (
      arg.id === this.para.store.sourceDistinctId &&
      arg.latestTrafficSourceType === this.para.store.latestTrafficSourceType &&
      arg.latestReferrer === this.para.store.latestReferrer
    )
      return;
    try {
      this.para.identifySource(arg);
      if (this.para.store.user_id && arg.id !== this.para.store.sourceDistinctId) this.userResourceId();
      this.appendCommProperties({
        $source_terminal: arg.latestTrafficSourceType,
      });
    } catch (err) {
      console.warn(err);
    }
  }
  /**
   * 绑定用户id和来源匿名id
   */
  userResourceId() {
    try {
      if (!this.para.store.sourceDistinctId) return;
      // 绑定匿名id和用户id
      const data = {
        user_id: this.para.store.user_id,
        distinct_id: this.para.store.sourceDistinctId,
      };
      if (this.para.bindServerUrl) {
        const result = this.requestHandler.sendRequest(this.para.bindServerUrl, data);
        result &&
          result.then(() => {
            this.para.showLog && console.log("绑定用户id和来源匿名id", data);
          });
      }
    } catch (err) {
      console.warn(err);
    }
  }
  /**
   * 绑定用户id和匿名id
   */
  userBind() {
    try {
      // 绑定匿名id和用户id
      const data = {
        user_id: this.para.store.user_id,
        distinct_id: this.para.store.distinct_id,
      };
      if (this.para.bindServerUrl) {
        const result = this.requestHandler.sendRequest(this.para.bindServerUrl, data);
        result &&
          result.then(() => {
            this.para.showLog && console.log("绑定用户id和匿名id", data);
          });
        // 绑定来源id和用户id
        this.userResourceId();
      }
    } catch (err) {
      console.warn(err);
    }
  }
  /**
   * 设置匿名id
   */
  async identify(id: string) {
    try {
      if (id === undefined || id === null || id === "") return console.warn("自定义匿名ID不能为空");
      await this.dataPoll.updateUniversalId(id);
      if (id === this.para.store.distinct_id && id === this.para.store.open_id) return;
      this.para.identify(id);
      this.dataPoll.modifyDisctincId(id);
    } catch (err) {
      console.warn(err);
    }
  }

  /**
   * 设置匿名id unionid openid
   */
  async setIdentifyUnion(data: IdentifyUnionInfo) {
    const { unionid } = data;
    try {
      await this.dataPoll.updateUniversalId(unionid);
      if (this.para.store.distinct_id) return;
      if (unionid === undefined || unionid === null || unionid === "") return console.warn("unionid不能为空");
      if (unionid === this.para.store.distinct_id && unionid === this.para.store.union_id) return;
      try {
        this.para.identifyUnion(data);
        this.dataPoll.modifyDisctincUnionId(data);
      } catch (err) {
        console.warn(err);
      }
    } catch (err) {
      console.warn(err);
    }
  }

  addExtraRequestHeaders(headers: Record<string, any>) {
    try {
      if (!isObject(headers)) return console.error("额外请求头必须为对象");
      this.requestHandler.addExtraRequestHeaders(headers);
    } catch (err) {
      console.warn(err);
    }
  }

  /**
   * 附加公共属性
   */
  appendCommProperties(data: any, group?: string) {
    try {
      if (!isObject(data)) return console.warn("公共属性必须为对象");
      this.para.appendCommProperties(data, group);
      this.para.appendPageProperties(data);
    } catch (err) {
      console.warn(err);
    }
  }
  /**
   * 替换公共属性
   */
  replaceCommProperties(data: any, group: string) {
    try {
      if (!isObject(data)) return console.warn("公共属性必须为对象");
      this.para.replaceCommProperties(data, group);
      this.para.appendPageProperties(data);
    } catch (err) {
      console.warn(err);
    }
  }
  /**
   * 删除公共属性
   */
  removeCommProperties(...args: string[]) {
    try {
      this.para.removeCommProperties(...args);
    } catch (err) {
      console.warn(err);
    }
  }
  /**
   * 删除公共属性组
   */
  removeCommPropertiesGroup(...args: string[]) {
    try {
      this.para.removeCommPropertiesGroup(...args);
    } catch (err) {
      console.warn(err);
    }
  }
  /**
   * 附加页面属性
   */
  appendPageProperties(data: any) {
    try {
      if (!isObject(data)) return console.warn("附加公共属性必须为对象");
      this.para.appendPageProperties(data);
    } catch (err) {
      console.warn(err);
    }
  }
  /**
   * 删除页面属性
   */
  removePageProperties(...args: string[]) {
    try {
      this.para.removePageProperties(...args);
    } catch (err) {
      console.warn(err);
    }
  }
  /**
   * 获取公共属性
   */
  getCommProperties(key: string, group: string) {
    try {
      return this.para.getCommProperties(key, group);
    } catch (err) {
      console.warn(err);
    }
  }
  /**
   * 附加公共属性到页面
   */
  appendCommPropertiesToPage(...args: string[]) {
    try {
      this.para.appendCommPropertiesToPage(...args);
    } catch (err) {
      console.warn(err);
    }
  }
  /**
   * 附加生命周期属性
   */
  appendCycleProperties(data: Record<string, any>): void {
    this.para.appendCycleProperties(data);
  }
  /**
   * 删除生命周期属性
   */
  removeCycleProperties(...args: string[]) {
    try {
      this.para.removeCycleProperties(...args);
    } catch (err) {
      console.warn(err);
    }
  }
  /**
   * 获取生命周期属性
   */
  getCycleProperties() {
    return this.para.getCycleProperties();
  }
}
