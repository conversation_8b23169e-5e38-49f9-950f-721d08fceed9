import SubjectListenerImpl from "@/runtime-core/subject-listener-impl";
import AlipayMiniSensorsParaImpl from "../alipay-mini-sensors-para-impl";

/**
 * 分享监听器 小程序端
 */
export default class AlipayMiniShareListener extends SubjectListenerImpl<ShareObserver> implements ShareListener, AlipayMiniSysPageObserver {
    init(data: AlipayMiniSensorsParaImpl) {
        data.sysPageListener.register(this);
    }
    listenerOnShareAppMessage(page: any) {
        const oldOnShareAppMessage = page.onShareAppMessage || function () {};
        const that = this;
        page.onShareAppMessage = function (options: object) {
            const result = oldOnShareAppMessage.call(this, ...arguments) || {};
            try {
                that.notify({ options, result });
            } catch(err) {
                console.warn('AlipayMiniShareListener -> listenerOnShareAppMessage', err);
            }
            return result;
        }
    }
    onPage(page: any) {
        return this.listenerOnShareAppMessage(page);
    }
    notify(params: OverloadParams) {
        this.observers.forEach(observer => observer.onShare(params));
    }
}