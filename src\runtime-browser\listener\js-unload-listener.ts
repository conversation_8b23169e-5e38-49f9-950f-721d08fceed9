/*
 * @Author: itnewdata <EMAIL>
 * @Date: 2022-02-16 22:25:44
 * @LastEditors: itnewdata <EMAIL>
 * @LastEditTime: 2023-03-25 11:14:23
 * @FilePath: \data_analysis_sdk\src\runtime-browser\listener\js-unload-listener.ts
 * @Description: 程序销毁监听器 浏览器端
 */
import SubjectListenerImpl from "@/runtime-core/subject-listener-impl";

export default class JsUnloadListener extends SubjectListenerImpl<UnloadObserver> implements UnloadListener {
    init(data: SensorsPara) {
        if (data.isTrackSinglePage || data.isIFrame) {
            window.addEventListener('unload', () => {
                this.notify();
            });
        }
    }
    notify(): void {
        this.observers.forEach(observer => {
            observer.onUnload();
        });
    }
}