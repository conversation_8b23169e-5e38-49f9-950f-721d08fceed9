/*
 * @Author: itnewdata <EMAIL>
 * @Date: 2022-02-16 22:25:44
 * @LastEditors: itnewdata <EMAIL>
 * @LastEditTime: 2023-02-22 10:44:20
 * @FilePath: \data_analysis_sdk\src\runtime-browser\listener\js-click-listener.ts
 * @Description: 页面点击监听器 浏览器端
 */
import SubjectListenerImpl from "@/runtime-core/subject-listener-impl";
export default class JsClickListener extends SubjectListenerImpl<ClickObserver> implements ClickListener {
    init() {
        window.addEventListener('click', (e: MouseEvent) => {
            this.notify(e);
        }, true);
    }
    notify(e: MouseEvent) {
        this.observers.forEach(observer => observer.onClickChange(e));
    }
}