/*
 * @Author: itnewdata <EMAIL>
 * @Date: 2022-02-16 22:25:44
 * @LastEditors: itnewdata <EMAIL>
 * @LastEditTime: 2023-03-22 12:21:56
 * @FilePath: \data_analysis_sdk\src\main-browser.ts
 * @Description: 浏览器端输出
 */
import SensorsImpl from "./runtime-core/sensors-impl";
import JsDataPollImpl from "./runtime-browser/js-data-poll-impl";
import JsSensorsParaImpl from "./runtime-browser/js-sensors-para-impl";
import JsPageListener from "./runtime-browser/listener/js-page-listener";
import JsSystemInfoGetter from "./runtime-browser/js-system-info-getter";
import JsStorage from "./runtime-browser/js-storage-handler";
import JsRequestHandler from "./runtime-browser/js-request-handler";
import JsClickListener from "./runtime-browser/listener/js-click-listener";
import JsStayListener from "./runtime-browser/listener/js-stay-listener";
import JsUnloadListener from "./runtime-browser/listener/js-unload-listener";
import JsLaunchListener from "./runtime-browser/listener/js-launch-listener";
import JsExposureListener from "./runtime-browser/listener/js-exposure-listener"; 
import JsSuddenChangeListener from "./runtime-browser/listener/js-sudden-change-listener";
import JsPageLoadListener from "./runtime-browser/listener/js-page-load-listener";
import JsPageUnloadListener from "./runtime-browser/listener/js-page-unload-listener";
import JsShareListener from "./runtime-browser/listener/js-share-listener";
import JsTouchListener from "./runtime-browser/listener/js-touch-listener";
import JsAppearListener from "./runtime-browser/listener/js-appear-listener";
import DefaultSensors from "./runtime-default/default-sensors";
import { judgingEnvironment } from "./utils";
import { libEnum } from "./runtime-core/enum";

const lib = judgingEnvironment();

/**
 * 传感器
 */
let sensors: Sensors = new DefaultSensors();

// Web浏览器端
if (lib === libEnum.WEB) {
    try {
        if (!window.sensors) {
            const storageHandler = new JsStorage();
            const dataPoll = new JsDataPollImpl({ storageHandler });
            const requestHandler = new JsRequestHandler();
            const pageLoadListener = new JsPageLoadListener();
            const pageUnloadListener = new JsPageUnloadListener();
            const pageListener = new JsPageListener();
            const clickListener = new JsClickListener();
            const stayListener = new JsStayListener();
            const systemInfoGetter = new JsSystemInfoGetter();
            const unloadListener = new JsUnloadListener();
            const launchListener = new JsLaunchListener();
            const suddenChangeListener = new JsSuddenChangeListener();
            const exposureListener = new JsExposureListener();
            const appearListener = new JsAppearListener();
            const shareListener = new JsShareListener();
            const touchListener = new JsTouchListener();
            const para = new JsSensorsParaImpl({
                dataPoll,
                requestHandler,
                pageLoadListener,
                pageUnloadListener,
                pageListener,
                clickListener,
                stayListener,
                systemInfoGetter,
                storageHandler,
                launchListener,
                unloadListener,
                exposureListener,
                appearListener,
                suddenChangeListener,
                shareListener,
                touchListener
            });
            sensors = new SensorsImpl({
                para,
                dataPoll,
                requestHandler,
                pageLoadListener,
                pageListener,
                clickListener,
                stayListener,
                systemInfoGetter,
                launchListener,
                unloadListener,
                exposureListener,
                appearListener,
                touchListener,
                shareListener
            });
            window.sensors = sensors;
        }
        sensors = window.sensors;
    } catch(err) {
        console.warn(err);
        window.sensors = sensors;
    }
}


export default sensors as Sensors;