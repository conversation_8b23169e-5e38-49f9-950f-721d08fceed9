import { eventEnum, terminalEnum, libEnum } from "../runtime-core/enum";
import { judgingEnvironment, generateUUID, queryAttr, isObject, generateDataSetAttributes } from "@/utils";
import UniappSensorsParaImpl from "./uniapp-sensors-para-impl";
import { storageKey } from "@/runtime-core/sensors-para-impl";

const lib = judgingEnvironment();

/**
 * 系统信息提取 uniapp
 */
export default class UniappSystemInfoGetter
  implements SystemInfoGetter, PageObserver, LaunchObserver, PageLoadObserver, UniappScrollObserver
{
  private appId?: string; // 小程序id
  private deviceId?: string;
  private model?: string; // 手机型号。
  private pixelRatio?: number; // 设备像素比。
  private windowWidth?: number; // 窗口宽度。
  private windowHeight?: number; // 窗口高度。
  private language?: string; // 支付宝设置的语言。分别有以下值：zh-Hans（简体中文）、en（English）、zh-Hant（繁体中文（台湾））、zh-HK（繁体中文（香港））。
  private version?: string; // 支付宝版本号。
  private battery?: string; // 当前电量百分比。
  private system?: string; // 系统版本。
  private platform?: string; // 系统名：Android，iOS / iPhone OS 。
  private screenHeight?: number; // 屏幕高度。
  private brand?: string; // 手机品牌。
  private scrollTop?: number; // 滚动条位置
  private scrollHeight?: number; // 页面文档高度
  private preUrl?: string; // 上一个页面路径
  private tempUrl?: string; // 临时路径
  private routeMap: any = {};
  private trackUrlMap?: Record<string, string>;
  private customDownloadChannel?: string;
  private currentUrl = ""; // 当前页面url
  private isUrlJustUpdate = false; // 是否刚刚更新url
  private fontSizeSetting?: number; // 用户设置字体大小。
  private runScene?: string; // 运行版本
  private networkType?: string; // 网络类型
  private downloadChannel?: string; // Android app下载渠道
  private versionCode?: string; // 宿主版本号
  /**
   * Toutiao	string	今日头条
   * Douyin	string	抖音（国内版)
   * news_article_lite	string	今日头条（极速版)
   * live_stream	string	火山小视频
   * XiGua	string	西瓜
   * PPX	string	皮皮虾
   */
  private appName?: string; // 当前运行的客户端。若当前为抖音，则有效值为 "Douyin"。
  private dataPoll?: DataPoll;
  private launchTime = Date.now(); // 程序启动时间
  private _detainedTime: number; // 上次鼠标移动时间
  private _duration_time: number = 0; // 停留时间
  private enterPageTime: number = 0;
  private wifiSsid?:string; //Wifi名称
  private wifiBssid?:string; //wifi设备mac地址
  launchQuery: Record<string, string> = {}; // 小程序启动参数
  onLoadQuery: Record<string, string> = {}; // 页面启动参数
  pageTitleConfig: Record<string, any> = {}; // 配置的标题名称
  private para?: UniappSensorsParaImpl;
  private storageHandler?: StorageHandler;
  /**
   * 生成路由映射
   */
  private genderRouterMap() {
    try {
      if (typeof globalThis !== "undefined") {
        for (let i = 0; i < (globalThis as any).__uniRoutes.length; i++) {
          const route = (globalThis as any).__uniRoutes[i];
          const path = route.path;
          const title = route.meta.navigationBar.titleText;
          this.routeMap[path] = title;
        }
      }
    } catch (err) {
      console.warn(err);
    }
  }
  /**
   * 加载系统信息
   * 华为app需要在前台加载系统信息
   */
  loadSystemInfo() {
    const systemInfo: any = uni.getSystemInfoSync();
    this.deviceId = systemInfo.deviceId;
    this.model = systemInfo.model;
    this.pixelRatio = systemInfo.pixelRatio;
    this.windowWidth = systemInfo.windowWidth;
    this.windowHeight = systemInfo.windowHeight;
    this.language = systemInfo.language;
    this.battery = systemInfo.battery;
    this.system = systemInfo.system;
    this.platform = systemInfo.platform;
    this.screenHeight = systemInfo.screenHeight;
    this.brand = systemInfo.brand;
    this.fontSizeSetting = systemInfo.fontSizeSetting;
    this.appName = systemInfo.hostName;
    this.versionCode = systemInfo.hostVersion;
    if (lib === libEnum.UNIAPP_APP_PLUS) {
      this.version = systemInfo.appVersion;
      this.downloadChannel = plus.runtime.channel;
    } else if (lib === libEnum.UNIAPP_WEIXIN) {
      const accountInfo = wx.getAccountInfoSync();
      const { level } = wx.getBatteryInfoSync();
      this.appId = accountInfo.miniProgram.appId;
      this.runScene = accountInfo.miniProgram.envVersion;
      this.version = accountInfo.miniProgram.version;
      this.battery = level;
    } else if (lib === libEnum.UNIAPP_KUAISHOU) {
      const accountInfo = ks.getAccountInfoSync();
      const { level } = ks.getBatteryInfoSync();
      this.appId = accountInfo.miniProgram.appId;
      this.runScene = accountInfo.miniProgram.envVersion;
      this.version = accountInfo.miniProgram.version;
      this.battery = level;
    }
     else if (lib === libEnum.UNIAPP_JD) {
      const accountInfo = jd.getAccountInfoSync();
      const { level } = jd.getBatteryInfoSync();
      this.appId = accountInfo.miniProgram.appId;
      this.runScene = accountInfo.miniProgram.envVersion;
      this.version = accountInfo.miniProgram.version;
      this.battery = level;
    } else if (lib === libEnum.UNIAPP_DING) {
      this.appId = dd.getAppIdSync().appId;
      dd.getRunScene().then((res: any) => {
        this.runScene = res.envVersion;
      });
      this.version = systemInfo.appVersion;
      this.battery = systemInfo.currentBattery;
    } else if (lib === libEnum.UNIAPP_ALIPAY) {
      const {
        miniProgram: { envVersion, appId, version },
      } = my.getAccountInfoSync();
      this.appId = appId;
      this.runScene = envVersion;
      this.version = version;
      this.battery = systemInfo.currentBattery;
    } else if (lib === libEnum.UNIAPP_BYTEDANCE) {
      const { microapp } = tt.getEnvInfoSync();
      this.appId = microapp.appId;
      this.runScene =
        (
          {
            production: "release",
            development: "develop",
            preview: "trial",
          } as any
        )[microapp.envType] || microapp.envType;
      this.version = microapp.mpVersion;
    }
  }
  constructor() {
    if (lib !== libEnum.UNIAPP_APP_PLUS) {
      this.loadSystemInfo();
    }
    uni.getNetworkType({
      success: (res: any) => {
        this.networkType = res.networkType;
        this.dataPoll &&
          this.dataPoll.modifyAny((data: PointData) => {
            data.lib.$network_type = this.networkType;
          });
      },
    });
    this._detainedTime = Date.now();
    lib === libEnum.UNIAPP_APP_PLUS && this.genderRouterMap();
  }
  init(data: UniappSensorsParaImpl) {
    this.para = data;
    this.dataPoll = data.dataPoll;
    this.pageTitleConfig = data.pageTitleConfig;
    this.trackUrlMap = data.trackUrlMap;
    this.customDownloadChannel = data.customDownloadChannel;
    this.storageHandler = data.storageHandler;
    data.launchListener.register(this);
    data.pageLoadListener.register(this);
    data.pageListener.register(this);
    data.scrollListener.register(this);
  }
  getTerminial() {
    if (lib === libEnum.UNIAPP_WEIXIN) return terminalEnum.WX_MINI;
    if (lib === libEnum.UNIAPP_KUAISHOU) return terminalEnum.KUAISHOU;
    if (lib === libEnum.UNIAPP_JD) return terminalEnum.JD_MINI;
    if (lib === libEnum.UNIAPP_BYTEDANCE)
      return this.appName === "Douyin" ? terminalEnum.TT_MINI : terminalEnum.TOUTIAO;
    else return terminalEnum.APP_V1;
  }
  getAppId() {
    return this.appId || "";
  }
  getModel() {
    return this.model;
  }
  getPixelRatio() {
    return this.pixelRatio;
  }
  getLanguage() {
    return this.language;
  }
  getAppVersion() {
    return this.version;
  }
  getAppDownloadChannel() {
    return this.customDownloadChannel || this.downloadChannel;
  }
  getCurrentBattery() {
    return this.battery;
  }
  getSystemVersion() {
    return this.system;
  }
  getSystemPlatform() {
    return this.platform;
  }
  getBrand() {
    return this.brand;
  }
  getFontSizeSetting() {
    return this.fontSizeSetting;
  }
  getViewportWidth() {
    return this.windowWidth || 0;
  }
  getViewportHeight() {
    return this.windowHeight || 0;
  }
  getScreenHeight() {
    return this.scrollHeight || this.screenHeight || 0;
  }
  getScrollTop() {
    return this.scrollTop || 0;
  }
  getUrl() {
    return this.currentUrl;
  }
  getUrlPath() {
    return this.getUrl();
  }
  getCurrentUrl() {
    try {
      const pages = getCurrentPages();
      let route = pages[pages.length - 1]?.route || "plugin://";
      if (this.trackUrlMap && this.trackUrlMap[route]) {
        route = this.trackUrlMap[route];
      }
      return route;
    } catch (err) {
      return "";
    }
  }
  getTitle() {
    // 根据标题配置返回标题
    const url = this.getUrl();
    if (lib === libEnum.UNIAPP_APP_PLUS) {
      return this.routeMap[url] || this.routeMap[`/${url}`] || "";
    }
    if (this.pageTitleConfig[url] || this.pageTitleConfig.defaultTitle) {
      return this.pageTitleConfig[url] || this.pageTitleConfig.defaultTitle || "";
    }
  }
  getMiniVersion() {
    return this.runScene;
  }
  getNetworkType() {
    return this.networkType;
  }
  getReferrer() {
    return this.preUrl || "";
  }
  getLatestTrafficSourceType() {
    const { scene = "直接流量" } = uni.getLaunchOptionsSync() || {};
    return scene;
  }
  getLatestReferrer(): string {
    return "";
  }
  getReportIncident(e: any, funcName: string) {
    let dataset: Record<string, any>, eventName, elementType, elementPath, positionSign;
    const eventTypeMap: any = {
      onClick: eventEnum.PAGE_CLICK,
      onSubmit: eventEnum.PAGE_CLICK,
      tap: eventEnum.PAGE_CLICK,
      firstAppear: eventEnum.PAGE_EXPOSURE,
      scroll: eventEnum.Touch,
    };
    const eventType = eventTypeMap[e.type];
    if (eventType === eventEnum.PAGE_CLICK) {
      dataset = {
        ...(e.target.targetDataset || {}),
        ...(e.target.dataset || {}),
        ...(e.currentTarget.dataset || {}),
      };
      elementType = e.currentTarget.tagName;
      const attrs = Object.keys(dataset)
        .sort()
        .map((key) => {
          if (dataset[key] === undefined) return key;
          else if (/[\u4e00-\u9fa5]/.test(dataset[key])) return key;
          else if (/^\d+$/.test(dataset[key])) {
            if (Number(dataset[key]) > 1000) return key;
            else return `${key}=${dataset[key]}`;
          } else if (isObject(dataset[key])) return key;
          else if (/[a-z\d]{10,}/.test(dataset[key])) return key;
          else return `${key}=${dataset[key]}`;
        })
        .filter((item) => !!item)
        .join("&");
      positionSign = queryAttr(dataset, "position_sign");
      const lastPage = getCurrentPages().slice(-1)[0] || {};
      lastPage.$lastPositionSign = positionSign || "";
      elementPath = `${this.getTerminial()}_${this.getUrl()}_${funcName}_${elementType ? "_" + elementType : ""}${
        attrs ? "_" + attrs : ""
      }${e.target.id ? "_" + e.target.id : ""}${positionSign ? "_" + positionSign : ""}`;
      dataset && (eventName = dataset.sensors);
    } else if (eventType === eventEnum.TOUCH) {
      if (e.detail && e.detail.source === "autoplay") return;
      dataset = e.currentTarget.dataset;
      eventName = dataset.sensorsTouch || dataset.sensorstouch;
      elementType = e.currentTarget.tagName;
    } else {
      dataset = e.currentTarget.dataset;
      eventName = dataset.sensorsExposure || dataset.sensorsexposure;
      elementType = e.currentTarget.tagName;
    }
    if (dataset) {
      const customProperties: any = generateDataSetAttributes(dataset);
      const result: any = {
        eventType,
        key: eventName || eventType,
        $element_type: elementType,
        $element_path: elementPath,
        customProperties,
      };
      if (eventType === eventEnum.PAGE_CLICK) {
        result.$page_x = e.detail.pageX;
        result.$page_y = e.detail.pageY;
        result.$client_x = e.detail.clientX || e.currentTarget.offsetLeft;
        result.$client_y = e.detail.clientY || e.currentTarget.offsetTop;

        // 尝试直接获取到业务属性
        const positionSign = queryAttr(dataset, "position_sign") || queryAttr(dataset.params, "position_sign");
        positionSign && (result.customProperties.positionSignId = positionSign);
      }

      return result;
    }
  }
  generateRandomId() {
    return this.deviceId || generateUUID();
  }
  getPageDurationTime() {
    return this._duration_time;
  }
  onBeforeShow() {
    if (!this.isUrlJustUpdate) {
      this.currentUrl = this.getCurrentUrl();
    }
    this.isUrlJustUpdate = false;
    if (this.tempUrl !== this.preUrl && this.tempUrl !== this.getUrl()) {
      this.preUrl = this.tempUrl;
    }
    this._detainedTime = this.enterPageTime = Date.now();
    this._duration_time = 0;
  }
  onBeforeHide() {
    this.tempUrl = this.getUrl();
    this._duration_time = this.enterPageTime ? Date.now() - this.enterPageTime : 0;
  }
  onScrollChange(args: UniappScrollInfo) {
    this.scrollTop = Math.floor(args.scrollTop);
  }
  onBeforeLaunch(options: any) {
    if (lib === libEnum.UNIAPP_APP_PLUS) {
      this.loadSystemInfo();
      this.para?.identify(this.generateRandomId());
      this.dataPoll &&
        this.dataPoll.modifyAny((data: PointData) => {
          data.distinct_id = this.generateRandomId();
          data.lib.$model = this.getModel();
          data.lib.$pixel_ratio = this.getPixelRatio();
          data.properties.$viewport_width = this.getViewportWidth();
          data.properties.$viewport_height = this.getViewportHeight();
          data.lib.$language = this.getLanguage();
          data.lib.$current_battery = this.getCurrentBattery();
          data.lib.$system_version = this.getSystemVersion();
          data.lib.$system_platform = this.getSystemPlatform();
          data.properties.$screen_height = this.getScreenHeight();
          data.lib.$brand = this.getBrand();
          data.lib.$font_size_setting = this.getFontSizeSetting();
          data.lib.$terminal = this.getTerminial();
          data.lib.$app_version = this.getAppVersion();
        });
    }
    if (options.query) {
      this.launchQuery = options.query;
    }
  }
  onLaunch() {
    this.launchTime = this.getTimestamp();
  }
  onBeforePageLoad(options: any) {
    this.onLoadQuery = options || {};
    this.currentUrl = this.getCurrentUrl();
    this.isUrlJustUpdate = true;
    // 如果页面参数中有statistical_from字段，把字段放到程序启动参数中
    const { statistical_from } = this.onLoadQuery;
    const launchQuery = this.getLaunchQuery();
    if (statistical_from && statistical_from !== "undefined") {
      launchQuery.statistical_from = statistical_from;
    }
  }
  getLaunchQuery(): Record<string, string> {
    return this.launchQuery || {};
  }
  getOnLoadQuery(): Record<string, string> {
    return this.onLoadQuery || {};
  }
  getTimestamp() {
    return Date.now();
  }
  getLaunchTime(): number {
    return this.launchTime;
  }
  getVersionCode() {
    return this.versionCode;
  }
  getPageList() {
    const routes = getCurrentPages();
    return routes?.map((p: any) => {
      let page = p && p.route ? p.route : "plugin://";
      if (this.trackUrlMap && this.trackUrlMap[page]) {
        page = this.trackUrlMap[page];
      }
      return page
    });
  }
  getPageVersion() {
    // 看看缓存里有没有存着的页面版本号
    const store = this.storageHandler?.get<SensorsParaStore>(storageKey);
    const commProperties = store?.commProperties || {};
    const page_version_obj = commProperties.page_version_obj ? JSON.parse(commProperties.page_version_obj) : {};
    const page_version = page_version_obj[this.currentUrl] || "";
    return page_version
  }
  getPositionList() {
    let index = 0;
    const routes = getCurrentPages();
    return routes.map((p: any, idx: number) => {
      if (p && p.$lastPositionSign) {
        index = idx;
        return p.$lastPositionSign;
      } else {
        return (routes[index] && routes[index].$lastPositionSign) || "";
      }
    });
  }

  getWifiSsid(){
    return this.wifiSsid;
  }
  getWifiBssid(){
    return this.wifiBssid;
  }

    //获取已连接的 Wi-Fi 信息
  initWifiParams(){
    try{
      uni.startWifi&&uni.startWifi({
          success: ()=> {
            uni.getConnectedWifi&&uni.getConnectedWifi({
              success: (res:{wifi:WifiInfo})=> {
                const {wifi:{BSSID,SSID}}=res;
                this.wifiSsid=SSID;
                this.wifiBssid=BSSID;
                this.dataPoll &&
                this.dataPoll.modifyAny((data: PointData) => {
                  data.lib.$wifi_bssid=BSSID;
                  data.lib.$wifi_ssid=SSID;
                });
              }
            })
          }
        });
      }catch(err){
        console.log(err)
      }
    }
    //监听网络状态变化事件
  initWifiMonitor(){
      uni.onNetworkStatusChange((res:NetworkStatus)=>{
        const {isConnected,networkType}=res;
        if(isConnected&&networkType==='wifi') return this.initWifiParams();
        this.wifiBssid='';
        this.wifiSsid='';
      });
    }
}
