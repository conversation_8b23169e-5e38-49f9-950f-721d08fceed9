/**
 * 上报数据
 */
interface PointData {
    client_event_id: string; // 事件id
    universal_id?: string; // 通用id
    open_id?: string; // 客户端独立id
    union_id?: string // union id
    user_id?: string|number; // 用户ID
    initial_id: string; // 初始ID
    session_id: string; // 会话ID
    distinct_id: string; // 匿名ID
    lib: Record<string, string | number | boolean | undefined>;
    properties: {
        page_list:string[],
        position_list:string[],
        index_module_key_list:string[],
        module_position_sort_list:string[],
        [x: string]: any;
    };
    event_type?: string; // 事件类型
    event_name: string; // 事件名称
    event_time: number; // 时间搓-秒
    event_date: number; // 时间搓-秒
    event_millitime: number; // 时间戳-毫秒
}

/**
 * 数据池
 */
interface DataPoll {
    list: Record<number, PointData>; // 上报数据列表
    append(data: PointData): void; // 添加上报数据
    getList(): { // 获取事件列表
        keys: number[],
        list: PointData[]
    };
    remove(keys: number[]): void; // 删除事件
    reset(keys: number[]): void; // 恢复事件
    init(data: SensorsPara): void; // 初始化
    bufferDataPoll(): void; // 缓存数据池
    modifyAny(fn: (data: PointData) => void): void; // 修改数据池事件属性
    modifyDisctincId(id: string): void; // 修改匿名id
    modifyUserId(id: string): void; // 修改用户id
    modifyDisctincUnionId(data: IdentifyUnionInfo): void  // 修改union匿名id
    updateUniversalId(id:string,user_id?:string): void; // 更新通用id
}