import SubjectListenerImpl from "@/runtime-core/subject-listener-impl"
import UniappSensorsParaImpl from "../uniapp-sensors-para-impl";
import { libEnum } from "@/runtime-core/enum";
import { judgingEnvironment } from "@/utils";

const lib = judgingEnvironment();

/**
 * vue组件监听 Uniapp
 */
export default class UniappSysComponentListener extends SubjectListenerImpl<UniappSysComponentObserver> implements UniappSysVueComponentObserver {
    init(data: UniappSensorsParaImpl) {
        data.sysVueComponentListener.register(this);
    }
    /**
     * 监听app触发
     * @param app vue 对象
     */
    onVueComponent(component: any): void {
        lib === libEnum.UNIAPP_APP_PLUS && !component.inheritAttrs && this.notify(component);
    }
    notify(component: any) {
        this.observers.forEach(observer => observer.onComponent(component));
    }
}