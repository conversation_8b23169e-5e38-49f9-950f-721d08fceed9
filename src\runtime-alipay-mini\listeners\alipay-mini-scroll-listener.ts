import AlipayMiniSensorsParaImpl from "../alipay-mini-sensors-para-impl";

/**
 * 页面滚动监听器 支付宝小程序端
 */
export default class AlipayMiniScrollListener implements SubjectListener, AlipayMiniSysPageObserver {
    observers: AlipayMiniScrollObserver[] = [];
    onPage(definitionPage: any) {
        this.listenerOnPageScroll(definitionPage);
    }
    init(data: AlipayMiniSensorsParaImpl) {
        data.sysPageListener.register(this);
    }
    register(observer: AlipayMiniScrollObserver) {
        this.observers.push(observer);
    };
    remove(oberser: AlipayMiniScrollObserver) {
        const index = this.observers.findIndex(item => item === oberser);
        if (index != -1) this.observers.splice(index, 1);
    };
    notify(args: AlipayMiniScrollInfo) {
        this.observers.forEach(observer => observer.onScrollChange(args));
    };
    private listenerOnPageScroll(definitionPage: any) {
        const oldOnPageScroll = definitionPage.onPageScroll || function () {};
        const that = this;
        definitionPage.onPageScroll = function (args: AlipayMiniScrollInfo) {
            try {
                that.notify(args);
            } catch(err) {
                console.warn('AlipayMiniScrollListener -> listenerOnPageScroll', err);
            }
            oldOnPageScroll.call(this, ...arguments);
        }
    }
}