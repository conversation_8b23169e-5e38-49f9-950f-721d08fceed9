/*
 * @Author: itnewdata <EMAIL>
 * @Date: 2022-02-16 22:25:44
 * @LastEditors: itnewdata <EMAIL>
 * @LastEditTime: 2023-02-22 10:49:15
 * @FilePath: \data_analysis_sdk\src\runtime-browser\data.d.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
// 浏览器全局对象
declare interface Window {
    sensors: any;
}

// 突变监听者
interface SuddenChangeListener extends SubjectListener {
    observers: SuddenChangeObserver[];
    init(data: SensorsPara): void;
}

// 突变订阅者
interface SuddenChangeObserver extends Observer {
    onSuddenChange(mutations: any): void;
}