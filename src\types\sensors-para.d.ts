/*
 * @Author: itnewdata <EMAIL>
 * @Date: 2022-02-16 22:25:44
 * @LastEditors: itnewdata <EMAIL>
 * @LastEditTime: 2023-04-26 12:13:40
 * @FilePath: \data_analysis_sdk\src\types\sensors-para.d.ts
 * @Description: 上报事件信息
 */
interface ReportIncidentInfo extends ReportIncident {
    key: string;
}

/**
 * 传感器配置数据
 */
 interface SensorsPara {
    serverUrl: string; // 上报接口
    bindServerUrl?: string; // 绑定用户接口，为空则等待登陆之后才回开始上报数据
    universalIdUrl?: string; // 获取通用Id接口
    showLog: boolean;  // 是否打印日志
    batchSendTimeout: number; // 批量发送延迟时间
    scrollDelayTime: number; // 停留触发时间
    terminal?: string;
    isTrackSinglePage: boolean; // 是否单页面
    trackUrlMap?: Record<string, any> // 上报地址映射
    needCookies?: boolean // 是否需要凭证信息
    isIFrame?: boolean; // 是否是嵌入的iframe页面
    customDownloadChannel?: string // uniapp 自定义下载渠道
    tabBarPositionMap?: Record<string,string>;
    autoTrack: {
        appLaunch: boolean; // 是否采集 $MpLaunch 事件
        appHide: boolean;   // 是否采集 $MpHide 事件
        pageShow: boolean;  // 是否采集 $PageView 事件
        pageLeave: boolean; // 是否采集 $PageLeave 事件
        pageClick: boolean; // 是否采集 $PageClick 事件
        pageStay: boolean; // 是否采集 $PageStay 事件
        pageShare: boolean; // 是否采集 $PageShare
        pageExposure: boolean; // 是否采集 $PageExposure 事件
        pageAppear: boolean; // 是否采集 $PageAppear 事件
    },
    cycleProperties: Record<string, any>; // 生命周期内的变量，重新启动会清除
    store: SensorsParaStore,
    pageTitleConfig: object; // 页面标题设置
    pageProperties: object; // 页面属性，只在本页面有效，页面关闭清除
    // 功能实现
    dataPoll: DataPoll; // 数据池
    requestHandler: RequestHandler; // 请求处理
    launchListener: LaunchListener; // 程序启动监听
    pageListener: PageListener; // 页面监听
    clickListener: ClickListener; // 点击监听
    stayListener: StayListener; // 停留监听
    systemInfoGetter: SystemInfoGetter; // 系统信息提取
    storageHandler: StorageHandler; // 缓存处理
    unloadListener: UnloadListener; // 页面卸载监听
    shareListener: ShareListener; // 分享监听器
    exposureListener: ExposureListener; // 曝光监听器
    touchListener: TouchListener; // 拖动监听器
    init(data: InitData): void; // 初始化
    getTrackData(data: ReportIncidentInfo): PointData; // 获取上报数据
    login(userId: string): void; // 登录
    identify(id: string): void; // 自定义匿名id
    identifyUnion(data: IdentifyUnionInfo): void  //自定义union匿名id
    clearLatestTrafficSource(): void; // 清理最近一次站外流量来源类型
    identifySource(arg: SourceArg): void; // 缓存来源id和最近一次站外流量来源类型
    appendCommProperties(data: any, groupKey?: string): void; // 设置公共属性
    replaceCommProperties(data: any, groupKey?: string): void; // 替换公共属性
    removeCommProperties(...args: string[]): void; // 删除公共属性
    removeCommPropertiesGroup(...args: string[]): void;
    appendPageProperties(data: any): void; // 设置页面属性
    removePageProperties(...args: string[]): void; // 删除页面属性
    getCommProperties(key: string, groupKey?: string): any; // 获取公共属性
    appendCommPropertiesToPage(...args: string[]): void; // 附加公共属性到页面
    appendCycleProperties(data: Record<string, any>): void; // 附加生命周期属性
    removeCycleProperties(...args: string[]): void; // 删除生命周期属性
    getCycleProperties(): Record<string, any>; // 获取生命周期属性
}

/**
 * 传感器配置数据 - 缓存数据
 */
interface SensorsParaStore {
    open_id: string; // 客户端独立id
    union_id?: string; // union id
    initial_id?: string // 初始化id
    distinct_id: string; // 匿名ID
    session_id: string; // 会话ID
    user_id?: string;     // 用户id,在退出登录后继续上报
    universal_id?:string; // 通用id
    commProperties: Record<string, any>;  // 公共参数
    commGroupProperties: Record<string, Record<string, any>>; // 分组公共参数
    lastVisitTime?: number; // 上次访问时间
    firstVisitTime?: number; // 第一次访问时间
    sourceDistinctId?: string; // 来源匿名id
    latestTrafficSourceType?: string; // 最近一次站外流量来源类型
    latestReferrer?: string; // 最近一次站外前向地址
    is_login: boolean; // 是否登录,退出登录后置为false
}