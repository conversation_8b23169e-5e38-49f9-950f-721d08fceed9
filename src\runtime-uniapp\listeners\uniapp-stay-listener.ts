import SubjectListenerImpl from "@/runtime-core/subject-listener-impl";
import UniappSensorsParaImpl from "../uniapp-sensors-para-impl";
import { debounce } from "@/utils";

type debounceNotify = () => void;

/**
 * 页面停留监听器 uniapp
 */
export default class UniappStayListener extends SubjectListenerImpl<StayObserver> implements StayListener, UniappScrollObserver {
    onScrollChange() {
        this.debounceNotify && this.debounceNotify();
    }
    init(data: UniappSensorsParaImpl) {
        this.debounceNotify = debounce(this.notify, data.scrollDelayTime, this);
        this.resetInit();
        data.scrollListener.register(this);
    }
    debounceNotify?: debounceNotify;
    notify() {
        this.observers.forEach(observer => observer.onStayChange());
    }
    resetInit() {
        this.debounceNotify && this.debounceNotify();
    }
}