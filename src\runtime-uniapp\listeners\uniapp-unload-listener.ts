import SubjectListenerImpl from "@/runtime-core/subject-listener-impl";
import UniappSensorsParaImpl from "../uniapp-sensors-para-impl";
import { App } from "vue";
import { libEnum, uniappLib } from "@/runtime-core/enum";
import { judgingEnvironment } from "@/utils";

const lib = judgingEnvironment();

/**
 * 程序销毁监听器 uniapp
 */
export default class UniappUnloadListener extends SubjectListenerImpl<UnloadObserver> implements UnloadListener, UniappSysAppObserver {
    init(data: UniappSensorsParaImpl) {
        data.sysAppListener.register(this);
    }
    onApp(app: App) {
        lib === libEnum.UNIAPP_APP_PLUS && this.listenerAppOnHideInAppPlus(app);
        (uniappLib.includes(lib as LibEnumType)) && this.listenerAppOnHideInMp(app);
    }
    /**
     * 监听App onHide方法 app plus
     */
    private listenerAppOnHideInAppPlus(app: any) {
        const oldOnHide = app._component.onHide || function () {};
        const that = this;
        app._component.onHide = function () {
            oldOnHide.call(this, ...arguments);
            that.notify();
        }
    }
    /**
     * 监听App onHide方法 mp
     */
    private listenerAppOnHideInMp(app: any) {
        uni.onAppHide(() => {
            this.notify();
        });
    }
    notify() {
        this.observers.forEach(observer => observer.onUnload());
    }
}