import SubjectListenerImpl from "@/runtime-core/subject-listener-impl";
import AlipayMiniSensorsParaImpl from "../alipay-mini-sensors-para-impl";

/**
 * 页面销毁监听器
 */
export default class AlipayMiniPageUnloadListener extends SubjectListenerImpl<PageUnloadObserver> implements PageUnloadListener, AlipayMiniSysPageObserver {
    init(data: AlipayMiniSensorsParaImpl) {
        data.sysPageListener.register(this);
    }
    onPage(page: any) {
        this.listenerPageOnUnload(page);
    }
    /**
     * 监听页面被关闭
     */
    listenerPageOnUnload(page: any) {
        const oldOnUnload = page.onUnload || function () {};
        const that = this;
        page.onUnload = function() {
            try {
                that.notify();
            } catch(err) {
                console.warn('AlipayMiniPageListener -> listenerPageOnUnload' + err);
            }
            oldOnUnload.call(this, ...arguments);
        }
    }
    notify(): void {
        this.observers.forEach(observer => {
            observer.onPageUnload && observer.onPageUnload();
        });
        this.observers.forEach(observer => {
            observer.onAfterPageUnload && observer.onAfterPageUnload();
        });
    }
}