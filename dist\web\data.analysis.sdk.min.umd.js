/*! For license information please see data.analysis.sdk.min.umd.js.LICENSE.txt */
!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports.hoo=t():e.hoo=t()}(this,(function(){return(()=>{var e={820:function(e,t,r){var n,o;!function(i,a,s){"use strict";"undefined"!=typeof window&&r.amdO?void 0===(o="function"==typeof(n=s)?n.call(t,r,t,e):n)||(e.exports=o):e.exports?e.exports=s():a.exports?a.exports=s():a.Fingerprint2=s()}(0,this,(function(){"use strict";void 0===Array.isArray&&(Array.isArray=function(e){return"[object Array]"===Object.prototype.toString.call(e)});var e=function(e,t){e=[e[0]>>>16,65535&e[0],e[1]>>>16,65535&e[1]],t=[t[0]>>>16,65535&t[0],t[1]>>>16,65535&t[1]];var r=[0,0,0,0];return r[3]+=e[3]+t[3],r[2]+=r[3]>>>16,r[3]&=65535,r[2]+=e[2]+t[2],r[1]+=r[2]>>>16,r[2]&=65535,r[1]+=e[1]+t[1],r[0]+=r[1]>>>16,r[1]&=65535,r[0]+=e[0]+t[0],r[0]&=65535,[r[0]<<16|r[1],r[2]<<16|r[3]]},t=function(e,t){e=[e[0]>>>16,65535&e[0],e[1]>>>16,65535&e[1]],t=[t[0]>>>16,65535&t[0],t[1]>>>16,65535&t[1]];var r=[0,0,0,0];return r[3]+=e[3]*t[3],r[2]+=r[3]>>>16,r[3]&=65535,r[2]+=e[2]*t[3],r[1]+=r[2]>>>16,r[2]&=65535,r[2]+=e[3]*t[2],r[1]+=r[2]>>>16,r[2]&=65535,r[1]+=e[1]*t[3],r[0]+=r[1]>>>16,r[1]&=65535,r[1]+=e[2]*t[2],r[0]+=r[1]>>>16,r[1]&=65535,r[1]+=e[3]*t[1],r[0]+=r[1]>>>16,r[1]&=65535,r[0]+=e[0]*t[3]+e[1]*t[2]+e[2]*t[1]+e[3]*t[0],r[0]&=65535,[r[0]<<16|r[1],r[2]<<16|r[3]]},r=function(e,t){return 32==(t%=64)?[e[1],e[0]]:t<32?[e[0]<<t|e[1]>>>32-t,e[1]<<t|e[0]>>>32-t]:(t-=32,[e[1]<<t|e[0]>>>32-t,e[0]<<t|e[1]>>>32-t])},n=function(e,t){return 0==(t%=64)?e:t<32?[e[0]<<t|e[1]>>>32-t,e[1]<<t]:[e[1]<<t-32,0]},o=function(e,t){return[e[0]^t[0],e[1]^t[1]]},i=function(e){return e=o(e,[0,e[0]>>>1]),e=t(e,[4283543511,3981806797]),e=o(e,[0,e[0]>>>1]),e=t(e,[3301882366,444984403]),o(e,[0,e[0]>>>1])},a=function(a,s){s=s||0;for(var u=(a=a||"").length%16,c=a.length-u,l=[0,s],f=[0,s],p=[0,0],h=[0,0],d=[2277735313,289559509],y=[1291169091,658871167],v=0;v<c;v+=16)p=[255&a.charCodeAt(v+4)|(255&a.charCodeAt(v+5))<<8|(255&a.charCodeAt(v+6))<<16|(255&a.charCodeAt(v+7))<<24,255&a.charCodeAt(v)|(255&a.charCodeAt(v+1))<<8|(255&a.charCodeAt(v+2))<<16|(255&a.charCodeAt(v+3))<<24],h=[255&a.charCodeAt(v+12)|(255&a.charCodeAt(v+13))<<8|(255&a.charCodeAt(v+14))<<16|(255&a.charCodeAt(v+15))<<24,255&a.charCodeAt(v+8)|(255&a.charCodeAt(v+9))<<8|(255&a.charCodeAt(v+10))<<16|(255&a.charCodeAt(v+11))<<24],p=t(p,d),p=r(p,31),p=t(p,y),l=o(l,p),l=r(l,27),l=e(l,f),l=e(t(l,[0,5]),[0,1390208809]),h=t(h,y),h=r(h,33),h=t(h,d),f=o(f,h),f=r(f,31),f=e(f,l),f=e(t(f,[0,5]),[0,944331445]);switch(p=[0,0],h=[0,0],u){case 15:h=o(h,n([0,a.charCodeAt(v+14)],48));case 14:h=o(h,n([0,a.charCodeAt(v+13)],40));case 13:h=o(h,n([0,a.charCodeAt(v+12)],32));case 12:h=o(h,n([0,a.charCodeAt(v+11)],24));case 11:h=o(h,n([0,a.charCodeAt(v+10)],16));case 10:h=o(h,n([0,a.charCodeAt(v+9)],8));case 9:h=o(h,[0,a.charCodeAt(v+8)]),h=t(h,y),h=r(h,33),h=t(h,d),f=o(f,h);case 8:p=o(p,n([0,a.charCodeAt(v+7)],56));case 7:p=o(p,n([0,a.charCodeAt(v+6)],48));case 6:p=o(p,n([0,a.charCodeAt(v+5)],40));case 5:p=o(p,n([0,a.charCodeAt(v+4)],32));case 4:p=o(p,n([0,a.charCodeAt(v+3)],24));case 3:p=o(p,n([0,a.charCodeAt(v+2)],16));case 2:p=o(p,n([0,a.charCodeAt(v+1)],8));case 1:p=o(p,[0,a.charCodeAt(v)]),p=t(p,d),p=r(p,31),p=t(p,y),l=o(l,p)}return l=o(l,[0,a.length]),f=o(f,[0,a.length]),l=e(l,f),f=e(f,l),l=i(l),f=i(f),l=e(l,f),f=e(f,l),("00000000"+(l[0]>>>0).toString(16)).slice(-8)+("00000000"+(l[1]>>>0).toString(16)).slice(-8)+("00000000"+(f[0]>>>0).toString(16)).slice(-8)+("00000000"+(f[1]>>>0).toString(16)).slice(-8)},s={preprocessor:null,audio:{timeout:1e3,excludeIOS11:!0},fonts:{swfContainerId:"fingerprintjs2",swfPath:"flash/compiled/FontList.swf",userDefinedFonts:[],extendedJsFonts:!1},screen:{detectScreenOrientation:!0},plugins:{sortPluginsFor:[/palemoon/i],excludeIE:!1},extraComponents:[],excludes:{enumerateDevices:!0,pixelRatio:!0,doNotTrack:!0,fontsFlash:!0,adBlock:!0},NOT_AVAILABLE:"not available",ERROR:"error",EXCLUDED:"excluded"},u=function(e,t){if(Array.prototype.forEach&&e.forEach===Array.prototype.forEach)e.forEach(t);else if(e.length===+e.length)for(var r=0,n=e.length;r<n;r++)t(e[r],r,e);else for(var o in e)e.hasOwnProperty(o)&&t(e[o],o,e)},c=function(e,t){var r=[];return null==e?r:Array.prototype.map&&e.map===Array.prototype.map?e.map(t):(u(e,(function(e,n,o){r.push(t(e,n,o))})),r)},l=function(e){if(null==navigator.plugins)return e.NOT_AVAILABLE;for(var t=[],r=0,n=navigator.plugins.length;r<n;r++)navigator.plugins[r]&&t.push(navigator.plugins[r]);return f(e)&&(t=t.sort((function(e,t){return e.name>t.name?1:e.name<t.name?-1:0}))),c(t,(function(e){var t=c(e,(function(e){return[e.type,e.suffixes]}));return[e.name,e.description,t]}))},f=function(e){for(var t=!1,r=0,n=e.plugins.sortPluginsFor.length;r<n;r++){var o=e.plugins.sortPluginsFor[r];if(navigator.userAgent.match(o)){t=!0;break}}return t},p=function(){var e=document.createElement("canvas");return!(!e.getContext||!e.getContext("2d"))},h=function(){if(!p())return!1;var e=y(),t=!!window.WebGLRenderingContext&&!!e;return v(e),t},d=function(){return("msWriteProfilerMark"in window)+("msLaunchUri"in navigator)+("msSaveBlob"in navigator)>=2},y=function(){var e=document.createElement("canvas"),t=null;try{t=e.getContext("webgl")||e.getContext("experimental-webgl")}catch(e){}return t||(t=null),t},v=function(e){var t=e.getExtension("WEBGL_lose_context");null!=t&&t.loseContext()},g=[{key:"userAgent",getData:function(e){e(navigator.userAgent)}},{key:"webdriver",getData:function(e,t){e(null==navigator.webdriver?t.NOT_AVAILABLE:navigator.webdriver)}},{key:"language",getData:function(e,t){e(navigator.language||navigator.userLanguage||navigator.browserLanguage||navigator.systemLanguage||t.NOT_AVAILABLE)}},{key:"colorDepth",getData:function(e,t){e(window.screen.colorDepth||t.NOT_AVAILABLE)}},{key:"deviceMemory",getData:function(e,t){e(navigator.deviceMemory||t.NOT_AVAILABLE)}},{key:"pixelRatio",getData:function(e,t){e(window.devicePixelRatio||t.NOT_AVAILABLE)}},{key:"hardwareConcurrency",getData:function(e,t){e(function(e){return navigator.hardwareConcurrency?navigator.hardwareConcurrency:e.NOT_AVAILABLE}(t))}},{key:"screenResolution",getData:function(e,t){e(function(e){var t=[window.screen.width,window.screen.height];return e.screen.detectScreenOrientation&&t.sort().reverse(),t}(t))}},{key:"availableScreenResolution",getData:function(e,t){e(function(e){if(window.screen.availWidth&&window.screen.availHeight){var t=[window.screen.availHeight,window.screen.availWidth];return e.screen.detectScreenOrientation&&t.sort().reverse(),t}return e.NOT_AVAILABLE}(t))}},{key:"timezoneOffset",getData:function(e){e((new Date).getTimezoneOffset())}},{key:"timezone",getData:function(e,t){window.Intl&&window.Intl.DateTimeFormat?e((new window.Intl.DateTimeFormat).resolvedOptions().timeZone||t.NOT_AVAILABLE):e(t.NOT_AVAILABLE)}},{key:"sessionStorage",getData:function(e,t){e(function(e){try{return!!window.sessionStorage}catch(t){return e.ERROR}}(t))}},{key:"localStorage",getData:function(e,t){e(function(e){try{return!!window.localStorage}catch(t){return e.ERROR}}(t))}},{key:"indexedDb",getData:function(e,t){e(function(e){if(d())return e.EXCLUDED;try{return!!window.indexedDB}catch(t){return e.ERROR}}(t))}},{key:"addBehavior",getData:function(e){e(!!window.HTMLElement.prototype.addBehavior)}},{key:"openDatabase",getData:function(e){e(!!window.openDatabase)}},{key:"cpuClass",getData:function(e,t){e(function(e){return navigator.cpuClass||e.NOT_AVAILABLE}(t))}},{key:"platform",getData:function(e,t){e(function(e){return navigator.platform?navigator.platform:e.NOT_AVAILABLE}(t))}},{key:"doNotTrack",getData:function(e,t){e(function(e){return navigator.doNotTrack?navigator.doNotTrack:navigator.msDoNotTrack?navigator.msDoNotTrack:window.doNotTrack?window.doNotTrack:e.NOT_AVAILABLE}(t))}},{key:"plugins",getData:function(e,t){"Microsoft Internet Explorer"===navigator.appName||"Netscape"===navigator.appName&&/Trident/.test(navigator.userAgent)?t.plugins.excludeIE?e(t.EXCLUDED):e(function(e){var t=[];return Object.getOwnPropertyDescriptor&&Object.getOwnPropertyDescriptor(window,"ActiveXObject")||"ActiveXObject"in window?t=c(["AcroPDF.PDF","Adodb.Stream","AgControl.AgControl","DevalVRXCtrl.DevalVRXCtrl.1","MacromediaFlashPaper.MacromediaFlashPaper","Msxml2.DOMDocument","Msxml2.XMLHTTP","PDF.PdfCtrl","QuickTime.QuickTime","QuickTimeCheckObject.QuickTimeCheck.1","RealPlayer","RealPlayer.RealPlayer(tm) ActiveX Control (32-bit)","RealVideo.RealVideo(tm) ActiveX Control (32-bit)","Scripting.Dictionary","SWCtl.SWCtl","Shell.UIHelper","ShockwaveFlash.ShockwaveFlash","Skype.Detection","TDCCtl.TDCCtl","WMPlayer.OCX","rmocx.RealPlayer G2 Control","rmocx.RealPlayer G2 Control.1"],(function(t){try{return new window.ActiveXObject(t),t}catch(t){return e.ERROR}})):t.push(e.NOT_AVAILABLE),navigator.plugins&&(t=t.concat(l(e))),t}(t)):e(l(t))}},{key:"canvas",getData:function(e,t){p()?e(function(e){var t=[],r=document.createElement("canvas");r.width=2e3,r.height=200,r.style.display="inline";var n=r.getContext("2d");return n.rect(0,0,10,10),n.rect(2,2,6,6),t.push("canvas winding:"+(!1===n.isPointInPath(5,5,"evenodd")?"yes":"no")),n.textBaseline="alphabetic",n.fillStyle="#f60",n.fillRect(125,1,62,20),n.fillStyle="#069",e.dontUseFakeFontInCanvas?n.font="11pt Arial":n.font="11pt no-real-font-123",n.fillText("Cwm fjordbank glyphs vext quiz, 😃",2,15),n.fillStyle="rgba(102, 204, 0, 0.2)",n.font="18pt Arial",n.fillText("Cwm fjordbank glyphs vext quiz, 😃",4,45),n.globalCompositeOperation="multiply",n.fillStyle="rgb(255,0,255)",n.beginPath(),n.arc(50,50,50,0,2*Math.PI,!0),n.closePath(),n.fill(),n.fillStyle="rgb(0,255,255)",n.beginPath(),n.arc(100,50,50,0,2*Math.PI,!0),n.closePath(),n.fill(),n.fillStyle="rgb(255,255,0)",n.beginPath(),n.arc(75,100,50,0,2*Math.PI,!0),n.closePath(),n.fill(),n.fillStyle="rgb(255,0,255)",n.arc(75,75,75,0,2*Math.PI,!0),n.arc(75,75,25,0,2*Math.PI,!0),n.fill("evenodd"),r.toDataURL&&t.push("canvas fp:"+r.toDataURL()),t}(t)):e(t.NOT_AVAILABLE)}},{key:"webgl",getData:function(e,t){h()?e(function(){var e,t=function(t){return e.clearColor(0,0,0,1),e.enable(e.DEPTH_TEST),e.depthFunc(e.LEQUAL),e.clear(e.COLOR_BUFFER_BIT|e.DEPTH_BUFFER_BIT),"["+t[0]+", "+t[1]+"]"};if(!(e=y()))return null;var r=[],n=e.createBuffer();e.bindBuffer(e.ARRAY_BUFFER,n);var o=new Float32Array([-.2,-.9,0,.4,-.26,0,0,.*********,0]);e.bufferData(e.ARRAY_BUFFER,o,e.STATIC_DRAW),n.itemSize=3,n.numItems=3;var i=e.createProgram(),a=e.createShader(e.VERTEX_SHADER);e.shaderSource(a,"attribute vec2 attrVertex;varying vec2 varyinTexCoordinate;uniform vec2 uniformOffset;void main(){varyinTexCoordinate=attrVertex+uniformOffset;gl_Position=vec4(attrVertex,0,1);}"),e.compileShader(a);var s=e.createShader(e.FRAGMENT_SHADER);e.shaderSource(s,"precision mediump float;varying vec2 varyinTexCoordinate;void main() {gl_FragColor=vec4(varyinTexCoordinate,0,1);}"),e.compileShader(s),e.attachShader(i,a),e.attachShader(i,s),e.linkProgram(i),e.useProgram(i),i.vertexPosAttrib=e.getAttribLocation(i,"attrVertex"),i.offsetUniform=e.getUniformLocation(i,"uniformOffset"),e.enableVertexAttribArray(i.vertexPosArray),e.vertexAttribPointer(i.vertexPosAttrib,n.itemSize,e.FLOAT,!1,0,0),e.uniform2f(i.offsetUniform,1,1),e.drawArrays(e.TRIANGLE_STRIP,0,n.numItems);try{r.push(e.canvas.toDataURL())}catch(e){}r.push("extensions:"+(e.getSupportedExtensions()||[]).join(";")),r.push("webgl aliased line width range:"+t(e.getParameter(e.ALIASED_LINE_WIDTH_RANGE))),r.push("webgl aliased point size range:"+t(e.getParameter(e.ALIASED_POINT_SIZE_RANGE))),r.push("webgl alpha bits:"+e.getParameter(e.ALPHA_BITS)),r.push("webgl antialiasing:"+(e.getContextAttributes().antialias?"yes":"no")),r.push("webgl blue bits:"+e.getParameter(e.BLUE_BITS)),r.push("webgl depth bits:"+e.getParameter(e.DEPTH_BITS)),r.push("webgl green bits:"+e.getParameter(e.GREEN_BITS)),r.push("webgl max anisotropy:"+function(e){var t=e.getExtension("EXT_texture_filter_anisotropic")||e.getExtension("WEBKIT_EXT_texture_filter_anisotropic")||e.getExtension("MOZ_EXT_texture_filter_anisotropic");if(t){var r=e.getParameter(t.MAX_TEXTURE_MAX_ANISOTROPY_EXT);return 0===r&&(r=2),r}return null}(e)),r.push("webgl max combined texture image units:"+e.getParameter(e.MAX_COMBINED_TEXTURE_IMAGE_UNITS)),r.push("webgl max cube map texture size:"+e.getParameter(e.MAX_CUBE_MAP_TEXTURE_SIZE)),r.push("webgl max fragment uniform vectors:"+e.getParameter(e.MAX_FRAGMENT_UNIFORM_VECTORS)),r.push("webgl max render buffer size:"+e.getParameter(e.MAX_RENDERBUFFER_SIZE)),r.push("webgl max texture image units:"+e.getParameter(e.MAX_TEXTURE_IMAGE_UNITS)),r.push("webgl max texture size:"+e.getParameter(e.MAX_TEXTURE_SIZE)),r.push("webgl max varying vectors:"+e.getParameter(e.MAX_VARYING_VECTORS)),r.push("webgl max vertex attribs:"+e.getParameter(e.MAX_VERTEX_ATTRIBS)),r.push("webgl max vertex texture image units:"+e.getParameter(e.MAX_VERTEX_TEXTURE_IMAGE_UNITS)),r.push("webgl max vertex uniform vectors:"+e.getParameter(e.MAX_VERTEX_UNIFORM_VECTORS)),r.push("webgl max viewport dims:"+t(e.getParameter(e.MAX_VIEWPORT_DIMS))),r.push("webgl red bits:"+e.getParameter(e.RED_BITS)),r.push("webgl renderer:"+e.getParameter(e.RENDERER)),r.push("webgl shading language version:"+e.getParameter(e.SHADING_LANGUAGE_VERSION)),r.push("webgl stencil bits:"+e.getParameter(e.STENCIL_BITS)),r.push("webgl vendor:"+e.getParameter(e.VENDOR)),r.push("webgl version:"+e.getParameter(e.VERSION));try{var c=e.getExtension("WEBGL_debug_renderer_info");c&&(r.push("webgl unmasked vendor:"+e.getParameter(c.UNMASKED_VENDOR_WEBGL)),r.push("webgl unmasked renderer:"+e.getParameter(c.UNMASKED_RENDERER_WEBGL)))}catch(e){}return e.getShaderPrecisionFormat?(u(["FLOAT","INT"],(function(t){u(["VERTEX","FRAGMENT"],(function(n){u(["HIGH","MEDIUM","LOW"],(function(o){u(["precision","rangeMin","rangeMax"],(function(i){var a=e.getShaderPrecisionFormat(e[n+"_SHADER"],e[o+"_"+t])[i];"precision"!==i&&(i="precision "+i);var s=["webgl ",n.toLowerCase()," shader ",o.toLowerCase()," ",t.toLowerCase()," ",i,":",a].join("");r.push(s)}))}))}))})),v(e),r):(v(e),r)}()):e(t.NOT_AVAILABLE)}},{key:"webglVendorAndRenderer",getData:function(e){h()?e(function(){try{var e=y(),t=e.getExtension("WEBGL_debug_renderer_info"),r=e.getParameter(t.UNMASKED_VENDOR_WEBGL)+"~"+e.getParameter(t.UNMASKED_RENDERER_WEBGL);return v(e),r}catch(e){return null}}()):e()}},{key:"adBlock",getData:function(e){e(function(){var e=document.createElement("div");e.innerHTML="&nbsp;",e.className="adsbox";var t=!1;try{document.body.appendChild(e),t=0===document.getElementsByClassName("adsbox")[0].offsetHeight,document.body.removeChild(e)}catch(e){t=!1}return t}())}},{key:"hasLiedLanguages",getData:function(e){e(function(){if(void 0!==navigator.languages)try{if(navigator.languages[0].substr(0,2)!==navigator.language.substr(0,2))return!0}catch(e){return!0}return!1}())}},{key:"hasLiedResolution",getData:function(e){e(window.screen.width<window.screen.availWidth||window.screen.height<window.screen.availHeight)}},{key:"hasLiedOs",getData:function(e){e(function(){var e,t=navigator.userAgent.toLowerCase(),r=navigator.oscpu,n=navigator.platform.toLowerCase();if(e=t.indexOf("windows phone")>=0?"Windows Phone":t.indexOf("windows")>=0||t.indexOf("win16")>=0||t.indexOf("win32")>=0||t.indexOf("win64")>=0||t.indexOf("win95")>=0||t.indexOf("win98")>=0||t.indexOf("winnt")>=0||t.indexOf("wow64")>=0?"Windows":t.indexOf("android")>=0?"Android":t.indexOf("linux")>=0||t.indexOf("cros")>=0||t.indexOf("x11")>=0?"Linux":t.indexOf("iphone")>=0||t.indexOf("ipad")>=0||t.indexOf("ipod")>=0||t.indexOf("crios")>=0||t.indexOf("fxios")>=0?"iOS":t.indexOf("macintosh")>=0||t.indexOf("mac_powerpc)")>=0?"Mac":"Other",("ontouchstart"in window||navigator.maxTouchPoints>0||navigator.msMaxTouchPoints>0)&&"Windows"!==e&&"Windows Phone"!==e&&"Android"!==e&&"iOS"!==e&&"Other"!==e&&-1===t.indexOf("cros"))return!0;if(void 0!==r){if((r=r.toLowerCase()).indexOf("win")>=0&&"Windows"!==e&&"Windows Phone"!==e)return!0;if(r.indexOf("linux")>=0&&"Linux"!==e&&"Android"!==e)return!0;if(r.indexOf("mac")>=0&&"Mac"!==e&&"iOS"!==e)return!0;if((-1===r.indexOf("win")&&-1===r.indexOf("linux")&&-1===r.indexOf("mac"))!=("Other"===e))return!0}return n.indexOf("win")>=0&&"Windows"!==e&&"Windows Phone"!==e||(n.indexOf("linux")>=0||n.indexOf("android")>=0||n.indexOf("pike")>=0)&&"Linux"!==e&&"Android"!==e||(n.indexOf("mac")>=0||n.indexOf("ipad")>=0||n.indexOf("ipod")>=0||n.indexOf("iphone")>=0)&&"Mac"!==e&&"iOS"!==e||!(n.indexOf("arm")>=0&&"Windows Phone"===e)&&!(n.indexOf("pike")>=0&&t.indexOf("opera mini")>=0)&&((n.indexOf("win")<0&&n.indexOf("linux")<0&&n.indexOf("mac")<0&&n.indexOf("iphone")<0&&n.indexOf("ipad")<0&&n.indexOf("ipod")<0)!=("Other"===e)||void 0===navigator.plugins&&"Windows"!==e&&"Windows Phone"!==e)}())}},{key:"hasLiedBrowser",getData:function(e){e(function(){var e,t=navigator.userAgent.toLowerCase(),r=navigator.productSub;if(t.indexOf("edge/")>=0||t.indexOf("iemobile/")>=0)return!1;if(t.indexOf("opera mini")>=0)return!1;if(("Chrome"==(e=t.indexOf("firefox/")>=0?"Firefox":t.indexOf("opera/")>=0||t.indexOf(" opr/")>=0?"Opera":t.indexOf("chrome/")>=0?"Chrome":t.indexOf("safari/")>=0?t.indexOf("android 1.")>=0||t.indexOf("android 2.")>=0||t.indexOf("android 3.")>=0||t.indexOf("android 4.")>=0?"AOSP":"Safari":t.indexOf("trident/")>=0?"Internet Explorer":"Other")||"Safari"===e||"Opera"===e)&&"20030107"!==r)return!0;var n,o=eval.toString().length;if(37===o&&"Safari"!==e&&"Firefox"!==e&&"Other"!==e)return!0;if(39===o&&"Internet Explorer"!==e&&"Other"!==e)return!0;if(33===o&&"Chrome"!==e&&"AOSP"!==e&&"Opera"!==e&&"Other"!==e)return!0;try{throw"a"}catch(e){try{e.toSource(),n=!0}catch(e){n=!1}}return n&&"Firefox"!==e&&"Other"!==e}())}},{key:"touchSupport",getData:function(e){e(function(){var e,t=0;void 0!==navigator.maxTouchPoints?t=navigator.maxTouchPoints:void 0!==navigator.msMaxTouchPoints&&(t=navigator.msMaxTouchPoints);try{document.createEvent("TouchEvent"),e=!0}catch(t){e=!1}return[t,e,"ontouchstart"in window]}())}},{key:"fonts",getData:function(e,t){var r=["monospace","sans-serif","serif"],n=["Andale Mono","Arial","Arial Black","Arial Hebrew","Arial MT","Arial Narrow","Arial Rounded MT Bold","Arial Unicode MS","Bitstream Vera Sans Mono","Book Antiqua","Bookman Old Style","Calibri","Cambria","Cambria Math","Century","Century Gothic","Century Schoolbook","Comic Sans","Comic Sans MS","Consolas","Courier","Courier New","Geneva","Georgia","Helvetica","Helvetica Neue","Impact","Lucida Bright","Lucida Calligraphy","Lucida Console","Lucida Fax","LUCIDA GRANDE","Lucida Handwriting","Lucida Sans","Lucida Sans Typewriter","Lucida Sans Unicode","Microsoft Sans Serif","Monaco","Monotype Corsiva","MS Gothic","MS Outlook","MS PGothic","MS Reference Sans Serif","MS Sans Serif","MS Serif","MYRIAD","MYRIAD PRO","Palatino","Palatino Linotype","Segoe Print","Segoe Script","Segoe UI","Segoe UI Light","Segoe UI Semibold","Segoe UI Symbol","Tahoma","Times","Times New Roman","Times New Roman PS","Trebuchet MS","Verdana","Wingdings","Wingdings 2","Wingdings 3"];t.fonts.extendedJsFonts&&(n=n.concat(["Abadi MT Condensed Light","Academy Engraved LET","ADOBE CASLON PRO","Adobe Garamond","ADOBE GARAMOND PRO","Agency FB","Aharoni","Albertus Extra Bold","Albertus Medium","Algerian","Amazone BT","American Typewriter","American Typewriter Condensed","AmerType Md BT","Andalus","Angsana New","AngsanaUPC","Antique Olive","Aparajita","Apple Chancery","Apple Color Emoji","Apple SD Gothic Neo","Arabic Typesetting","ARCHER","ARNO PRO","Arrus BT","Aurora Cn BT","AvantGarde Bk BT","AvantGarde Md BT","AVENIR","Ayuthaya","Bandy","Bangla Sangam MN","Bank Gothic","BankGothic Md BT","Baskerville","Baskerville Old Face","Batang","BatangChe","Bauer Bodoni","Bauhaus 93","Bazooka","Bell MT","Bembo","Benguiat Bk BT","Berlin Sans FB","Berlin Sans FB Demi","Bernard MT Condensed","BernhardFashion BT","BernhardMod BT","Big Caslon","BinnerD","Blackadder ITC","BlairMdITC TT","Bodoni 72","Bodoni 72 Oldstyle","Bodoni 72 Smallcaps","Bodoni MT","Bodoni MT Black","Bodoni MT Condensed","Bodoni MT Poster Compressed","Bookshelf Symbol 7","Boulder","Bradley Hand","Bradley Hand ITC","Bremen Bd BT","Britannic Bold","Broadway","Browallia New","BrowalliaUPC","Brush Script MT","Californian FB","Calisto MT","Calligrapher","Candara","CaslonOpnface BT","Castellar","Centaur","Cezanne","CG Omega","CG Times","Chalkboard","Chalkboard SE","Chalkduster","Charlesworth","Charter Bd BT","Charter BT","Chaucer","ChelthmITC Bk BT","Chiller","Clarendon","Clarendon Condensed","CloisterBlack BT","Cochin","Colonna MT","Constantia","Cooper Black","Copperplate","Copperplate Gothic","Copperplate Gothic Bold","Copperplate Gothic Light","CopperplGoth Bd BT","Corbel","Cordia New","CordiaUPC","Cornerstone","Coronet","Cuckoo","Curlz MT","DaunPenh","Dauphin","David","DB LCD Temp","DELICIOUS","Denmark","DFKai-SB","Didot","DilleniaUPC","DIN","DokChampa","Dotum","DotumChe","Ebrima","Edwardian Script ITC","Elephant","English 111 Vivace BT","Engravers MT","EngraversGothic BT","Eras Bold ITC","Eras Demi ITC","Eras Light ITC","Eras Medium ITC","EucrosiaUPC","Euphemia","Euphemia UCAS","EUROSTILE","Exotc350 Bd BT","FangSong","Felix Titling","Fixedsys","FONTIN","Footlight MT Light","Forte","FrankRuehl","Fransiscan","Freefrm721 Blk BT","FreesiaUPC","Freestyle Script","French Script MT","FrnkGothITC Bk BT","Fruitger","FRUTIGER","Futura","Futura Bk BT","Futura Lt BT","Futura Md BT","Futura ZBlk BT","FuturaBlack BT","Gabriola","Galliard BT","Gautami","Geeza Pro","Geometr231 BT","Geometr231 Hv BT","Geometr231 Lt BT","GeoSlab 703 Lt BT","GeoSlab 703 XBd BT","Gigi","Gill Sans","Gill Sans MT","Gill Sans MT Condensed","Gill Sans MT Ext Condensed Bold","Gill Sans Ultra Bold","Gill Sans Ultra Bold Condensed","Gisha","Gloucester MT Extra Condensed","GOTHAM","GOTHAM BOLD","Goudy Old Style","Goudy Stout","GoudyHandtooled BT","GoudyOLSt BT","Gujarati Sangam MN","Gulim","GulimChe","Gungsuh","GungsuhChe","Gurmukhi MN","Haettenschweiler","Harlow Solid Italic","Harrington","Heather","Heiti SC","Heiti TC","HELV","Herald","High Tower Text","Hiragino Kaku Gothic ProN","Hiragino Mincho ProN","Hoefler Text","Humanst 521 Cn BT","Humanst521 BT","Humanst521 Lt BT","Imprint MT Shadow","Incised901 Bd BT","Incised901 BT","Incised901 Lt BT","INCONSOLATA","Informal Roman","Informal011 BT","INTERSTATE","IrisUPC","Iskoola Pota","JasmineUPC","Jazz LET","Jenson","Jester","Jokerman","Juice ITC","Kabel Bk BT","Kabel Ult BT","Kailasa","KaiTi","Kalinga","Kannada Sangam MN","Kartika","Kaufmann Bd BT","Kaufmann BT","Khmer UI","KodchiangUPC","Kokila","Korinna BT","Kristen ITC","Krungthep","Kunstler Script","Lao UI","Latha","Leelawadee","Letter Gothic","Levenim MT","LilyUPC","Lithograph","Lithograph Light","Long Island","Lydian BT","Magneto","Maiandra GD","Malayalam Sangam MN","Malgun Gothic","Mangal","Marigold","Marion","Marker Felt","Market","Marlett","Matisse ITC","Matura MT Script Capitals","Meiryo","Meiryo UI","Microsoft Himalaya","Microsoft JhengHei","Microsoft New Tai Lue","Microsoft PhagsPa","Microsoft Tai Le","Microsoft Uighur","Microsoft YaHei","Microsoft Yi Baiti","MingLiU","MingLiU_HKSCS","MingLiU_HKSCS-ExtB","MingLiU-ExtB","Minion","Minion Pro","Miriam","Miriam Fixed","Mistral","Modern","Modern No. 20","Mona Lisa Solid ITC TT","Mongolian Baiti","MONO","MoolBoran","Mrs Eaves","MS LineDraw","MS Mincho","MS PMincho","MS Reference Specialty","MS UI Gothic","MT Extra","MUSEO","MV Boli","Nadeem","Narkisim","NEVIS","News Gothic","News GothicMT","NewsGoth BT","Niagara Engraved","Niagara Solid","Noteworthy","NSimSun","Nyala","OCR A Extended","Old Century","Old English Text MT","Onyx","Onyx BT","OPTIMA","Oriya Sangam MN","OSAKA","OzHandicraft BT","Palace Script MT","Papyrus","Parchment","Party LET","Pegasus","Perpetua","Perpetua Titling MT","PetitaBold","Pickwick","Plantagenet Cherokee","Playbill","PMingLiU","PMingLiU-ExtB","Poor Richard","Poster","PosterBodoni BT","PRINCETOWN LET","Pristina","PTBarnum BT","Pythagoras","Raavi","Rage Italic","Ravie","Ribbon131 Bd BT","Rockwell","Rockwell Condensed","Rockwell Extra Bold","Rod","Roman","Sakkal Majalla","Santa Fe LET","Savoye LET","Sceptre","Script","Script MT Bold","SCRIPTINA","Serifa","Serifa BT","Serifa Th BT","ShelleyVolante BT","Sherwood","Shonar Bangla","Showcard Gothic","Shruti","Signboard","SILKSCREEN","SimHei","Simplified Arabic","Simplified Arabic Fixed","SimSun","SimSun-ExtB","Sinhala Sangam MN","Sketch Rockwell","Skia","Small Fonts","Snap ITC","Snell Roundhand","Socket","Souvenir Lt BT","Staccato222 BT","Steamer","Stencil","Storybook","Styllo","Subway","Swis721 BlkEx BT","Swiss911 XCm BT","Sylfaen","Synchro LET","System","Tamil Sangam MN","Technical","Teletype","Telugu Sangam MN","Tempus Sans ITC","Terminal","Thonburi","Traditional Arabic","Trajan","TRAJAN PRO","Tristan","Tubular","Tunga","Tw Cen MT","Tw Cen MT Condensed","Tw Cen MT Condensed Extra Bold","TypoUpright BT","Unicorn","Univers","Univers CE 55 Medium","Univers Condensed","Utsaah","Vagabond","Vani","Vijaya","Viner Hand ITC","VisualUI","Vivaldi","Vladimir Script","Vrinda","Westminster","WHITNEY","Wide Latin","ZapfEllipt BT","ZapfHumnst BT","ZapfHumnst Dm BT","Zapfino","Zurich BlkEx BT","Zurich Ex BT","ZWAdobeF"])),n=(n=n.concat(t.fonts.userDefinedFonts)).filter((function(e,t){return n.indexOf(e)===t}));var o=document.getElementsByTagName("body")[0],i=document.createElement("div"),a=document.createElement("div"),s={},u={},c=function(){var e=document.createElement("span");return e.style.position="absolute",e.style.left="-9999px",e.style.fontSize="72px",e.style.fontStyle="normal",e.style.fontWeight="normal",e.style.letterSpacing="normal",e.style.lineBreak="auto",e.style.lineHeight="normal",e.style.textTransform="none",e.style.textAlign="left",e.style.textDecoration="none",e.style.textShadow="none",e.style.whiteSpace="normal",e.style.wordBreak="normal",e.style.wordSpacing="normal",e.innerHTML="mmmmmmmmmmlli",e},l=function(e,t){var r=c();return r.style.fontFamily="'"+e+"',"+t,r},f=function(e){for(var t=!1,n=0;n<r.length;n++)if(t=e[n].offsetWidth!==s[r[n]]||e[n].offsetHeight!==u[r[n]])return t;return t},p=function(){for(var e=[],t=0,n=r.length;t<n;t++){var o=c();o.style.fontFamily=r[t],i.appendChild(o),e.push(o)}return e}();o.appendChild(i);for(var h=0,d=r.length;h<d;h++)s[r[h]]=p[h].offsetWidth,u[r[h]]=p[h].offsetHeight;var y=function(){for(var e={},t=0,o=n.length;t<o;t++){for(var i=[],s=0,u=r.length;s<u;s++){var c=l(n[t],r[s]);a.appendChild(c),i.push(c)}e[n[t]]=i}return e}();o.appendChild(a);for(var v=[],g=0,m=n.length;g<m;g++)f(y[n[g]])&&v.push(n[g]);o.removeChild(a),o.removeChild(i),e(v)},pauseBefore:!0},{key:"fontsFlash",getData:function(e,t){return void 0!==window.swfobject?window.swfobject.hasFlashPlayerVersion("9.0.0")?t.fonts.swfPath?void function(e,t){var r="___fp_swf_loaded";window[r]=function(t){e(t)};var n,o=t.fonts.swfContainerId;(n=document.createElement("div")).setAttribute("id",(void 0).fonts.swfContainerId),document.body.appendChild(n);var i={onReady:r};window.swfobject.embedSWF(t.fonts.swfPath,o,"1","1","9.0.0",!1,i,{allowScriptAccess:"always",menu:"false"},{})}((function(t){e(t)}),t):e("missing options.fonts.swfPath"):e("flash not installed"):e("swf object not loaded")},pauseBefore:!0},{key:"audio",getData:function(e,t){var r=t.audio;if(r.excludeIOS11&&navigator.userAgent.match(/OS 11.+Version\/11.+Safari/))return e(t.EXCLUDED);var n=window.OfflineAudioContext||window.webkitOfflineAudioContext;if(null==n)return e(t.NOT_AVAILABLE);var o=new n(1,44100,44100),i=o.createOscillator();i.type="triangle",i.frequency.setValueAtTime(1e4,o.currentTime);var a=o.createDynamicsCompressor();u([["threshold",-50],["knee",40],["ratio",12],["reduction",-20],["attack",0],["release",.25]],(function(e){void 0!==a[e[0]]&&"function"==typeof a[e[0]].setValueAtTime&&a[e[0]].setValueAtTime(e[1],o.currentTime)})),i.connect(a),a.connect(o.destination),i.start(0),o.startRendering();var s=setTimeout((function(){return console.warn('Audio fingerprint timed out. Please report bug at https://github.com/fingerprintjs/fingerprintjs with your user agent: "'+navigator.userAgent+'".'),o.oncomplete=function(){},o=null,e("audioTimeout")}),r.timeout);o.oncomplete=function(t){var r;try{clearTimeout(s),r=t.renderedBuffer.getChannelData(0).slice(4500,5e3).reduce((function(e,t){return e+Math.abs(t)}),0).toString(),i.disconnect(),a.disconnect()}catch(t){return void e(t)}e(r)}}},{key:"enumerateDevices",getData:function(e,t){if(!navigator.mediaDevices||!navigator.mediaDevices.enumerateDevices)return e(t.NOT_AVAILABLE);navigator.mediaDevices.enumerateDevices().then((function(t){e(t.map((function(e){return"id="+e.deviceId+";gid="+e.groupId+";"+e.kind+";"+e.label})))})).catch((function(t){e(t)}))}}],m=function(e){throw new Error("'new Fingerprint()' is deprecated, see https://github.com/fingerprintjs/fingerprintjs#upgrade-guide-from-182-to-200")};return m.get=function(e,t){t?e||(e={}):(t=e,e={}),function(e,t){if(null==t)return e;var r,n;for(n in t)null==(r=t[n])||Object.prototype.hasOwnProperty.call(e,n)||(e[n]=r)}(e,s),e.components=e.extraComponents.concat(g);var r={data:[],addPreprocessedComponent:function(t,n){"function"==typeof e.preprocessor&&(n=e.preprocessor(t,n)),r.data.push({key:t,value:n})}},n=-1,o=function(i){if((n+=1)>=e.components.length)t(r.data);else{var a=e.components[n];if(e.excludes[a.key])o(!1);else{if(!i&&a.pauseBefore)return n-=1,void setTimeout((function(){o(!0)}),1);try{a.getData((function(e){r.addPreprocessedComponent(a.key,e),o(!1)}),e)}catch(e){r.addPreprocessedComponent(a.key,String(e)),o(!1)}}}};o(!1)},m.getPromise=function(e){return new Promise((function(t,r){m.get(e,t)}))},m.getV18=function(e,t){return null==t&&(t=e,e={}),m.get(e,(function(r){for(var n=[],o=0;o<r.length;o++){var i=r[o];if(i.value===(e.NOT_AVAILABLE||"not available"))n.push({key:i.key,value:"unknown"});else if("plugins"===i.key)n.push({key:"plugins",value:c(i.value,(function(e){var t=c(e[2],(function(e){return e.join?e.join("~"):e})).join(",");return[e[0],e[1],t].join("::")}))});else if(-1!==["canvas","webgl"].indexOf(i.key)&&Array.isArray(i.value))n.push({key:i.key,value:i.value.join("~")});else if(-1!==["sessionStorage","localStorage","indexedDb","addBehavior","openDatabase"].indexOf(i.key)){if(!i.value)continue;n.push({key:i.key,value:1})}else i.value?n.push(i.value.join?{key:i.key,value:i.value.join(";")}:i):n.push({key:i.key,value:i.value})}var s=a(c(n,(function(e){return e.value})).join("~~~"),31);t(s,n)}))},m.x64hash128=a,m.VERSION="2.1.4",m}))},294:function(e){e.exports=function(e){function t(n){if(r[n])return r[n].exports;var o=r[n]={i:n,l:!1,exports:{}};return e[n].call(o.exports,o,o.exports,t),o.l=!0,o.exports}var r={};return t.m=e,t.c=r,t.i=function(e){return e},t.d=function(e,r,n){t.o(e,r)||Object.defineProperty(e,r,{configurable:!1,enumerable:!0,get:n})},t.n=function(e){var r=e&&e.__esModule?function(){return e.default}:function(){return e};return t.d(r,"a",r),r},t.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},t.p="",t(t.s=1)}([function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=function(){function e(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,r,n){return r&&e(t.prototype,r),n&&e(t,n),t}}(),o=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e)}return n(e,null,[{key:"hash",value:function(t){return e.hex(e.md51(t))}},{key:"md5cycle",value:function(t,r){var n=t[0],o=t[1],i=t[2],a=t[3];n=e.ff(n,o,i,a,r[0],7,-680876936),a=e.ff(a,n,o,i,r[1],12,-389564586),i=e.ff(i,a,n,o,r[2],17,606105819),o=e.ff(o,i,a,n,r[3],22,-1044525330),n=e.ff(n,o,i,a,r[4],7,-176418897),a=e.ff(a,n,o,i,r[5],12,1200080426),i=e.ff(i,a,n,o,r[6],17,-1473231341),o=e.ff(o,i,a,n,r[7],22,-45705983),n=e.ff(n,o,i,a,r[8],7,1770035416),a=e.ff(a,n,o,i,r[9],12,-1958414417),i=e.ff(i,a,n,o,r[10],17,-42063),o=e.ff(o,i,a,n,r[11],22,-1990404162),n=e.ff(n,o,i,a,r[12],7,1804603682),a=e.ff(a,n,o,i,r[13],12,-40341101),i=e.ff(i,a,n,o,r[14],17,-1502002290),o=e.ff(o,i,a,n,r[15],22,1236535329),n=e.gg(n,o,i,a,r[1],5,-165796510),a=e.gg(a,n,o,i,r[6],9,-1069501632),i=e.gg(i,a,n,o,r[11],14,643717713),o=e.gg(o,i,a,n,r[0],20,-373897302),n=e.gg(n,o,i,a,r[5],5,-701558691),a=e.gg(a,n,o,i,r[10],9,38016083),i=e.gg(i,a,n,o,r[15],14,-660478335),o=e.gg(o,i,a,n,r[4],20,-405537848),n=e.gg(n,o,i,a,r[9],5,568446438),a=e.gg(a,n,o,i,r[14],9,-1019803690),i=e.gg(i,a,n,o,r[3],14,-187363961),o=e.gg(o,i,a,n,r[8],20,1163531501),n=e.gg(n,o,i,a,r[13],5,-1444681467),a=e.gg(a,n,o,i,r[2],9,-51403784),i=e.gg(i,a,n,o,r[7],14,1735328473),o=e.gg(o,i,a,n,r[12],20,-1926607734),n=e.hh(n,o,i,a,r[5],4,-378558),a=e.hh(a,n,o,i,r[8],11,-2022574463),i=e.hh(i,a,n,o,r[11],16,1839030562),o=e.hh(o,i,a,n,r[14],23,-35309556),n=e.hh(n,o,i,a,r[1],4,-1530992060),a=e.hh(a,n,o,i,r[4],11,1272893353),i=e.hh(i,a,n,o,r[7],16,-155497632),o=e.hh(o,i,a,n,r[10],23,-1094730640),n=e.hh(n,o,i,a,r[13],4,681279174),a=e.hh(a,n,o,i,r[0],11,-358537222),i=e.hh(i,a,n,o,r[3],16,-722521979),o=e.hh(o,i,a,n,r[6],23,76029189),n=e.hh(n,o,i,a,r[9],4,-640364487),a=e.hh(a,n,o,i,r[12],11,-421815835),i=e.hh(i,a,n,o,r[15],16,530742520),o=e.hh(o,i,a,n,r[2],23,-995338651),n=e.ii(n,o,i,a,r[0],6,-198630844),a=e.ii(a,n,o,i,r[7],10,1126891415),i=e.ii(i,a,n,o,r[14],15,-1416354905),o=e.ii(o,i,a,n,r[5],21,-57434055),n=e.ii(n,o,i,a,r[12],6,1700485571),a=e.ii(a,n,o,i,r[3],10,-1894986606),i=e.ii(i,a,n,o,r[10],15,-1051523),o=e.ii(o,i,a,n,r[1],21,-2054922799),n=e.ii(n,o,i,a,r[8],6,1873313359),a=e.ii(a,n,o,i,r[15],10,-30611744),i=e.ii(i,a,n,o,r[6],15,-1560198380),o=e.ii(o,i,a,n,r[13],21,1309151649),n=e.ii(n,o,i,a,r[4],6,-145523070),a=e.ii(a,n,o,i,r[11],10,-1120210379),i=e.ii(i,a,n,o,r[2],15,718787259),o=e.ii(o,i,a,n,r[9],21,-343485551),t[0]=n+t[0]&4294967295,t[1]=o+t[1]&4294967295,t[2]=i+t[2]&4294967295,t[3]=a+t[3]&4294967295}},{key:"cmn",value:function(e,t,r,n,o,i){return((t=(t+e&4294967295)+(n+i&4294967295)&4294967295)<<o|t>>>32-o)+r&4294967295}},{key:"ff",value:function(t,r,n,o,i,a,s){return e.cmn(r&n|~r&o,t,r,i,a,s)}},{key:"gg",value:function(t,r,n,o,i,a,s){return e.cmn(r&o|n&~o,t,r,i,a,s)}},{key:"hh",value:function(t,r,n,o,i,a,s){return e.cmn(r^n^o,t,r,i,a,s)}},{key:"ii",value:function(t,r,n,o,i,a,s){return e.cmn(n^(r|~o),t,r,i,a,s)}},{key:"md51",value:function(t){for(var r,n=t.length,o=[1732584193,-271733879,-1732584194,271733878],i=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],a=64;a<=n;a+=64)e.md5cycle(o,e.md5blk(t.substring(a-64,a)));for(t=t.substring(a-64),a=0,r=t.length;a<r;a++)i[a>>2]|=t.charCodeAt(a)<<(a%4<<3);if(i[a>>2]|=128<<(a%4<<3),a>55)for(e.md5cycle(o,i),a=0;a<16;a++)i[a]=0;return i[14]=8*n,e.md5cycle(o,i),o}},{key:"md5blk",value:function(e){for(var t=[],r=0;r<64;r+=4)t[r>>2]=e.charCodeAt(r)+(e.charCodeAt(r+1)<<8)+(e.charCodeAt(r+2)<<16)+(e.charCodeAt(r+3)<<24);return t}},{key:"rhex",value:function(t){var r="";return r+=e.hexArray[t>>4&15]+e.hexArray[t>>0&15],r+=e.hexArray[t>>12&15]+e.hexArray[t>>8&15],(r+=e.hexArray[t>>20&15]+e.hexArray[t>>16&15])+(e.hexArray[t>>28&15]+e.hexArray[t>>24&15])}},{key:"hex",value:function(t){for(var r=t.length,n=0;n<r;n++)t[n]=e.rhex(t[n]);return t.join("")}}]),e}();o.hexArray=["0","1","2","3","4","5","6","7","8","9","a","b","c","d","e","f"],t.default=o},function(e,t,r){e.exports=r(0)}])},737:function(e){e.exports=function(e){function t(n){if(r[n])return r[n].exports;var o=r[n]={i:n,l:!1,exports:{}};return e[n].call(o.exports,o,o.exports,t),o.l=!0,o.exports}var r={};return t.m=e,t.c=r,t.i=function(e){return e},t.d=function(e,r,n){t.o(e,r)||Object.defineProperty(e,r,{configurable:!1,enumerable:!0,get:n})},t.n=function(e){var r=e&&e.__esModule?function(){return e.default}:function(){return e};return t.d(r,"a",r),r},t.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},t.p="",t(t.s=1)}([function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=function(){function e(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,r,n){return r&&e(t.prototype,r),n&&e(t,n),t}}(),o=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e)}return n(e,null,[{key:"hash",value:function(t){return e.stringToHex(e.arrayToString(e.run(e.stringToArray(t),8*t.length)))}},{key:"run",value:function(t,r){var n=[1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298],o=15+(r+64>>9<<4),i=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],a=0,s=1779033703,u=-1150833019,c=1013904242,l=-1521486534,f=1359893119,p=-1694144372,h=528734635,d=1541459225,y=s,v=u,g=c,m=l,b=f,w=p,P=h,S=d;for(t[r>>5]|=128<<24-r%32,t[o]=r;a<o;a+=16){s=y,u=v,c=g,l=m,f=b,p=w,h=P,d=S;for(var T=0,k=null,O=null;T<64;T+=1)i[T]=T<16?t[T+a]:e.add(e.add(e.add(e.gamma1256(i[T-2]),i[T-7]),e.gamma0256(i[T-15])),i[T-16]),k=e.add(e.add(e.add(e.add(S,e.sigma1256(b)),e.ch(b,w,P)),n[T]),i[T]),O=e.add(e.sigma0256(y),e.maj(y,v,g)),S=P,P=w,w=b,b=e.add(m,k),m=g,g=v,v=y,y=e.add(k,O);y=e.add(y,s),v=e.add(v,u),g=e.add(g,c),m=e.add(m,l),b=e.add(b,f),w=e.add(w,p),P=e.add(P,h),S=e.add(S,d)}return[y,v,g,m,b,w,P,S]}},{key:"arrayToString",value:function(e){for(var t=32*e.length,r=0,n="";r<t;r+=8)n+=String.fromCharCode(e[r>>5]>>>24-r%32&255);return n}},{key:"stringToArray",value:function(e){for(var t=8*e.length,r=Array(e.length>>2),n=r.length,o=0;o<n;o+=1)r[o]=0;for(o=0;o<t;o+=8)r[o>>5]|=(255&e.charCodeAt(o/8))<<24-o%32;return r}},{key:"stringToHex",value:function(e){for(var t="0123456789abcdef",r=e.length,n="",o=null,i=0;i<r;i+=1)o=e.charCodeAt(i),n+=t.charAt(o>>>4&15)+t.charAt(15&o);return n}},{key:"rotl",value:function(e,t){return e>>>t|e<<32-t}},{key:"rotr",value:function(e,t){return e>>>t}},{key:"ch",value:function(e,t,r){return e&t^~e&r}},{key:"maj",value:function(e,t,r){return e&t^e&r^t&r}},{key:"sigma0256",value:function(t){return e.rotl(t,2)^e.rotl(t,13)^e.rotl(t,22)}},{key:"sigma1256",value:function(t){return e.rotl(t,6)^e.rotl(t,11)^e.rotl(t,25)}},{key:"gamma0256",value:function(t){return e.rotl(t,7)^e.rotl(t,18)^e.rotr(t,3)}},{key:"gamma1256",value:function(t){return e.rotl(t,17)^e.rotl(t,19)^e.rotr(t,10)}},{key:"add",value:function(e,t){var r=(65535&e)+(65535&t);return(e>>16)+(t>>16)+(r>>16)<<16|65535&r}}]),e}();t.default=o},function(e,t,r){e.exports=r(0)}])}},t={};function r(n){var o=t[n];if(void 0!==o)return o.exports;var i=t[n]={exports:{}};return e[n].call(i.exports,i,i.exports,r),i.exports}r.amdO={},r.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return r.d(t,{a:t}),t},r.d=(e,t)=>{for(var n in t)r.o(t,n)&&!r.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},r.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t);var n={};return(()=>{"use strict";r.d(n,{default:()=>Sn});var e="js",t="AlipayMini",o="uniapp-h5",i="$Login",a="$PageView",s="$PageClick",u="$PageStay",c="$PageLeave",l="$MpLaunch",f="$MpHide",p="$PageShare",h="$PageInvite",d="alipay.indi",y="wx.mini",v="alipay.tp.vivo",g="alipay.honor",m="alipay.huawei",b="alipay.tp.xiaomi",w="alipay.tp.oppo",P=["dynamicStrProps","dynamicIntProps","dynamicFloatProps"],S=r(737),T=r.n(S),k=r(294),O=r.n(k),_=r(820),L=r.n(_);function x(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=E(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var n=0,o=function(){};return{s:o,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,a=!0,s=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return a=e.done,e},e:function(e){s=!0,i=e},f:function(){try{a||null==r.return||r.return()}finally{if(s)throw i}}}}function E(e,t){if(e){if("string"==typeof e)return A(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?A(e,t):void 0}}function A(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function C(e){return C="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},C(e)}function I(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function j(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?I(Object(r),!0).forEach((function(t){B(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):I(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function B(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function R(e){return"[object Object]"===Object.prototype.toString.call(e)}function D(){return Date.now()+"-"+Math.floor(1e7*Math.random())+"-"+Math.random().toString(16).replace(".","")+"-"+String(31242*Math.random()).replace(".","").slice(0,8)}function M(){var r="undefined"!=typeof uni,n="undefined"!=typeof my;try{if(n&&!r)return t;if(r){var i=uni.getSystemInfoSync().uniPlatform;return"undefined"!=typeof wx&&"mp-weixin"===i?"uniapp-weixin":"undefined"!=typeof tt?"uniapp-byte-dance":"undefined"!=typeof ks?"uniapp-kuaishou":"undefined"!=typeof dd?"uniapp-ding":n?"uniapp-alipay":"undefined"!=typeof plus?"uniapp-app-plus":"web"===i?o:"uniapp-unknown"}if(void 0!==window)return e}catch(e){return console.warn("环境判断出错:",e)}}function U(e,t,r){var n=null;return function(){for(var o=arguments.length,i=new Array(o),a=0;a<o;a++)i[a]=arguments[a];return n&&clearTimeout(n),new Promise((function(o){n=setTimeout((function(){o(e.call.apply(e,[r].concat(i))),n=null}),t)}))}}function N(e,t,r){var n=0;return function(){if(Date.now()-n>=t){for(var o=arguments.length,i=new Array(o),a=0;a<o;a++)i[a]=arguments[a];e.apply(r,i),n=Date.now()}}}function G(e){if(!e)return!1;var t=toString.call(e);return"[object Function]"==t||"[object AsyncFunction]"==t}function H(e){return""!==e&&null!=e&&!isNaN(e)}function $(e){if(!R(e))return"";for(var t=Object.keys(e).sort(),r="",n=0;n<t.length;n++){var o=e[t[n]];r+=(r?"&":"")+(R(o)?JSON.stringify(o):o)}return T().hash(r)}function F(e){if(!R(e))return"";for(var t=Object.keys(e).sort(),r="",n=0;n<t.length;n++){var o=e[t[n]];r+=(r?"&":"")+(R(o)?JSON.stringify(o):o)}return O().hash(r).toString()}function V(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=arguments.length>2?arguments[2]:void 0,n=Date.now(),o=D(),i=$(r),a="jjpiewjf;k029q3-1*ksk3323m",s=F(j({microtime:n,nonstr:o,hash_data:i,secret:a},t));return"".concat(e,"?microtime=").concat(n,"&nonstr=").concat(o,"&hash_data=").concat(i,"&sign=").concat(s)}function W(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return e.replace(/([A-Z])/g,"_$1").toLowerCase()}function X(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if("object"!==C(e)||null==e)return e;var t=Array.isArray(e)?[]:{};for(var r in e)e.hasOwnProperty(r)&&(t[r]=X(e[r]));return t}function q(e){return void 0===e||""===e||null===e||"number"==typeof e&&isNaN(e)}function K(){var e,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",r="",n=x(t);try{for(n.s();!(e=n.n()).done;){var o=e.value;r+=o.charCodeAt(0)}}catch(e){n.e(e)}finally{n.f()}return r}function z(e,t){var r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if("object"===C(e)&&null!=e)for(var n in e){var o=void 0,i=/sensors_.+/.exec(n);if(i&&(o=W(i[0].slice(8))),e.hasOwnProperty(n)&&(n===t||r&&o===t))return e[n];if("object"===C(e[n])){var a=z(e[n],t);if(a)return a}}}function J(){var e=location.search.match(/[^?|&]+\=[^&]+/g);return e?Object.fromEntries(e.map((function(e){return e.split("=")}))):{}}function Q(e,t,r){if(R(r)){var n=t.replace(/.*?dynamic(.+?)Props$/,"$1").toLowerCase();Object.entries(r).forEach((function(t,r){var o,i,a=(i=2,function(e){if(Array.isArray(e))return e}(o=t)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i=[],a=!0,s=!1;try{for(r=r.call(e);!(a=(n=r.next()).done)&&(i.push(n.value),!t||i.length!==t);a=!0);}catch(e){s=!0,o=e}finally{try{a||null==r.return||r.return()}finally{if(s)throw o}}return i}}(o,i)||E(o,i)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()),s=a[0],u=a[1];e["dynamic_".concat(n,"_key_").concat(r+1)]=s,e["dynamic_".concat(n,"_value_").concat(r+1)]=u}))}}function Y(e){return Y="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Y(e)}function Z(){Z=function(){return e};var e={},t=Object.prototype,r=t.hasOwnProperty,n="function"==typeof Symbol?Symbol:{},o=n.iterator||"@@iterator",i=n.asyncIterator||"@@asyncIterator",a=n.toStringTag||"@@toStringTag";function s(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{s({},"")}catch(e){s=function(e,t,r){return e[t]=r}}function u(e,t,r,n){var o=t&&t.prototype instanceof f?t:f,i=Object.create(o.prototype),a=new T(n||[]);return i._invoke=function(e,t,r){var n="suspendedStart";return function(o,i){if("executing"===n)throw new Error("Generator is already running");if("completed"===n){if("throw"===o)throw i;return{value:void 0,done:!0}}for(r.method=o,r.arg=i;;){var a=r.delegate;if(a){var s=w(a,r);if(s){if(s===l)continue;return s}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if("suspendedStart"===n)throw n="completed",r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n="executing";var u=c(e,t,r);if("normal"===u.type){if(n=r.done?"completed":"suspendedYield",u.arg===l)continue;return{value:u.arg,done:r.done}}"throw"===u.type&&(n="completed",r.method="throw",r.arg=u.arg)}}}(e,r,a),i}function c(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}e.wrap=u;var l={};function f(){}function p(){}function h(){}var d={};s(d,o,(function(){return this}));var y=Object.getPrototypeOf,v=y&&y(y(k([])));v&&v!==t&&r.call(v,o)&&(d=v);var g=h.prototype=f.prototype=Object.create(d);function m(e){["next","throw","return"].forEach((function(t){s(e,t,(function(e){return this._invoke(t,e)}))}))}function b(e,t){function n(o,i,a,s){var u=c(e[o],e,i);if("throw"!==u.type){var l=u.arg,f=l.value;return f&&"object"==Y(f)&&r.call(f,"__await")?t.resolve(f.__await).then((function(e){n("next",e,a,s)}),(function(e){n("throw",e,a,s)})):t.resolve(f).then((function(e){l.value=e,a(l)}),(function(e){return n("throw",e,a,s)}))}s(u.arg)}var o;this._invoke=function(e,r){function i(){return new t((function(t,o){n(e,r,t,o)}))}return o=o?o.then(i,i):i()}}function w(e,t){var r=e.iterator[t.method];if(void 0===r){if(t.delegate=null,"throw"===t.method){if(e.iterator.return&&(t.method="return",t.arg=void 0,w(e,t),"throw"===t.method))return l;t.method="throw",t.arg=new TypeError("The iterator does not provide a 'throw' method")}return l}var n=c(r,e.iterator,t.arg);if("throw"===n.type)return t.method="throw",t.arg=n.arg,t.delegate=null,l;var o=n.arg;return o?o.done?(t[e.resultName]=o.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,l):o:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,l)}function P(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function S(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function T(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(P,this),this.reset(!0)}function k(e){if(e){var t=e[o];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var n=-1,i=function t(){for(;++n<e.length;)if(r.call(e,n))return t.value=e[n],t.done=!1,t;return t.value=void 0,t.done=!0,t};return i.next=i}}return{next:O}}function O(){return{value:void 0,done:!0}}return p.prototype=h,s(g,"constructor",h),s(h,"constructor",p),p.displayName=s(h,a,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===p||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,h):(e.__proto__=h,s(e,a,"GeneratorFunction")),e.prototype=Object.create(g),e},e.awrap=function(e){return{__await:e}},m(b.prototype),s(b.prototype,i,(function(){return this})),e.AsyncIterator=b,e.async=function(t,r,n,o,i){void 0===i&&(i=Promise);var a=new b(u(t,r,n,o),i);return e.isGeneratorFunction(r)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},m(g),s(g,a,"Generator"),s(g,o,(function(){return this})),s(g,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=[];for(var r in e)t.push(r);return t.reverse(),function r(){for(;t.length;){var n=t.pop();if(n in e)return r.value=n,r.done=!1,r}return r.done=!0,r}},e.values=k,T.prototype={constructor:T,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(S),!e)for(var t in this)"t"===t.charAt(0)&&r.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function n(r,n){return a.type="throw",a.arg=e,t.next=r,n&&(t.method="next",t.arg=void 0),!!n}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],a=i.completion;if("root"===i.tryLoc)return n("end");if(i.tryLoc<=this.prev){var s=r.call(i,"catchLoc"),u=r.call(i,"finallyLoc");if(s&&u){if(this.prev<i.catchLoc)return n(i.catchLoc,!0);if(this.prev<i.finallyLoc)return n(i.finallyLoc)}else if(s){if(this.prev<i.catchLoc)return n(i.catchLoc,!0)}else{if(!u)throw new Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return n(i.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=e,a.arg=t,i?(this.method="next",this.next=i.finallyLoc,l):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),l},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),S(r),l}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if("throw"===n.type){var o=n.arg;S(r)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,r){return this.delegate={iterator:k(e),resultName:t,nextLoc:r},"next"===this.method&&(this.arg=void 0),l}},e}function ee(e,t,r,n,o,i,a){try{var s=e[i](a),u=s.value}catch(e){return void r(e)}s.done?t(u):Promise.resolve(u).then(n,o)}function te(e){return function(){var t=this,r=arguments;return new Promise((function(n,o){var i=e.apply(t,r);function a(e){ee(i,n,o,a,s,"next",e)}function s(e){ee(i,n,o,a,s,"throw",e)}a(void 0)}))}}function re(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=function(e,t){if(e){if("string"==typeof e)return ne(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?ne(e,t):void 0}}(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var n=0,o=function(){};return{s:o,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,a=!0,s=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return a=e.done,e},e:function(e){s=!0,i=e},f:function(){try{a||null==r.return||r.return()}finally{if(s)throw i}}}}function ne(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function oe(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function ie(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var ae=new Map([["2088532636361975",!0],["2088342505212574",!0]]),se=M(),ue=function(){function e(t){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),ie(this,"para",void 0),ie(this,"sysAppListener",void 0),ie(this,"dataPoll",void 0),ie(this,"requestHandler",void 0),ie(this,"stayListener",void 0),ie(this,"systemInfoGetter",void 0),ie(this,"send",void 0),ie(this,"_debounceUpload",U(this.upload,500,this)),this.para=t.para,this.dataPoll=t.dataPoll,this.sysAppListener=t.sysAppListener,this.requestHandler=t.requestHandler,t.pageLoadListener.register(this),t.pageListener.register(this),t.clickListener.register(this),t.stayListener.register(this),this.stayListener=t.stayListener,this.systemInfoGetter=t.systemInfoGetter,t.launchListener.register(this),t.unloadListener.register(this),t.exposureListener.register(this),t.appearListener.register(this),t.shareListener.register(this),t.touchListener.register(this),t.sourceListener&&t.sourceListener.register(this),t.tabItemListener&&t.tabItemListener.register(this),this.send=U(this.sendPointData,this.para.batchSendTimeout,this)}var t,r,n,P,S;return t=e,r=[{key:"install",value:function(e){var t;se===o&&(null===(t=this.sysAppListener)||void 0===t||t.notify(e))}},{key:"init",value:function(e){try{this.para.init(e),this.send=U(this.sendPointData,this.para.batchSendTimeout,this)}catch(e){console.warn(e)}}},{key:"appendPointData",value:function(e){var t=this.para.getTrackData(e);if(!ae.get(t.distinct_id))return this.dataPoll.append(t),t}},{key:"getReportIncidentInfo",value:function(e){if("string"==typeof e.key)return[e];var t,r=[],n=re(e.key);try{for(n.s();!(t=n.n()).done;){var o=t.value,i=X(e);i.key=o,r.push(i)}}catch(e){n.e(e)}finally{n.f()}return r}},{key:"track",value:function(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],r=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];return this.upload(e,{isSend:t,isDebounce:r})}},{key:"upload",value:function(e,t){var r=[];try{var n,o=re(this.getReportIncidentInfo(e));try{for(o.s();!(n=o.n()).done;){var i=n.value,a=this.appendPointData(i);a&&r.push(a)}}catch(e){o.e(e)}finally{o.f()}t.isSend&&(t.isDebounce?this.send&&this.send():this.sendPointData({isLast:t.isLast}))}catch(e){console.warn("SensorsImpl -> track",e)}return r}},{key:"sendPointData",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{isClone:!1,isLast:!1};if(this.para.bindServerUrl||this.para.store.user_id){var r=this.systemInfoGetter.getTerminial(),n=this.para.store,o=n.distinct_id,i=n.open_id;if((r!==d||o&&i&&o===i)&&(r!==y||o)){var a=[v,g,m,b,w];if(!a.includes(r)||o){var s=this.dataPoll.getList(),u=s.keys,c=s.list;if(0!==c.length){t.isClone&&(c=X(c));var l=this.requestHandler.send(c,t.isLast);l&&l.then((function(t){e.para.showLog&&console.log("上报数据",c),e.dataPoll.remove(u)})).catch((function(t){e.dataPoll.reset(u),e.para.showLog&&console.log("上报失败",t)}))}}}}}},{key:"onPageLoad",value:function(){try{var e=this.systemInfoGetter.getOnLoadQuery();if(e.dc_st&&(e.dc_uid||e.dc_did)){var t=this.appendPointData({eventType:h,key:h});t&&(t.event_millitime=this.systemInfoGetter.getLaunchTime()),this.send&&this.send()}}catch(e){console.warn(e)}}},{key:"onPageShow",value:function(){try{this.para.autoTrack.pageShow&&this.track({eventType:a,key:a}),this.para.autoTrack.pageStay&&this.stayListener.resetInit()}catch(e){console.warn(e)}}},{key:"onPageHide",value:function(){try{this.para.autoTrack.pageLeave&&this.track({eventType:c,key:"$PageLeave"})}catch(e){console.warn(e)}}},{key:"onClickChange",value:function(e,t){try{var r=this.systemInfoGetter.getReportIncident(e,t);r&&this.para.autoTrack.pageClick&&this.track(r,r.key!==s)}catch(e){console.warn(e)}}},{key:"onTabItemClick",value:function(e){var t=this,r=this.para,n=r.autoTrack.pageClick,o=r.tabBarPositionMap;n&&o&&o[e]&&setTimeout((function(){t.track({key:"$TabItemClick",eventType:s,customProperties:{positionSign:o[e],positionSignId:o[e]}})}),0)}},{key:"onStayChange",value:function(e){try{if(this.para.autoTrack.pageStay){var t={eventType:u,key:u};e&&(t.customProperties={$screen_height:this.systemInfoGetter.getScreenHeight(e),$screen_top:this.systemInfoGetter.getScrollTop(e)}),this.track(t,!1)}}catch(e){console.warn(e)}}},{key:"onLaunch",value:function(){try{this.para.autoTrack.appLaunch&&this.track({eventType:l,key:l}),this.para.clearLatestTrafficSource()}catch(e){console.warn(e)}}},{key:"onUnload",value:function(){try{this.para.autoTrack.appHide&&this.upload({eventType:f,key:f},{isSend:!0,isLast:!0})}catch(e){console.warn(e)}}},{key:"_addParamsToPageShare",value:function(e){var t=e.result;if(t.path||(t.path=this.systemInfoGetter.getUrl()),t.path.includes("dc_st"))return t;var r={dc_st:Date.now(),dc_did:this.para.store.distinct_id};this.para.store.user_id&&(r.dc_uid=this.para.store.user_id);var n=this.para.getCommProperties("activityId","clue");n&&(r.dc_acp=n);var o=Object.keys(r).map((function(e){return"".concat(e,"=").concat(r[e])})).join("&");if(t.path)-1!==t.path.indexOf("?")?t.path+="&".concat(o):t.path+="?".concat(o);else{var i=this.systemInfoGetter.getOnLoadQuery()||{};t.path="".concat(this.systemInfoGetter.getUrl(),"?").concat(Object.keys(i).map((function(e){return"".concat(e,"=").concat(i[e])})).join("&"),"&").concat(o)}return t}},{key:"onShare",value:function(e){try{if(this._addParamsToPageShare(e),this.para.autoTrack.pageShare){var t=this.appendPointData({eventType:p,key:p});t&&(t.properties.$referrer=this.systemInfoGetter.getUrl(),t.properties.$url=e.result.path),this.sendPointData()}}catch(e){console.warn(e)}}},{key:"login",value:(S=te(Z().mark((function e(t){var r,n,o;return Z().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(e.prev=0,null!=t&&""!==t&&"0"!=t){e.next=3;break}return e.abrupt("return",console.warn("记录登录用户ID不能为空"));case 3:return this.para.store.is_login=!0,r=this.para.store,n=r.union_id,o=r.open_id,e.next=7,this.dataPoll.updateUniversalId(n||o,t);case 7:if(t!=this.para.store.user_id){e.next=9;break}return e.abrupt("return");case 9:this.para.store.user_id&&this.sendPointData(),this.para.login(t),this.dataPoll.modifyUserId(t),this.track({eventType:i,key:i}),this.userBind(),e.next=19;break;case 16:e.prev=16,e.t0=e.catch(0),console.warn(e.t0);case 19:case"end":return e.stop()}}),e,this,[[0,16]])}))),function(e){return S.apply(this,arguments)})},{key:"onExposure",value:function(e){try{var t=this.systemInfoGetter.getReportIncident(e);t&&this.para.autoTrack.pageExposure&&this.track(t,!1)}catch(e){console.warn(e)}}},{key:"onAppear",value:function(e){try{var t=this.systemInfoGetter.getReportIncident(e);t&&this.para.autoTrack.pageAppear&&this.track(t,!1)}catch(e){console.warn(e)}}},{key:"onTouch",value:function(e){try{var t=this.systemInfoGetter.getReportIncident(e);t&&this.track(t,!1)}catch(e){console.warn(e)}}},{key:"onSource",value:function(e){if(void 0===e.id||null===e.id||""===e.id)return console.warn("来源匿名ID不能为空");if(e.id!==this.para.store.sourceDistinctId||e.latestTrafficSourceType!==this.para.store.latestTrafficSourceType||e.latestReferrer!==this.para.store.latestReferrer)try{this.para.identifySource(e),this.para.store.user_id&&e.id!==this.para.store.sourceDistinctId&&this.userResourceId(),this.appendCommProperties({$source_terminal:e.latestTrafficSourceType})}catch(e){console.warn(e)}}},{key:"userResourceId",value:function(){var e=this;try{if(!this.para.store.sourceDistinctId)return;var t={user_id:this.para.store.user_id,distinct_id:this.para.store.sourceDistinctId};if(this.para.bindServerUrl){var r=this.requestHandler.sendRequest(this.para.bindServerUrl,t);r&&r.then((function(){e.para.showLog&&console.log("绑定用户id和来源匿名id",t)}))}}catch(e){console.warn(e)}}},{key:"userBind",value:function(){var e=this;try{var t={user_id:this.para.store.user_id,distinct_id:this.para.store.distinct_id};if(this.para.bindServerUrl){var r=this.requestHandler.sendRequest(this.para.bindServerUrl,t);r&&r.then((function(){e.para.showLog&&console.log("绑定用户id和匿名id",t)})),this.userResourceId()}}catch(e){console.warn(e)}}},{key:"identify",value:(P=te(Z().mark((function e(t){return Z().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(e.prev=0,null!=t&&""!==t){e.next=3;break}return e.abrupt("return",console.warn("自定义匿名ID不能为空"));case 3:return e.next=5,this.dataPoll.updateUniversalId(t);case 5:if(t!==this.para.store.distinct_id||t!==this.para.store.open_id){e.next=7;break}return e.abrupt("return");case 7:this.para.identify(t),this.dataPoll.modifyDisctincId(t),e.next=14;break;case 11:e.prev=11,e.t0=e.catch(0),console.warn(e.t0);case 14:case"end":return e.stop()}}),e,this,[[0,11]])}))),function(e){return P.apply(this,arguments)})},{key:"setIdentifyUnion",value:(n=te(Z().mark((function e(t){var r;return Z().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=t.unionid,e.prev=1,e.next=4,this.dataPoll.updateUniversalId(r);case 4:if(!this.para.store.distinct_id){e.next=6;break}return e.abrupt("return");case 6:if(null!=r&&""!==r){e.next=8;break}return e.abrupt("return",console.warn("unionid不能为空"));case 8:if(r!==this.para.store.distinct_id||r!==this.para.store.union_id){e.next=10;break}return e.abrupt("return");case 10:try{this.para.identifyUnion(t),this.dataPoll.modifyDisctincUnionId(t)}catch(e){console.warn(e)}e.next=16;break;case 13:e.prev=13,e.t0=e.catch(1),console.warn(e.t0);case 16:case"end":return e.stop()}}),e,this,[[1,13]])}))),function(e){return n.apply(this,arguments)})},{key:"appendCommProperties",value:function(e,t){try{if(!R(e))return console.warn("公共属性必须为对象");this.para.appendCommProperties(e,t),this.para.appendPageProperties(e)}catch(e){console.warn(e)}}},{key:"replaceCommProperties",value:function(e,t){try{if(!R(e))return console.warn("公共属性必须为对象");this.para.replaceCommProperties(e,t),this.para.appendPageProperties(e)}catch(e){console.warn(e)}}},{key:"removeCommProperties",value:function(){try{var e;(e=this.para).removeCommProperties.apply(e,arguments)}catch(e){console.warn(e)}}},{key:"removeCommPropertiesGroup",value:function(){try{var e;(e=this.para).removeCommPropertiesGroup.apply(e,arguments)}catch(e){console.warn(e)}}},{key:"appendPageProperties",value:function(e){try{if(!R(e))return console.warn("附加公共属性必须为对象");this.para.appendPageProperties(e)}catch(e){console.warn(e)}}},{key:"removePageProperties",value:function(){try{var e;(e=this.para).removePageProperties.apply(e,arguments)}catch(e){console.warn(e)}}},{key:"getCommProperties",value:function(e,t){try{return this.para.getCommProperties(e,t)}catch(e){console.warn(e)}}},{key:"appendCommPropertiesToPage",value:function(){try{var e;(e=this.para).appendCommPropertiesToPage.apply(e,arguments)}catch(e){console.warn(e)}}},{key:"appendCycleProperties",value:function(e){this.para.appendCycleProperties(e)}},{key:"removeCycleProperties",value:function(){try{var e;(e=this.para).removeCycleProperties.apply(e,arguments)}catch(e){console.warn(e)}}}],r&&oe(t.prototype,r),Object.defineProperty(t,"prototype",{writable:!1}),e}();const ce="1.3.17";function le(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=function(e,t){if(e){if("string"==typeof e)return fe(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?fe(e,t):void 0}}(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var n=0,o=function(){};return{s:o,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,a=!0,s=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return a=e.done,e},e:function(e){s=!0,i=e},f:function(){try{a||null==r.return||r.return()}finally{if(s)throw i}}}}function fe(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function pe(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function he(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var de="sensonrsdata",ye="DISTINCT_ID_STORE_KEY",ve=M(),ge=function(){function r(e){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,r),he(this,"$terminal",void 0),he(this,"$lib_version",ce),he(this,"storageKey","RRZU_SENSORS_DATA"),he(this,"bufferPageProperties",{}),he(this,"preUrl",void 0),he(this,"format",void 0),he(this,"autoTrack",{appLaunch:!0,appHide:!0,pageShow:!0,pageLeave:!0,pageClick:!0,pageStay:!0,pageShare:!0,pageExposure:!0,pageAppear:!0}),he(this,"serverUrl",""),he(this,"universalIdUrl",""),he(this,"needCookies",void 0),he(this,"customDownloadChannel",void 0),he(this,"isIFrame",void 0),he(this,"bindServerUrl",""),he(this,"showLog",!1),he(this,"batchSendTimeout",3e3),he(this,"dataSendTimeout",6e4),he(this,"scrollDelayTime",4e3),he(this,"isTrackSinglePage",!1),he(this,"trackUrlMap",void 0),he(this,"store",{open_id:"",distinct_id:"",union_id:"",user_id:"",initial_id:"",universal_id:"",commProperties:{},commGroupProperties:{},is_login:!1}),he(this,"cycleProperties",{}),he(this,"launchParams",{}),he(this,"loadParams",{}),he(this,"pageTitleConfig",{}),he(this,"pageProperties",{}),he(this,"dataPoll",void 0),he(this,"requestHandler",void 0),he(this,"pageLoadListener",void 0),he(this,"pageUnloadListener",void 0),he(this,"pageListener",void 0),he(this,"clickListener",void 0),he(this,"stayListener",void 0),he(this,"tabItemListener",void 0),he(this,"systemInfoGetter",void 0),he(this,"storageHandler",void 0),he(this,"launchListener",void 0),he(this,"unloadListener",void 0),he(this,"shareListener",void 0),he(this,"exposureListener",void 0),he(this,"appearListener",void 0),he(this,"touchListener",void 0),he(this,"inviteUserId",void 0),he(this,"inviteDistinctId",void 0),he(this,"inviteTime",void 0),he(this,"tabBarPositionMap",void 0),this.requestHandler=e.requestHandler,this.dataPoll=e.dataPoll,this.pageLoadListener=e.pageLoadListener,this.pageUnloadListener=e.pageUnloadListener,this.pageListener=e.pageListener,this.clickListener=e.clickListener,this.stayListener=e.stayListener,this.launchListener=e.launchListener,this.unloadListener=e.unloadListener,this.systemInfoGetter=e.systemInfoGetter,this.storageHandler=e.storageHandler,this.$terminal=this.systemInfoGetter.getTerminial(),e.launchListener.register(this),e.pageLoadListener.register(this),e.pageListener.register(this),e.pageUnloadListener.register(this),e.unloadListener.register(this),this.shareListener=e.shareListener,this.exposureListener=e.exposureListener,this.appearListener=e.appearListener,this.touchListener=e.touchListener}var n,o;return n=r,o=[{key:"init",value:function(e){if(!e.serverUrl)return console.warn("当前 serverUrl 为空或不正确，只在控制台打印日志，network 中不会发数据，请配置正确的 serverUrl");if(e.bindServerUrl||console.warn("当前 bindServerUrl 为空或不正确"),e.universalIdUrl||console.warn("当前 universalIdUrl 为空或不正确"),this.initStore(),e.terminal&&(this.$terminal=e.terminal,this.systemInfoGetter.getTerminial=function(){return e.terminal}),this.initDistinctId(),this.serverUrl=e.serverUrl,this.universalIdUrl=e.universalIdUrl||"",this.needCookies=e.needCookies,e.customDownloadChannel&&(this.customDownloadChannel=e.customDownloadChannel),this.isIFrame=e.isIFrame,this.bindServerUrl=e.bindServerUrl||"",this.format=e.format,e.showLog&&(this.showLog=e.showLog),e.isTrackSinglePage&&(this.isTrackSinglePage=e.isTrackSinglePage),e.trackUrlMap&&(this.trackUrlMap=e.trackUrlMap),R(e.tabBarPositionMap)&&(this.tabBarPositionMap=e.tabBarPositionMap),H(e.batchSendTimeout)&&(this.batchSendTimeout=e.batchSendTimeout),H(e.dataSendTimeout)&&(this.dataSendTimeout=e.dataSendTimeout),H(e.scrollDelayTime)&&(this.scrollDelayTime=e.scrollDelayTime),e.autoTrack&&(e.autoTrack.appLaunch&&(this.autoTrack.appLaunch=e.autoTrack.appLaunch),e.autoTrack.appHide&&(this.autoTrack.appHide=e.autoTrack.appHide),e.autoTrack.pageShow&&(this.autoTrack.pageShow=e.autoTrack.pageShow),e.autoTrack.pageLeave&&(this.autoTrack.pageLeave=e.autoTrack.pageLeave),e.autoTrack.pageClick&&(this.autoTrack.pageClick=e.autoTrack.pageClick)),e.pageTitleConfig){if(!R(e.pageTitleConfig))return console.warn("设置页面标题参数必须为对象");this.pageTitleConfig=e.pageTitleConfig}this.systemInfoGetter.init(this),this.dataPoll.init(this),this.requestHandler.serverUrl=e.serverUrl,this.requestHandler.needCookies=e.needCookies,H(this.dataSendTimeout)&&(this.requestHandler.dataSendTimeout=this.dataSendTimeout),(this.autoTrack.pageShow||this.autoTrack.pageStay)&&this.pageListener.init(this),this.pageLoadListener.init(this),this.pageUnloadListener.init(this),this.autoTrack.pageClick&&this.clickListener.init(this),this.autoTrack.pageStay&&this.stayListener.init(this),this.unloadListener.init(this),this.launchListener&&this.launchListener.init(this),this.shareListener.init(this),this.exposureListener.init(this),this.appearListener.init(this),this.touchListener.init(this),this.bufferStore()}},{key:"initStore",value:function(){var e=this.storageHandler.get(de);e&&(this.store=e,e.commGroupProperties||(this.store.commGroupProperties={}))}},{key:"initDistinctId",value:function(){var e=this.systemInfoGetter.generateRandomId(),t=this.systemInfoGetter.getTerminial(),r=[y,v,g,m,b,w];this.store.distinct_id||r.includes(t)||(this.store.distinct_id=e),this.store.initial_id=e}},{key:"bufferStore",value:function(){this.storageHandler.set(de,this.store)}},{key:"appendLaunchQuery",value:function(e){var t=this.systemInfoGetter.getLaunchQuery();for(var r in t)q(t[r])||(e.properties["launch_"+W(r)]=t[r],"third_terminal"===r&&(e.properties.launch_statistical_from=t[r]))}},{key:"login",value:function(e){var t=this.storageHandler.get(ye)||{};this.store.user_id&&this.store.user_id!==e?(t&&t[e]&&(this.store.distinct_id=t[e]),this.store.user_id=e):(this.store.user_id=e,t[e]=this.store.distinct_id),this.storageHandler.set(ye,t),this.bufferStore()}},{key:"identify",value:function(e){e&&(this.store.open_id=e,this.store.distinct_id=e,this.bufferStore())}},{key:"identifyUnion",value:function(e){var t=e.openid,r=e.unionid;this.store.open_id=t,this.store.union_id=r,r&&(this.store.distinct_id=r),this.bufferStore()}},{key:"getTrackData",value:function(e){var t={client_event_id:D()};this.store.user_id&&(t.user_id=this.store.user_id),this.store.open_id&&(t.open_id=this.store.open_id),this.store.union_id&&(t.union_id=this.store.union_id),this.store.initial_id&&(t.initial_id=this.store.initial_id),this.store.universal_id&&(t.universal_id=this.store.universal_id),t.distinct_id=this.store.latestTrafficSourceType&&this.store.sourceDistinctId||this.store.distinct_id;var r=this.systemInfoGetter.getTimestamp();for(var n in t.lib={$terminal:this.$terminal,$lib_version:ce},t.properties={$latest_traffic_source_type:this.store.latestTrafficSourceType||this.systemInfoGetter.getLatestTrafficSourceType(),$latest_referrer:this.store.latestReferrer||this.systemInfoGetter.getLatestReferrer(),$timezone_offset:(new Date).getTimezoneOffset(),$viewport_width:this.systemInfoGetter.getViewportWidth(),$viewport_height:this.systemInfoGetter.getViewportHeight(),$screen_height:this.systemInfoGetter.getScreenHeight(),$screen_top:this.systemInfoGetter.getScrollTop(),$url:this.systemInfoGetter.getUrl(),$url_path:this.systemInfoGetter.getUrlPath(),$referrer:this.systemInfoGetter.getReferrer(),$is_first_day:this.getIsFirstDay(r),$is_first_time:!this.store.firstVisitTime,$is_login:!!this.store.is_login},this.appendLaunchQuery(t),this.cycleProperties)q(this.cycleProperties[n])||(t.properties[W(n)]=this.cycleProperties[n]);var o=this.systemInfoGetter.getTitle();o&&(t.properties.$title=o),t.event_type=e.eventType||"$Track",t.event_name=e.key,t.event_time=Number(r.toString().slice(0,10)),t.event_date=t.event_time,t.event_millitime=r,t.properties.$event_duration=this.systemInfoGetter.getPageDurationTime(),e.$appear_duration&&(t.properties.$appear_duration=e.$appear_duration),e.eventType===s&&(t.properties.$element_type=e.$element_type,e.$page_x&&(t.properties.$page_x=e.$page_x),e.$page_y&&(t.properties.$page_y=e.$page_y),e.$client_x&&(t.properties.$client_x=e.$client_x),e.$client_y&&(t.properties.$client_y=e.$client_y),e.$element_class_name&&(t.properties.$element_class_name=e.$element_class_name),e.$element_content&&(t.properties.$element_content=e.$element_content),e.$element_selector&&(t.properties.$element_selector=e.$element_selector),e.$element_path&&(t.properties.$element_path=e.$element_path));var i=this.store.commProperties;Object.assign(t.properties,this.parseProperties(i));var a=i&&(i.$user_register_time||i.$userRegisterTime);if(t.properties.$register_less_24h=a&&1e3*a+864e5>r?1:0,Array.isArray(e.commPropertieGroup)){var u,c=le(e.commPropertieGroup);try{for(c.s();!(u=c.n()).done;){var l=u.value,f=this.store.commGroupProperties[l];f&&Object.assign(t.properties,this.parseProperties(f))}}catch(e){c.e(e)}finally{c.f()}}else if("string"==typeof e.commPropertieGroup){var p=this.store.commGroupProperties[e.commPropertieGroup];Object.assign(t.properties,this.parseProperties(p))}var h=this.pageProperties[this.systemInfoGetter.getUrl()]||{};Object.assign(t.properties,this.parseProperties(h));var d=e.customProperties;return Object.assign(t.properties,this.parseProperties(d)),this.inviteTime&&(this.inviteUserId||this.inviteDistinctId)&&(t.properties.$invite_time=this.inviteTime,this.inviteUserId&&(t.properties.$invite_user_id=this.inviteUserId),this.inviteDistinctId&&(t.properties.$invite_distinct_id=this.inviteDistinctId)),this.store.lastVisitTime=t.event_time,this.store.firstVisitTime||(this.store.firstVisitTime=t.event_millitime,this.bufferStore()),this.formatTrackData(t)}},{key:"formatTrackData",value:function(e){if(!this.format)return e;for(var t in e){var r=e[t],n=this.format(t);e[n]=R(r)?this.formatTrackData(r):r,n!==t&&delete e[t]}return e}},{key:"parseProperties",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",r={},n=t?W(t)+"_":"";if(R(e)&&!Array.isArray(e))for(var o in e)P.includes(o)?Q(r,o,e[o]):!q(e[o])&&Object.assign(r,this.parseProperties(e[o],n+(/^(\$|[a-z]|_|\d)+$/gi.test(o)?o:K(o))));else q(e)||(r[W(t)]=e);return r}},{key:"getIsFirstDay",value:function(e){return!this.store.firstVisitTime||new Date(this.store.firstVisitTime).getDate()===new Date(e).getDate()}},{key:"onBeforeLaunch",value:function(){this.cycleProperties={}}},{key:"onLaunch",value:function(){var e=this;this.dataPoll.modifyAny((function(t){e.appendLaunchQuery(t)}))}},{key:"onPageLoad",value:function(){var e=this.systemInfoGetter.getOnLoadQuery();e.dc_st&&(e.dc_uid||e.dc_did)&&(this.inviteUserId=e.dc_uid,this.inviteDistinctId=e.dc_did,this.inviteTime=e.dc_st),(e.dc_acp||e.id)&&this.appendCycleProperties({activityId:e.dc_acp,goodsId:e.id});var t=this.systemInfoGetter.getOnLoadQuery(),r={};for(var n in t)(t[n]&&"undefined"!==t[n]||0===t[n])&&(r["onload_"+W(n)]=t[n]);this.appendPageProperties(r)}},{key:"onUnload",value:function(){this.storageHandler.set(de,this.store)}},{key:"onBeforeShow",value:function(){if(ve!==t&&ve!==e){var r=getCurrentPages(),n=r.length;if(n&&n>1){var o=r[n-1],i=r[n-2];o&&i&&o.route===i.route&&o.options===i.options&&(this.pageProperties[this.systemInfoGetter.getUrl()]=this.bufferPageProperties)}}else this.systemInfoGetter.getUrl()===this.preUrl&&(this.pageProperties[this.systemInfoGetter.getUrl()]=this.bufferPageProperties)}},{key:"onPageHide",value:function(){this.preUrl=this.systemInfoGetter.getUrl()}},{key:"onAfterPageHide",value:function(){this.bufferPageProperties=this.pageProperties[this.systemInfoGetter.getUrl()]||{}}},{key:"onAfterPageUnload",value:function(){this.pageProperties[this.systemInfoGetter.getUrl()]&&delete this.pageProperties[this.systemInfoGetter.getUrl()]}},{key:"identifySource",value:function(e){e.id&&(this.store.sourceDistinctId=e.id,this.store.latestTrafficSourceType=e.latestTrafficSourceType,this.store.latestReferrer=e.latestReferrer,this.bufferStore())}},{key:"clearSource",value:function(){delete this.store.sourceDistinctId,this.bufferStore()}},{key:"clearLatestTrafficSource",value:function(){delete this.store.latestTrafficSourceType,delete this.store.latestReferrer,this.bufferStore()}},{key:"appendCommProperties",value:function(e,t){var r=this;if(t)for(var n in e){var o=this.store.commGroupProperties[t]||{};o[n]=e[n],this.store.commGroupProperties[t]=o}else for(var i in e)this.store.commProperties[i]=e[i];!t&&this.dataPoll.modifyAny((function(t){Object.assign(t.properties,r.parseProperties(e))})),this.bufferStore()}},{key:"replaceCommProperties",value:function(e,t){t?this.store.commGroupProperties[t]&&(this.store.commGroupProperties[t]={}):this.store.commProperties={},this.appendCommProperties(e,t)}},{key:"removeCommProperties",value:function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];if(0!==t.findIndex((function(e){return"string"!=typeof e})))for(var n=0,o=t;n<o.length;n++){var i,a=o[n],s=le(a.value);try{for(s.s();!(i=s.n()).done;){var u=i.value;this.store.commGroupProperties[a.key]&&delete this.store.commGroupProperties[a.key][u]}}catch(e){s.e(e)}finally{s.f()}}else for(var c=0,l=t;c<l.length;c++){var f=l[c];delete this.store.commProperties[f]}this.bufferStore()}},{key:"removeCommPropertiesGroup",value:function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];for(var n=0,o=t;n<o.length;n++){var i=o[n];delete this.store.commGroupProperties[i]}this.bufferStore()}},{key:"appendPageProperties",value:function(e){var t=this,r=this.systemInfoGetter.getUrl();this.pageProperties[this.systemInfoGetter.getUrl()]||(this.pageProperties[this.systemInfoGetter.getUrl()]={}),Object.assign(this.pageProperties[this.systemInfoGetter.getUrl()],e),this.dataPoll.modifyAny((function(n){n.properties.$url===r&&Object.assign(n.properties,t.parseProperties(e))}))}},{key:"removePageProperties",value:function(){if(this.pageProperties[this.systemInfoGetter.getUrl()]){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];for(var n in t)delete this.pageProperties[this.systemInfoGetter.getUrl()][n]}}},{key:"getCommProperties",value:function(e,t){var r=t?this.store.commGroupProperties[t]||{}:this.store.commProperties;return r[e]||r[W(e)]||r[function(){return(arguments.length>0&&void 0!==arguments[0]?arguments[0]:"").replace(/\_(\w)/g,(function(e,t){return t.toUpperCase()}))}(e)]}},{key:"appendCommPropertiesToPage",value:function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];for(var n=0,o=t||[];n<o.length;n++){var i=o[n],a=this.store.commGroupProperties[i]||{};this.appendPageProperties(a)}}},{key:"appendCycleProperties",value:function(e){for(var t in e)this.cycleProperties[t]=e[t]}},{key:"removeCycleProperties",value:function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];for(var n in t)delete this.cycleProperties[n]}}],o&&pe(n.prototype,o),Object.defineProperty(n,"prototype",{writable:!1}),r}();function me(e){return me="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},me(e)}function be(){be=function(){return e};var e={},t=Object.prototype,r=t.hasOwnProperty,n="function"==typeof Symbol?Symbol:{},o=n.iterator||"@@iterator",i=n.asyncIterator||"@@asyncIterator",a=n.toStringTag||"@@toStringTag";function s(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{s({},"")}catch(e){s=function(e,t,r){return e[t]=r}}function u(e,t,r,n){var o=t&&t.prototype instanceof f?t:f,i=Object.create(o.prototype),a=new T(n||[]);return i._invoke=function(e,t,r){var n="suspendedStart";return function(o,i){if("executing"===n)throw new Error("Generator is already running");if("completed"===n){if("throw"===o)throw i;return{value:void 0,done:!0}}for(r.method=o,r.arg=i;;){var a=r.delegate;if(a){var s=w(a,r);if(s){if(s===l)continue;return s}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if("suspendedStart"===n)throw n="completed",r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n="executing";var u=c(e,t,r);if("normal"===u.type){if(n=r.done?"completed":"suspendedYield",u.arg===l)continue;return{value:u.arg,done:r.done}}"throw"===u.type&&(n="completed",r.method="throw",r.arg=u.arg)}}}(e,r,a),i}function c(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}e.wrap=u;var l={};function f(){}function p(){}function h(){}var d={};s(d,o,(function(){return this}));var y=Object.getPrototypeOf,v=y&&y(y(k([])));v&&v!==t&&r.call(v,o)&&(d=v);var g=h.prototype=f.prototype=Object.create(d);function m(e){["next","throw","return"].forEach((function(t){s(e,t,(function(e){return this._invoke(t,e)}))}))}function b(e,t){function n(o,i,a,s){var u=c(e[o],e,i);if("throw"!==u.type){var l=u.arg,f=l.value;return f&&"object"==me(f)&&r.call(f,"__await")?t.resolve(f.__await).then((function(e){n("next",e,a,s)}),(function(e){n("throw",e,a,s)})):t.resolve(f).then((function(e){l.value=e,a(l)}),(function(e){return n("throw",e,a,s)}))}s(u.arg)}var o;this._invoke=function(e,r){function i(){return new t((function(t,o){n(e,r,t,o)}))}return o=o?o.then(i,i):i()}}function w(e,t){var r=e.iterator[t.method];if(void 0===r){if(t.delegate=null,"throw"===t.method){if(e.iterator.return&&(t.method="return",t.arg=void 0,w(e,t),"throw"===t.method))return l;t.method="throw",t.arg=new TypeError("The iterator does not provide a 'throw' method")}return l}var n=c(r,e.iterator,t.arg);if("throw"===n.type)return t.method="throw",t.arg=n.arg,t.delegate=null,l;var o=n.arg;return o?o.done?(t[e.resultName]=o.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,l):o:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,l)}function P(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function S(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function T(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(P,this),this.reset(!0)}function k(e){if(e){var t=e[o];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var n=-1,i=function t(){for(;++n<e.length;)if(r.call(e,n))return t.value=e[n],t.done=!1,t;return t.value=void 0,t.done=!0,t};return i.next=i}}return{next:O}}function O(){return{value:void 0,done:!0}}return p.prototype=h,s(g,"constructor",h),s(h,"constructor",p),p.displayName=s(h,a,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===p||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,h):(e.__proto__=h,s(e,a,"GeneratorFunction")),e.prototype=Object.create(g),e},e.awrap=function(e){return{__await:e}},m(b.prototype),s(b.prototype,i,(function(){return this})),e.AsyncIterator=b,e.async=function(t,r,n,o,i){void 0===i&&(i=Promise);var a=new b(u(t,r,n,o),i);return e.isGeneratorFunction(r)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},m(g),s(g,a,"Generator"),s(g,o,(function(){return this})),s(g,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=[];for(var r in e)t.push(r);return t.reverse(),function r(){for(;t.length;){var n=t.pop();if(n in e)return r.value=n,r.done=!1,r}return r.done=!0,r}},e.values=k,T.prototype={constructor:T,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(S),!e)for(var t in this)"t"===t.charAt(0)&&r.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function n(r,n){return a.type="throw",a.arg=e,t.next=r,n&&(t.method="next",t.arg=void 0),!!n}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],a=i.completion;if("root"===i.tryLoc)return n("end");if(i.tryLoc<=this.prev){var s=r.call(i,"catchLoc"),u=r.call(i,"finallyLoc");if(s&&u){if(this.prev<i.catchLoc)return n(i.catchLoc,!0);if(this.prev<i.finallyLoc)return n(i.finallyLoc)}else if(s){if(this.prev<i.catchLoc)return n(i.catchLoc,!0)}else{if(!u)throw new Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return n(i.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=e,a.arg=t,i?(this.method="next",this.next=i.finallyLoc,l):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),l},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),S(r),l}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if("throw"===n.type){var o=n.arg;S(r)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,r){return this.delegate={iterator:k(e),resultName:t,nextLoc:r},"next"===this.method&&(this.arg=void 0),l}},e}function we(e,t,r,n,o,i,a){try{var s=e[i](a),u=s.value}catch(e){return void r(e)}s.done?t(u):Promise.resolve(u).then(n,o)}function Pe(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function Se(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function Te(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var ke="datapoll",Oe="destroySnapshot",_e=function(){function e(t){var r=t.storageHandler;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),Te(this,"list",{}),Te(this,"index",0),Te(this,"atWorks",{}),Te(this,"atLeisures",[]),Te(this,"storageHandler",void 0),Te(this,"systemInfoGetter",void 0),Te(this,"paraStore",{open_id:"",distinct_id:"",union_id:"",user_id:"",initial_id:"",universal_id:"",commProperties:{},commGroupProperties:{}}),Te(this,"isTrySave",!0),Te(this,"trySaveTime",4e3),Te(this,"storeEtag",""),Te(this,"requestHandler",void 0),Te(this,"showLog",void 0),Te(this,"universalIdUrl",void 0),this.storageHandler=r,this.initList()}var t,r,n,o;return t=e,r=[{key:"init",value:function(e){this.systemInfoGetter=e.systemInfoGetter,e.unloadListener.register(this),this.trySave(e),e.autoTrack.appHide&&this.checkDestroySnapshot(),this.requestHandler=e.requestHandler,this.showLog=e.showLog,this.paraStore=e.store,this.universalIdUrl=e.universalIdUrl}},{key:"trySave",value:function(e){var t=this;setInterval((function(){if(t.isTrySave&&(t.bufferDataPoll(),e.autoTrack.appHide&&t.destroySnapshot(e)),t.atLeisures.length>100&&t.requestHandler){var r=t.getList(),n=r.keys,o=r.list;t.showLog&&console.log("上报数据",o);var i=t.requestHandler.send(o);i&&i.then((function(){t.remove(n)})).catch((function(e){t.reset(n),t.showLog&&console.log("上报失败",e)}))}}),this.trySaveTime)}},{key:"destroySnapshot",value:function(e){var t=e.getTrackData({eventType:c,key:"$PageLeave",$event_duration:this.systemInfoGetter?this.systemInfoGetter.getPageDurationTime():0}),r=e.getTrackData({key:f,eventType:f,$event_duration:this.systemInfoGetter?this.systemInfoGetter.getTimestamp()-this.systemInfoGetter.getLaunchTime():0});this.storageHandler.set(Oe,[t,r])}},{key:"checkDestroySnapshot",value:function(){var e=this.storageHandler.get(Oe);e&&this.append(e)}},{key:"initList",value:function(){var e=this.storageHandler.get(ke);e&&(this.list=e.list,this.index=e.index,this.atWorks=e.atWorks,this.atLeisures=e.atLeisures)}},{key:"onUnload",value:function(){this.remove(Object.keys(this.atWorks)),this.bufferDataPoll(),this.storageHandler.remove(Oe)}},{key:"bufferDataPoll",value:function(){var e=this.getEtag();e!=this.storeEtag&&(this.storageHandler.set(ke,{list:this.list,index:this.index,atWorks:this.atWorks,atLeisures:this.atLeisures}),this.storeEtag=e)}},{key:"getEtag",value:function(){var e="";for(var t in this.atWorks)e+=t+",";return e+="|",this.atLeisures.forEach((function(t){return e+=t+","})),e}},{key:"append",value:function(e){if(!(Object.keys(this.list).length>1e3)){var t,r=function(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=function(e,t){if(e){if("string"==typeof e)return Pe(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Pe(e,t):void 0}}(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var n=0,o=function(){};return{s:o,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,a=!0,s=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return a=e.done,e},e:function(e){s=!0,i=e},f:function(){try{a||null==r.return||r.return()}finally{if(s)throw i}}}}(Array.isArray(e)?e:[e]);try{for(r.s();!(t=r.n()).done;){var n=t.value;this.list[this.index]=n,this.atLeisures.push(this.index),this.index++}}catch(e){r.e(e)}finally{r.f()}}}},{key:"getList",value:function(){for(var e={keys:[],list:[]},t=0;t<100;t++){var r=this.atLeisures.shift();if(void 0===r)break;e.keys.push(r),e.list.push(this.list[r]),this.atWorks[r]=1}return e}},{key:"remove",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];t.forEach((function(t){delete e.atWorks[t],delete e.list[t]}))}},{key:"reset",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];t.forEach((function(t){delete e.atWorks[t],e.list[t]&&e.atLeisures.unshift(Number(t))}))}},{key:"modifyAny",value:function(e){for(var t in this.list){var r=this.list[t];this.systemInfoGetter&&r.event_millitime>=this.systemInfoGetter.getLaunchTime()&&e(r)}}},{key:"modifyDisctincId",value:function(e){for(var t in this.list){var r=this.list[t];r.distinct_id=e,r.open_id=e}}},{key:"modifyDisctincUnionId",value:function(e){var t=e.openid,r=e.unionid;for(var n in this.list){var o=this.list[n];r&&(o.distinct_id=r),o.open_id=t,o.union_id=r}}},{key:"modifyUserId",value:function(e){for(var t in this.list)this.list[t].user_id=e}},{key:"updateUniversalId",value:(n=be().mark((function e(t,r){var n,o,i,a,s,u,c,l,f;return be().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(e.prev=0,o=this.storageHandler.get(de)||{},i=o.universal_id,a=null===(n=this.systemInfoGetter)||void 0===n?void 0:n.getTerminial(),this.universalIdUrl&&a&&t){e.next=5;break}return e.abrupt("return",i);case 5:if(i&&!r){e.next=14;break}return s={channel_type:a,channel_user_id:t},r&&(s.user_id=Number(r)),e.next=10,this.requestHandler.getUniversalId(this.universalIdUrl,s);case 10:u=e.sent,c=u.data.data.universal_id,this.paraStore.universal_id=c,i=c;case 14:for(l in this.list)(f=this.list[l]).universal_id&&"$Login"!==f.event_name||(f.universal_id=i);return e.abrupt("return",i);case 18:e.prev=18,e.t0=e.catch(0),console.error(e.t0);case 21:case"end":return e.stop()}}),e,this,[[0,18]])})),o=function(){var e=this,t=arguments;return new Promise((function(r,o){var i=n.apply(e,t);function a(e){we(i,r,o,a,s,"next",e)}function s(e){we(i,r,o,a,s,"throw",e)}a(void 0)}))},function(e,t){return o.apply(this,arguments)})}],r&&Se(t.prototype,r),Object.defineProperty(t,"prototype",{writable:!1}),e}();function Le(e){return Le="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Le(e)}function xe(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Ee(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function Ae(e,t){return Ae=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},Ae(e,t)}function Ce(e,t){if(t&&("object"===Le(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return Ie(e)}function Ie(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function je(e){return je=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},je(e)}function Be(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var Re=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Ae(e,t)}(a,e);var t,r,n,o,i=(n=a,o=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}(),function(){var e,t=je(n);if(o){var r=je(this).constructor;e=Reflect.construct(t,arguments,r)}else e=t.apply(this,arguments);return Ce(this,e)});function a(){var e;xe(this,a);for(var t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];return Be(Ie(e=i.call.apply(i,[this].concat(r))),"isTrySave",!1),e}return t=a,(r=[{key:"checkDestroySnapshot",value:function(){var e,t=this.storageHandler.get(Oe);t&&(document.referrer?document.referrer.includes(location.hostname)&&this.append(t):null!==(e=this.systemInfoGetter)&&void 0!==e&&e.getReferrer()||this.append(t))}}])&&Ee(t.prototype,r),Object.defineProperty(t,"prototype",{writable:!1}),a}(_e);function De(e){return De="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},De(e)}function Me(){return Me="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(e,t,r){var n=Ue(e,t);if(n){var o=Object.getOwnPropertyDescriptor(n,t);return o.get?o.get.call(arguments.length<3?e:r):o.value}},Me.apply(this,arguments)}function Ue(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=$e(e)););return e}function Ne(e,t){return Ne=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},Ne(e,t)}function Ge(e,t){if(t&&("object"===De(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return He(e)}function He(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function $e(e){return $e=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},$e(e)}function Fe(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function Ve(e,t,r){return t&&Fe(e.prototype,t),r&&Fe(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function We(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Xe(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var qe=Ve((function e(){We(this,e),Xe(this,"distinct_id",""),Xe(this,"open_id",""),Xe(this,"commProperties",{}),Xe(this,"commGroupProperties",{}),Xe(this,"$latest_traffic_source_type",void 0),Xe(this,"$latest_referrer",void 0)})),Ke=M(),ze=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Ne(e,t)}(i,e);var t,r,n=(t=i,r=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}(),function(){var e,n=$e(t);if(r){var o=$e(this).constructor;e=Reflect.construct(n,arguments,o)}else e=n.apply(this,arguments);return Ge(this,e)});function i(e){var t;return We(this,i),Xe(He(t=n.call(this,e)),"store",new qe),Xe(He(t),"suddenChangeListener",void 0),Xe(He(t),"sysAppListener",void 0),Xe(He(t),"sysVueComponentListener",void 0),Xe(He(t),"sysPageListener",void 0),Xe(He(t),"autoTrack",{appLaunch:!0,appHide:!0,pageShow:!0,pageLeave:!0,pageClick:!0,pageStay:!0,pageShare:!0,pageExposure:!0,pageAppear:!0}),t.sysAppListener=e.sysAppListener,t.sysVueComponentListener=e.sysVueComponentListener,t.sysPageListener=e.sysPageListener,t.tabItemListener=e.tabItemListener,t.suddenChangeListener=e.suddenChangeListener,t}return Ve(i,[{key:"init",value:function(e){var t,r,n;Me($e(i.prototype),"init",this).call(this,e),Ke===o&&(null===(t=this.sysVueComponentListener)||void 0===t||t.init(this),null===(r=this.sysPageListener)||void 0===r||r.init(this),null===(n=this.tabItemListener)||void 0===n||n.init(this));var a=this.systemInfoGetter;this.store.$latest_traffic_source_type&&-1!==a.getReferrer().indexOf(a.getHost())||(this.store.$latest_traffic_source_type=a.getLatestTrafficSourceType(),this.store.$latest_referrer=a.getReferrer()),this.suddenChangeListener.init(this)}},{key:"getTrackData",value:function(e){var t=Me($e(i.prototype),"getTrackData",this).call(this,e),r=this.systemInfoGetter;return t.properties.$title=r.getTitle(e.key),t.properties.$referrer=r.getReferrer(),t.properties.$url_path=r.getUrlPath(),t.properties.$user_agent=r.getUserAgent(),t.properties.page_list=r.getPageList(),t.properties.position_list=r.getPositionList(),Me($e(i.prototype),"formatTrackData",this).call(this,t)}}]),i}(ge);function Je(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}var Qe,Ye=function(){function e(){var t,r;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),r=[],(t="observers")in this?Object.defineProperty(this,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):this[t]=r}var t,r;return t=e,(r=[{key:"register",value:function(e){this.observers.push(e)}},{key:"remove",value:function(e){var t=this.observers.findIndex((function(t){return t===e}));-1!=t&&this.observers.splice(t,1)}},{key:"notify",value:function(){console.warn("监听器通知方法需要重构")}}])&&Je(t.prototype,r),Object.defineProperty(t,"prototype",{writable:!1}),e}();function Ze(e){return Ze="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Ze(e)}function et(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function rt(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function nt(e,t){return nt=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},nt(e,t)}function ot(e,t){if(t&&("object"===Ze(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return it(e)}function it(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function at(e){return at=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},at(e)}function st(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}!function(e){e[e.show=0]="show",e[e.hide=1]="hide"}(Qe||(Qe={}));var ut=M(),ct=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&nt(e,t)}(s,e);var t,r,n,i,a=(n=s,i=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}(),function(){var e,t=at(n);if(i){var r=at(this).constructor;e=Reflect.construct(t,arguments,r)}else e=t.apply(this,arguments);return ot(this,e)});function s(){var e;et(this,s);for(var t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];return st(it(e=a.call.apply(a,[this].concat(r))),"isTrackSinglePage",!1),st(it(e),"waterTapNotifyShow",N(e.notifyShow,100,it(e))),st(it(e),"waterTapNotifyHide",N(e.notifyHide,100,it(e))),e}return t=s,r=[{key:"init",value:function(e){var t,r=this;this.isTrackSinglePage=e.isTrackSinglePage,this.isTrackSinglePage?ut===o?(null===(t=e.sysPageListener)||void 0===t||t.register(this),setTimeout((function(){r.addSinglePageEvent()}),100)):(this.waterTapNotifyShow(),setTimeout((function(){r.addSinglePageEvent()}),100)):(window.addEventListener("load",(function(){r.waterTapNotifyShow()})),window.addEventListener("beforeunload",(function(){r.waterTapNotifyHide()}),!1))}},{key:"timerWaterTapNotifyShow",value:function(){var e=this;ut!==o&&setTimeout((function(){e.waterTapNotifyShow()}),0)}},{key:"addSinglePageEvent",value:function(){var e=this,t=window.history.pushState,r=window.history.replaceState;G(window.history.pushState)&&(window.history.pushState=function(){e.waterTapNotifyHide();for(var r=arguments.length,n=new Array(r),o=0;o<r;o++)n[o]=arguments[o];t.call.apply(t,[window.history].concat(n)),e.timerWaterTapNotifyShow()}),G(window.history.replaceState)&&(window.history.replaceState=function(){e.waterTapNotifyHide();for(var t=arguments.length,n=new Array(t),o=0;o<t;o++)n[o]=arguments[o];r.call.apply(r,[window.history].concat(n)),e.timerWaterTapNotifyShow()})}},{key:"onPage",value:function(e){this.listenerPageOnShow(e)}},{key:"listenerPageOnShow",value:function(e){var t=e.onShow||function(){},r=this;e.onShow=function(){try{r.waterTapNotifyShow()}catch(e){console.warn("UniappPageListener -> listenerPageOnShow",e)}t.call.apply(t,[this].concat(Array.prototype.slice.call(arguments)))}}},{key:"notifyShow",value:function(){this.notify(Qe.show)}},{key:"notifyHide",value:function(){this.notify(Qe.hide)}},{key:"notify",value:function(e){try{this.observers.forEach((function(t){e===Qe.show&&t.onBeforeShow&&t.onBeforeShow(),e===Qe.hide&&t.onBeforeHide&&t.onBeforeHide()})),this.observers.forEach((function(t){e===Qe.show&&t.onPageShow&&t.onPageShow(),e===Qe.hide&&t.onPageHide&&t.onPageHide()})),this.observers.forEach((function(t){e===Qe.show&&t.onAfterPageShow&&t.onAfterPageShow(),e===Qe.hide&&t.onAfterPageHide&&t.onAfterPageHide()}))}catch(e){console.warn("JsPageListener -> notify",e)}}}],r&&rt(t.prototype,r),Object.defineProperty(t,"prototype",{writable:!1}),s}(Ye);function lt(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function ft(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?lt(Object(r),!0).forEach((function(t){ht(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):lt(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function pt(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function ht(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var dt=M(),yt="DC_LAUNCH_QUERY",vt=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),ht(this,"isTrackSinglePage",void 0),ht(this,"trackUrlMap",void 0),ht(this,"pageTitle",void 0),ht(this,"preUrl",void 0),ht(this,"tempUrl",void 0),ht(this,"launchTime",Date.now()),ht(this,"fingerprintId",""),ht(this,"para",void 0),ht(this,"_detainedTime",void 0),ht(this,"_duration_time",0),this._detainedTime=Date.now()}var t,r;return t=e,r=[{key:"init",value:function(e){this.para=e,this.isTrackSinglePage=e.isTrackSinglePage,this.trackUrlMap=e.trackUrlMap,e.pageListener.register(this),e.launchListener.register(this),window.addEventListener("mousemove",N(this.beDetained,100,this),!1)}},{key:"generateRandomId",value:function(){var e=this;return this.fingerprintId||new Promise((function(e){L().get((function(t){var r=t.map((function(e,t){return 0===t?e.value.replace(/\bNetType\/\w+\b/,""):e.value})),n=L().x64hash128(r.join(""),31);e(n)}))})).then((function(t){e.fingerprintId=t,e.para&&e.para.identify(t),e.para&&e.para.dataPoll.modifyAny((function(e){e.distinct_id=t}))})),this.fingerprintId||function(){var e="";try{e=((e=21)=>{let t="",r=crypto.getRandomValues(new Uint8Array(e));for(;e--;){let n=63&r[e];t+=n<36?n.toString(36):n<62?(n-26).toString(36).toUpperCase():n<63?"_":"-"}return t})()}catch(t){console.warn("不支持nanoid生成"),e=D()}return e}()}},{key:"getTerminial",value:function(){return dt===o?"h5.mini":/Android|webOS|iPhone|iPod|BlackBerry/i.test(navigator.userAgent)?"wap":"pc"}},{key:"getViewportWidth",value:function(){return window.innerWidth||document.documentElement.clientWidth||document.body.clientWidth||0}},{key:"getViewportHeight",value:function(){return window.innerHeight||document.documentElement.clientHeight||document.body.clientHeight||0}},{key:"getScreenHeight",value:function(e){return e?e.scrollHeight:document.documentElement.scrollHeight||document.body.scrollHeight||0}},{key:"getScrollTop",value:function(e){return e?e.scrollTop:document.documentElement.scrollTop||document.body.scrollHeight||0}},{key:"getLatestTrafficSourceType",value:function(){var e=window.location.href,t=document.referrer;return/(utm_source|utm_medium|utm_campaign|utm_content|utm_term)\=[^&]+/.test(e)?"付费广告流量":/(www\.baidu\.|m\.baidu\.|m\.sm\.cn|so\.com|sogou\.com|youdao\.com|google\.|yahoo.com\/|bing.com\/|ask.com\/)/.test(t)?"自然搜索流量":/(weibo\.com|renren\.com|kaixin001\.com|douban\.com|qzone\.qq\.com|zhihu\.com|tieba\.baidu\.com|weixin\.qq\.com)/.test(t)?"社交网站流量":t&&e!==t?"":"直接流量"}},{key:"getLatestReferrer",value:function(){return this.preUrl||""}},{key:"getUrl",value:function(){return dt===o?this.getUrlPath():window.location.href}},{key:"getHost",value:function(){return window.location.host}},{key:"getReferrer",value:function(){return this.isTrackSinglePage?this.preUrl||"":document.referrer}},{key:"getUrlPath",value:function(){if(dt===o){var e=(i=window.location.href,""!==(a=window.location.hash)&&"#"!==a&&-1!==i.indexOf(a)?"hash":"pushState"in window.history&&"replaceState"in window.history?"history":"unknown"),t=getCurrentPages(),r=t.length&&t[t.length-1].route,n="";return"hash"===e?n=window.location.hash.slice(2).split("?")[0]:"history"===e&&(n=window.location.pathname.slice(1).split("?")[0]),!n&&r&&(n=r),this.trackUrlMap&&this.trackUrlMap[n]&&(n=this.trackUrlMap[n]),n}var i,a;return document.location.pathname}},{key:"getTitle",value:function(e){return this.isTrackSinglePage&&e===c&&this.pageTitle||document.title}},{key:"getDomIndex",value:function(e){if(!e.parentNode)return-1;for(var t=0,r=e.tagName,n=e.parentNode.children,o=0;o<n.length;o++)if(n[o].tagName===r){if(e===n[o])return t;t++}return-1}},{key:"selector",value:function(e){var t=e.parentNode&&9==e.parentNode.nodeType?-1:this.getDomIndex(e);return e.tagName.toLowerCase()+(~t?":nth-of-type("+(t+1)+")":"")}},{key:"getDomSelector",value:function(e,t){if(!e||!e.parentNode||!e.parentNode.children)return"";t=t&&Array.isArray(t)?t:[];var r=e.nodeName.toLowerCase();return e&&"body"!==r&&1==e.nodeType?(t.unshift(this.selector(e)),this.getDomSelector(e.parentNode,t)):(t.unshift("body"),t.join(" > "))}},{key:"getElementPath",value:function(e){for(var t=[];e.parentNode;){if(e===document.body){t.unshift("body");break}t.unshift(e.tagName.toLowerCase()),e=e.parentNode}return t.join(" > ")}},{key:"_composedPath",value:function(e){if(e.path)return e.path;var t=e.target;for(e.path=[];null!==t.parentNode;)e.path.push(t),t=t.parentNode;return e.path.push(document,window),e.path}},{key:"getClickReportIncident",value:function(e){e.path||(e.path=this._composedPath(e));for(var t,r=e.target,n={},i={},a=0;a<e.path.length;a++){var u=e.path[a].__uniDataset||{};"string"==typeof u.params&&(u.params=JSON.parse(u.params));var c=ft(ft({},e.path[a].dataset),u);for(var l in c){var f=/sensors_.+/.exec(l);f&&(n[W(f[0].slice(8))]=c[l]),i[l]=c[l]}}t=r.dataset.sensors?r.dataset.sensors:s;var p=z(i,"position_sign");return dt===o&&(getCurrentPages().slice(-1)[0].$lastPositionSign=p||""),p&&(n.positionSignId=p),{eventType:s,key:t,$element_type:r.tagName,$element_class_name:r.className,$element_content:r.textContent,$element_selector:this.getDomSelector(r),$element_path:this.getElementPath(r),$page_x:e.pageX,$page_y:e.pageY,$client_x:e.clientX,$client_y:e.clientY,customProperties:n}}},{key:"getExposureResportIncident",value:function(e){var t,r,n=e.target,o=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t={};for(var r in e){var n=/sensors_.+/.exec(r);if(n){var o=n[0].slice(8),i=W(o),a=e[r];P.includes(o)?Q(t,r,a):t[i]=a}}return t}(n.dataset);return e.target.classList.contains("sensors_exposure")&&(t=n.dataset.sensorsExposure||n.dataset.sensorsexposure,r="$PageExposure"),e.target.classList.contains("sensors_touch")&&(t=n.dataset.sensorsTouch||n.dataset.sensorstouch,r="$Touch"),{eventType:r,key:t||r,$element_type:n.tagName,$element_class_name:n.className,$element_content:n.textContent,$element_selector:this.getDomSelector(n),$element_path:this.getElementPath(n),customProperties:o}}},{key:"getReportIncident",value:function(e){return e.target&&(e.target.classList.contains("sensors_exposure")||e.target.classList.contains("sensors_touch"))?this.getExposureResportIncident(e):this.getClickReportIncident(e)}},{key:"getUserAgent",value:function(){return navigator.userAgent}},{key:"beDetained",value:function(){var e=Date.now();e-this._detainedTime<12e4&&(this._duration_time+=e-this._detainedTime),this._detainedTime=e}},{key:"getPageDurationTime",value:function(){return this._duration_time}},{key:"onBeforePageLoad",value:function(){var e=this.getOnLoadQuery().statistical_from,t=this.getLaunchQuery();e&&"undefined"!==e&&(t.statistical_from=e,window.sessionStorage.setItem(yt,JSON.stringify(t)))}},{key:"onBeforeLaunch",value:function(e){window.sessionStorage.setItem(yt,JSON.stringify(e))}},{key:"onBeforeShow",value:function(){this.isTrackSinglePage&&(this.pageTitle=document.title),this.tempUrl!==this.preUrl&&(this.preUrl=this.tempUrl);var e=Date.now();this._detainedTime=e,this._duration_time=0}},{key:"onBeforeHide",value:function(){this.tempUrl=this.getUrl()}},{key:"getLaunchQuery",value:function(){var e=window.sessionStorage.getItem(yt);return e?JSON.parse(e):{}}},{key:"getOnLoadQuery",value:function(){return J()}},{key:"getTimestamp",value:function(){return Date.now()}},{key:"getLaunchTime",value:function(){return this.launchTime}},{key:"getPageList",value:function(){var e=this;return dt!==o?[]:getCurrentPages().map((function(t){var r=t.route;return e.trackUrlMap&&e.trackUrlMap[r]&&(r=e.trackUrlMap[r]),r}))}},{key:"getPositionList",value:function(){if(dt!==o)return[];var e=0,t=getCurrentPages();return t.map((function(r,n){return r.$lastPositionSign?(e=n,r.$lastPositionSign):t[e]&&t[e].$lastPositionSign||""}))}}],r&&pt(t.prototype,r),Object.defineProperty(t,"prototype",{writable:!1}),e}();function gt(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}var mt="ThisDataIsTheObject|",bt=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e)}var t,r;return t=e,(r=[{key:"get",value:function(e){var t=window.localStorage.getItem(e);return t&&new RegExp("^"+mt).test(t)?JSON.parse(t.slice(mt.length)):t}},{key:"set",value:function(e,t){(R(t)||Array.isArray(t))&&(t=mt+JSON.stringify(t)),window.localStorage.setItem(e,t)}},{key:"remove",value:function(e){window.localStorage.removeItem(e)}}])&&gt(t.prototype,r),Object.defineProperty(t,"prototype",{writable:!1}),e}();function wt(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function Pt(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var St=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),Pt(this,"serverUrl",""),Pt(this,"needCookies",!1),Pt(this,"dataSendTimeout",3e3)}var t,r;return t=e,r=[{key:"encryption",value:function(e){return function(e){var t,r,n,o,i,a="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",s=0,u=0,c="",l=[];if(!e)return e;e=function(e){var t,r,n,o,i="";for(t=r=0,o=(e=(e+"").replace(/\r\n/g,"\n").replace(/\r/g,"\n")).length,n=0;n<o;n++){var a=e.charCodeAt(n),s=null;a<128?r++:s=a>127&&a<2048?String.fromCharCode(a>>6|192,63&a|128):String.fromCharCode(a>>12|224,a>>6&63|128,63&a|128),null!==s&&(r>t&&(i+=e.substring(t,r)),i+=s,t=r=n+1)}return r>t&&(i+=e.substring(t,e.length)),i}(e);do{t=(i=e.charCodeAt(s++)<<16|e.charCodeAt(s++)<<8|e.charCodeAt(s++))>>18&63,r=i>>12&63,n=i>>6&63,o=63&i,l[u++]=a.charAt(t)+a.charAt(r)+a.charAt(n)+a.charAt(o)}while(s<e.length);switch(c=l.join(""),e.length%3){case 1:c=c.slice(0,-2)+"==";break;case 2:c=c.slice(0,-1)+"="}return c}(encodeURIComponent(JSON.stringify(e)))}},{key:"sendBeacon",value:function(e,t){var r=navigator.sendBeacon(e,t);return r?Promise.resolve(r):Promise.reject(r)}},{key:"sendFetch",value:function(e,t){return fetch(e,{method:"POST",body:"data="+t,headers:{"Content-Type":"application/x-www-form-urlencoded"},credentials:"include",keepalive:!0}).then((function(e){if(e.ok)return e.json();throw new Error("请求错误")}))}},{key:"send",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=this.encryption(e),n=V(this.serverUrl,{},{data:r});if(t){var o=new Blob(["data=".concat(r)],{type:"application/x-www-form-urlencoded"});return this.needCookies&&"keepalive"in Request.prototype?this.sendFetch(n,r):this.sendBeacon(n,o)}return this.sendXhr(n,r)}},{key:"sendSing",value:function(e){return this.send([e])}},{key:"sendXhr",value:function(e,t){var r=this;return new Promise((function(n,o){var i=null;window.XMLHttpRequest?i=new window.XMLHttpRequest:window.ActiveXObject&&(i=new window.ActiveXObject("Microsoft.XMLHTTP"));var a=/https?:\/\/[^\/]+/.exec(e);(a&&a[0]===window.location.origin||r.needCookies)&&(i.withCredentials=!0),i.open("POST",e,!0),i.setRequestHeader("content-type","application/x-www-form-urlencoded"),i.onreadystatechange=function(){4===i.readyState&&(200===i.status?n(i.response):o({response:i.response,status:i.status,errorText:i.statusText}))},i.onabort=i.onerror=i.ontimeout=function(e){o({response:i.response,status:i.status,errorText:i.statusText})},i.send("data="+t);var s=setTimeout((function(){o({response:"",status:12,errorText:"请求超时"}),clearTimeout(s)}),r.dataSendTimeout)}))}},{key:"sendRequest",value:function(e,t){return e=V(e,{},{data:t=this.encryption(t)}),this.sendXhr(e,t)}},{key:"getUniversalId",value:function(e,t){var r=this.encryption(t);return e=V(e,{},{data:r}),this.sendXhr(e,r).then((function(e){return{data:JSON.parse(e)}}))}}],r&&wt(t.prototype,r),Object.defineProperty(t,"prototype",{writable:!1}),e}();function Tt(e){return Tt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Tt(e)}function kt(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Ot(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function _t(e,t){return _t=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},_t(e,t)}function Lt(e,t){if(t&&("object"===Tt(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}function xt(e){return xt=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},xt(e)}var Et=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&_t(e,t)}(a,e);var t,r,n,o,i=(n=a,o=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}(),function(){var e,t=xt(n);if(o){var r=xt(this).constructor;e=Reflect.construct(t,arguments,r)}else e=t.apply(this,arguments);return Lt(this,e)});function a(){return kt(this,a),i.apply(this,arguments)}return t=a,(r=[{key:"init",value:function(){var e=this;window.addEventListener("click",(function(t){e.notify(t)}),!0)}},{key:"notify",value:function(e){this.observers.forEach((function(t){return t.onClickChange(e)}))}}])&&Ot(t.prototype,r),Object.defineProperty(t,"prototype",{writable:!1}),a}(Ye);function At(e){return At="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},At(e)}function Ct(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function It(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function jt(e,t){return jt=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},jt(e,t)}function Bt(e,t){if(t&&("object"===At(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return Rt(e)}function Rt(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Dt(e){return Dt=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},Dt(e)}function Mt(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var Ut=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&jt(e,t)}(a,e);var t,r,n,o,i=(n=a,o=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}(),function(){var e,t=Dt(n);if(o){var r=Dt(this).constructor;e=Reflect.construct(t,arguments,r)}else e=t.apply(this,arguments);return Bt(this,e)});function a(){var e;Ct(this,a);for(var t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];return Mt(Rt(e=i.call.apply(i,[this].concat(r))),"debounceNotify",void 0),e}return t=a,(r=[{key:"init",value:function(e){var t=this;this.debounceNotify=U(this.notify,e.scrollDelayTime,this),this.resetInit(),window.addEventListener("scroll",(function(e){t.debounceNotify&&t.debounceNotify()})),!this.listenerScroll()&&window.addEventListener("load",(function(){t.listenerScroll()}))}},{key:"listenerScroll",value:function(){var e=this,t=document.querySelectorAll(".sensors-scroll");if(t.length)for(var r=function(r){var n=t[r];n.addEventListener("scroll",(function(t){e.debounceNotify&&e.debounceNotify(n)}))},n=0;n<t.length;n++)r(n);return t.length}},{key:"notify",value:function(e){this.observers.forEach((function(t){return t.onStayChange(e)}))}},{key:"resetInit",value:function(){this.debounceNotify&&this.debounceNotify()}}])&&It(t.prototype,r),Object.defineProperty(t,"prototype",{writable:!1}),a}(Ye);function Nt(e){return Nt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Nt(e)}function Gt(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Ht(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function $t(e,t){return $t=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},$t(e,t)}function Ft(e,t){if(t&&("object"===Nt(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}function Vt(e){return Vt=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},Vt(e)}var Wt=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&$t(e,t)}(a,e);var t,r,n,o,i=(n=a,o=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}(),function(){var e,t=Vt(n);if(o){var r=Vt(this).constructor;e=Reflect.construct(t,arguments,r)}else e=t.apply(this,arguments);return Ft(this,e)});function a(){return Gt(this,a),i.apply(this,arguments)}return t=a,(r=[{key:"init",value:function(e){var t=this;(e.isTrackSinglePage||e.isIFrame)&&window.addEventListener("unload",(function(){t.notify()}))}},{key:"notify",value:function(){this.observers.forEach((function(e){e.onUnload()}))}}])&&Ht(t.prototype,r),Object.defineProperty(t,"prototype",{writable:!1}),a}(Ye);function Xt(e){return Xt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Xt(e)}function qt(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Kt(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function zt(e,t){return zt=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},zt(e,t)}function Jt(e,t){if(t&&("object"===Xt(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}function Qt(e){return Qt=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},Qt(e)}var Yt="DC_LAUNCH",Zt=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&zt(e,t)}(a,e);var t,r,n,o,i=(n=a,o=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}(),function(){var e,t=Qt(n);if(o){var r=Qt(this).constructor;e=Reflect.construct(t,arguments,r)}else e=t.apply(this,arguments);return Jt(this,e)});function a(){return qt(this,a),i.apply(this,arguments)}return t=a,(r=[{key:"init",value:function(e){this.listenerOnLaunch()}},{key:"listenerOnLaunch",value:function(){if(!sessionStorage.getItem(Yt)){var e=J();this.notify("BEFORE",e),sessionStorage.setItem(Yt,Date.now().toString()),this.notify("AFTER",e)}}},{key:"notify",value:function(e,t){"BEFORE"===e&&this.observers.forEach((function(e){return e.onBeforeLaunch&&e.onBeforeLaunch(t)})),"AFTER"===e&&this.observers.forEach((function(e){return e.onLaunch&&e.onLaunch(t)}))}}])&&Kt(t.prototype,r),Object.defineProperty(t,"prototype",{writable:!1}),a}(Ye);function er(e){return er="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},er(e)}function tr(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function rr(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function nr(e,t){return nr=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},nr(e,t)}function or(e,t){if(t&&("object"===er(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return ir(e)}function ir(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function ar(e){return ar=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},ar(e)}function sr(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var ur=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&nr(e,t)}(a,e);var t,r,n,o,i=(n=a,o=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}(),function(){var e,t=ar(n);if(o){var r=ar(this).constructor;e=Reflect.construct(t,arguments,r)}else e=t.apply(this,arguments);return or(this,e)});function a(){var e;tr(this,a);for(var t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];return sr(ir(e=i.call.apply(i,[this].concat(r))),"observer",void 0),e}return t=a,(r=[{key:"init",value:function(e){e.pageListener.register(this),e.suddenChangeListener.register(this)}},{key:"onPageShow",value:function(){var e=this;setTimeout((function(){e.listenerExposure()}),0)}},{key:"onSuddenChange",value:function(e){this.observer.disconnect(),this.listenerExposure()}},{key:"listenerExposure",value:function(){var e=this;this.observer=new IntersectionObserver((function(t){for(var r=0;r<t.length;r++)if(t[r].isIntersecting){if(t[r].target.classList.contains("sensors_exposure_already"))return;e.notify(t[r]),t[r].target.classList.add("sensors_exposure_already")}}));for(var t=document.querySelectorAll(".sensors_exposure"),r=0;r<t.length;r++)this.observer.observe(t[r])}},{key:"notify",value:function(e){this.observers.forEach((function(t){return t.onExposure(e)}))}}])&&rr(t.prototype,r),Object.defineProperty(t,"prototype",{writable:!1}),a}(Ye);function cr(e){return cr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},cr(e)}function lr(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function fr(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function pr(e,t){return pr=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},pr(e,t)}function hr(e,t){if(t&&("object"===cr(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return dr(e)}function dr(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function yr(e){return yr=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},yr(e)}function vr(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var gr=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&pr(e,t)}(a,e);var t,r,n,o,i=(n=a,o=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}(),function(){var e,t=yr(n);if(o){var r=yr(this).constructor;e=Reflect.construct(t,arguments,r)}else e=t.apply(this,arguments);return hr(this,e)});function a(){var e;lr(this,a);for(var t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];return vr(dr(e=i.call.apply(i,[this].concat(r))),"isTrackSinglePage",!1),e}return t=a,(r=[{key:"init",value:function(e){e.pageListener.register(this),this.isTrackSinglePage=e.isTrackSinglePage}},{key:"onPageShow",value:function(){var e=this;setTimeout((function(){e.listenerMutation()}),0)}},{key:"onPageHide",value:function(){}},{key:"listenerMutation",value:function(){for(var e=this,t=new MutationObserver((function(t){e.notify(t)})),r=document.querySelectorAll(".sensors_mutations"),n=0;n<r.length;n++)t.observe(r[n],{childList:!0,subtree:!0})}},{key:"notify",value:function(e){this.observers.forEach((function(t){return t.onSuddenChange(e)}))}}])&&fr(t.prototype,r),Object.defineProperty(t,"prototype",{writable:!1}),a}(Ye);function mr(e){return mr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},mr(e)}function br(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function wr(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function Pr(e,t){return Pr=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},Pr(e,t)}function Sr(e,t){if(t&&("object"===mr(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}function Tr(e){return Tr=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},Tr(e)}var kr=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Pr(e,t)}(a,e);var t,r,n,o,i=(n=a,o=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}(),function(){var e,t=Tr(n);if(o){var r=Tr(this).constructor;e=Reflect.construct(t,arguments,r)}else e=t.apply(this,arguments);return Sr(this,e)});function a(){return br(this,a),i.apply(this,arguments)}return t=a,(r=[{key:"init",value:function(e){e.pageListener.register(this)}},{key:"onBeforeShow",value:function(){this.notify({})}},{key:"notify",value:function(e){this.observers.forEach((function(t){return t.onPageLoad&&t.onPageLoad(e)}))}}])&&wr(t.prototype,r),Object.defineProperty(t,"prototype",{writable:!1}),a}(Ye);function Or(e){return Or="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Or(e)}function _r(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Lr(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function xr(e,t){return xr=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},xr(e,t)}function Er(e,t){if(t&&("object"===Or(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}function Ar(e){return Ar=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},Ar(e)}var Cr=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&xr(e,t)}(a,e);var t,r,n,o,i=(n=a,o=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}(),function(){var e,t=Ar(n);if(o){var r=Ar(this).constructor;e=Reflect.construct(t,arguments,r)}else e=t.apply(this,arguments);return Er(this,e)});function a(){return _r(this,a),i.apply(this,arguments)}return t=a,(r=[{key:"init",value:function(e){e.pageListener.register(this)}},{key:"onAfterPageHide",value:function(){this.notify()}},{key:"notify",value:function(){this.observers.forEach((function(e){e.onPageUnload&&e.onPageUnload()})),this.observers.forEach((function(e){e.onAfterPageUnload&&e.onAfterPageUnload()}))}}])&&Lr(t.prototype,r),Object.defineProperty(t,"prototype",{writable:!1}),a}(Ye);function Ir(e){return Ir="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Ir(e)}function jr(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Br(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function Rr(e,t){return Rr=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},Rr(e,t)}function Dr(e,t){if(t&&("object"===Ir(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}function Mr(e){return Mr=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},Mr(e)}var Ur=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Rr(e,t)}(a,e);var t,r,n,o,i=(n=a,o=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}(),function(){var e,t=Mr(n);if(o){var r=Mr(this).constructor;e=Reflect.construct(t,arguments,r)}else e=t.apply(this,arguments);return Dr(this,e)});function a(){return jr(this,a),i.apply(this,arguments)}return t=a,(r=[{key:"init",value:function(e){}},{key:"notify",value:function(e){this.observers.forEach((function(t){return t.onShare(e)}))}}])&&Br(t.prototype,r),Object.defineProperty(t,"prototype",{writable:!1}),a}(Ye);function Nr(e){return Nr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Nr(e)}function Gr(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Hr(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function $r(e,t){return $r=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},$r(e,t)}function Fr(e,t){if(t&&("object"===Nr(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return Vr(e)}function Vr(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Wr(e){return Wr=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},Wr(e)}function Xr(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var qr=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&$r(e,t)}(a,e);var t,r,n,o,i=(n=a,o=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}(),function(){var e,t=Wr(n);if(o){var r=Wr(this).constructor;e=Reflect.construct(t,arguments,r)}else e=t.apply(this,arguments);return Fr(this,e)});function a(){var e;Gr(this,a);for(var t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];return Xr(Vr(e=i.call.apply(i,[this].concat(r))),"listenerEvents",[]),e}return t=a,(r=[{key:"init",value:function(e){e.pageListener.register(this)}},{key:"onPageShow",value:function(){var e=this;setTimeout((function(){e.listenerOnTouch()}),0)}},{key:"onPageHide",value:function(){this.listenerEvents.forEach((function(e){var t=e.target,r=e.event;t.removeEventListener("scroll",r)}))}},{key:"listenerScroll",value:function(e){var t=N(this.notify,1e3,this);this.listenerEvents.push({target:e,event:t}),e.addEventListener("scroll",(function(e){t(e)}))}},{key:"listenerOnTouch",value:function(){for(var e=document.querySelectorAll(".sensors_touch"),t=0;t<e.length;t++)this.listenerScroll(e[t])}},{key:"notify",value:function(e){this.observers.forEach((function(t){return t.onTouch(e)}))}}])&&Hr(t.prototype,r),Object.defineProperty(t,"prototype",{writable:!1}),a}(Ye);function Kr(e){return Kr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Kr(e)}function zr(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Jr(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function Qr(e,t){return Qr=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},Qr(e,t)}function Yr(e,t){if(t&&("object"===Kr(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}function Zr(e){return Zr=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},Zr(e)}var en=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Qr(e,t)}(a,e);var t,r,n,o,i=(n=a,o=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}(),function(){var e,t=Zr(n);if(o){var r=Zr(this).constructor;e=Reflect.construct(t,arguments,r)}else e=t.apply(this,arguments);return Yr(this,e)});function a(){return zr(this,a),i.apply(this,arguments)}return t=a,(r=[{key:"init",value:function(e){}}])&&Jr(t.prototype,r),Object.defineProperty(t,"prototype",{writable:!1}),a}(Ye);function tn(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}var rn=function(){function e(){var t,r;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),r=void 0,(t="para")in this?Object.defineProperty(this,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):this.para=r,this.para=null}var t,r;return t=e,(r=[{key:"track",value:function(e){}},{key:"init",value:function(e){}},{key:"login",value:function(e){}},{key:"identify",value:function(e){}},{key:"appendCommProperties",value:function(e){}},{key:"replaceCommProperties",value:function(e,t){}},{key:"removeCommProperties",value:function(){}},{key:"removeCommPropertiesGroup",value:function(){}},{key:"appendPageProperties",value:function(e){}},{key:"removePageProperties",value:function(){}},{key:"appendCommPropertiesToPage",value:function(){}},{key:"appendCycleProperties",value:function(e){}},{key:"removeCycleProperties",value:function(){}}])&&tn(t.prototype,r),Object.defineProperty(t,"prototype",{writable:!1}),e}(),nn=M(),on=new rn;if(nn===e)try{if(!window.sensors){var an=new bt,sn=new Re({storageHandler:an}),un=new St,cn=new kr,ln=new Cr,fn=new ct,pn=new Et,hn=new Ut,dn=new vt,yn=new Wt,vn=new Zt,gn=new gr,mn=new ur,bn=new en,wn=new Ur,Pn=new qr;on=new ue({para:new ze({dataPoll:sn,requestHandler:un,pageLoadListener:cn,pageUnloadListener:ln,pageListener:fn,clickListener:pn,stayListener:hn,systemInfoGetter:dn,storageHandler:an,launchListener:vn,unloadListener:yn,exposureListener:mn,appearListener:bn,suddenChangeListener:gn,shareListener:wn,touchListener:Pn}),dataPoll:sn,requestHandler:un,pageLoadListener:cn,pageListener:fn,clickListener:pn,stayListener:hn,systemInfoGetter:dn,launchListener:vn,unloadListener:yn,exposureListener:mn,appearListener:bn,touchListener:Pn,shareListener:wn}),window.sensors=on}on=window.sensors}catch(e){console.warn(e),window.sensors=on}const Sn=on})(),n.default})()}));