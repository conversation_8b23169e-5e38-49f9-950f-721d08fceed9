import SubjectListenerImpl from "@/runtime-core/subject-listener-impl";
import AlipayMiniSensorsParaImpl from "../alipay-mini-sensors-para-impl";

/**
 * 程序销毁监听器 支付宝小程序端
 */
export default class AlipayMiniUnloadListener extends SubjectListenerImpl<UnloadObserver> implements UnloadListener {
    init(data: AlipayMiniSensorsParaImpl) {
        my.onAppHide(() => {
            this.notify();
        });
    }
    notify() {
        this.observers.forEach(observer => observer.onUnload());
    }
}