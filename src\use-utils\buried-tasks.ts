/**
 * Step 类代表一个步骤，包含步骤的详细信息和状态管理
 */
export class Step {
  // 步骤的唯一键值
  key = '';
  // 步骤标题
  title = '';
  // 步骤执行的条件
  condition = '';
  // 步骤描述
  description = '';

  // 步骤当前状态：等待、处理中、完成
  status = 'wait';
  // 步骤前一个状态
  previousStatus = 'wait';
  // 步骤开始时间戳
  startTime = 0;
  // 步骤继续时间戳
  continueTime = 0;
  // 步骤持续时间，单位秒
  duration = 0;

  /**
   * 构造一个新的步骤实例
   * @param option 步骤的配置项，包括 key、title、condition 和 description
   */
  constructor(option: { key: string; title: string; condition: string; description: string }) {
    const { key, title, condition, description } = option;
    this.key = key;
    this.title = title;
    this.condition = condition;
    this.description = description;
  }

  /**
   * 初始化步骤状态
   */
  init() {
    this.status = 'wait';
    this.previousStatus = 'wait';
    this.startTime = 0;
    this.continueTime = 0;
    this.duration = 0;
  }

  /**
   * 改变步骤状态
   * @param status 新的状态，可以是 'process'、'finish' 或 'wait'
   */
  changeStatus(status: 'process' | 'finish' | 'wait') {
    this.previousStatus = this.status;
    this.status = status;
  }

  /**
   * 将步骤状态改为处理中
   */
  process() {
    if (this.status === 'process') {
      return;
    }

    if (this.status === 'wait') {
      this.startTime = Date.now();
    } else {
      this.continueTime = Date.now();
    }

    this.changeStatus('process');
  }

  /**
   * 将步骤状态改为完成
   */
  finish() {
    if (this.status !== 'process') {
      return;
    }

    const time = this.previousStatus === 'wait' ? this.startTime : this.continueTime;
    const endTime = Date.now();
    this.duration = this.duration + Math.floor((endTime - time) / 1000);

    this.changeStatus('finish');
  }
}

/**
 * Task 类代表一个任务，包含多个步骤和任务级的状态管理
 */
export class Task {
  // 任务当前状态：等待、处理中、完成
  status = 'wait';
  // 任务包含的步骤数组
  steps: Step[] = [];
  // 上报事件key
  trackKey = '';

  /**
   * 构造一个新的任务实例
   * @param option 任务的配置项，包括 steps 和 trackKey
   */
  constructor(option: { steps: Step[]; trackKey: string }) {
    const { steps, trackKey } = option;
    this.steps = steps;
    this.trackKey = trackKey;
  }

  /**
   * 初始化任务状态
   */
  init() {
    this.status = 'wait';
    this.steps.forEach(step => step.init());
  }

  /**
   * 开始任务
   */
  start() {
    this.init();
    this.status = 'process';
  }

  /**
   * 结束任务
   */
  end() {
    this.status = 'finish';
    this.trackDuration();
    this.init();
  }

  /**
   * 查找特定步骤并执行回调函数
   * @param stepKey 步骤的键值
   * @param cb 找到步骤后执行的回调函数
   */
  findStep(stepKey: string, cb: (step: Step) => void) {
    const step = this.steps.find(step => step.key === stepKey);
    step && cb(step);
  }

  /**
   * 将特定步骤的状态改为处理中
   * @param stepKey 步骤的键值
   */
  processStep(stepKey: string) {
    if (this.status !== 'process') {
      return;
    }

    this.findStep(stepKey, step => step.process());
  }

  /**
   * 将特定步骤的状态改为完成
   * @param stepKey 步骤的键值
   */
  finishStep(stepKey: string) {
    if (this.status !== 'process') {
      return;
    }

    this.findStep(stepKey, step => step.finish());
  }

  /**
   * 记录任务持续时间
   */
  trackDuration() {
    const durationData = this.steps.map(step => {
      const { key, title, condition, description, duration } = step;
      const formattedDuration = formatSeconds(duration);
      return { key, title, condition, description, duration, formattedDuration };
    });
    const platform = getPlatform()
    platform.sensors.track({
      key: this.trackKey,
      customProperties: {
        dynamicStrProps: {
          durationData: JSON.stringify(durationData),
        },
      },
    });
  }
}

/**
 * 格式化秒数为小时、分钟和秒的格式
 * @param seconds 输入的秒数
 * @returns 格式化后的时间字符串
 */
function formatSeconds(seconds: number) {
  const h = Math.floor(seconds / 3600);
  const m = Math.floor((seconds % 3600) / 60);
  const s = seconds % 60;

  return [h ? `${h}时` : null, m ? `${m}分` : null, s ? `${s}秒` : null].filter(Boolean).join(' ');
}

/**
 * @description 获取平台环境
 * @returns {object} 平台环境
 */
function getPlatform(): any {
  const isUniApp = typeof uni !== 'undefined' && uni !== null;
  const isAlipay = typeof my !== 'undefined' && my !== null;
  const isBrowser = typeof window !== 'undefined' && window !== null;

  let platform: any;

  if (isUniApp) {
    platform = uni;
  } else if (isAlipay) {
    platform = my;
  } else if (isBrowser) {
    platform = window;
  }

  if (!platform) {
    throw new Error('平台环境未定义，请检查运行环境');
  }

  return platform;
}