## **@rrzu/data-analysis**

**1.api以及具体使用方法可参照：**

[前端埋点指南](https://oxa3qz.yuque.com/oxa3qz/icl0kt/xdm092)

**2.指令说明**

sdk使用demo预览：

```bash
npm run serve
```

npm包发布：

```bash
npm run publish:data-analysis:latest //发布正式环境
npm run publish:data-analysis:beta //发布测试环境
```

构建支付宝小程序sdk：

```bash
npm run build:alipay //正式环境
npm run build:alipay-dev //开发环境
```

构建uni-app sdk：

```bash
npm run build:uni-app //正式环境
npm run build:uni-app-dev //开发环境
```

构建web端sdk:

```bash
npm run build:es6 //正式环境
npm run build:es6-dev //开发环境

npm run build:umd //正式环境
npm run build:umd-dev //开发环境
```

changelog生成：

```bash
npm run changelog
```
