import SubjectListenerImpl from "@/runtime-core/subject-listener-impl";
import { debounce } from "@/utils";

type debounceNotify = (ele?: Element) => void;

/**
 * 页面停留监听器 浏览器端
 */
export default class JsStayListener extends SubjectListenerImpl<StayObserver> implements StayListener {
    init(data: SensorsPara) {
        this.debounceNotify = debounce(this.notify, data.scrollDelayTime, this);
        this.resetInit();
        window.addEventListener('scroll', (e: Event) => {
            this.debounceNotify && this.debounceNotify();
        });
        !this.listenerScroll() && window.addEventListener('load', () => {
            this.listenerScroll();
        });
    }
    /**
     * 监听sensors-scroll元素
     */
    listenerScroll() {
        const scrollEleList = document.querySelectorAll(".sensors-scroll");
        if (scrollEleList.length) for (let i=0; i<scrollEleList.length; i++) {
            const ele = scrollEleList[i];
            ele.addEventListener('scroll', (e: Event) => {
                this.debounceNotify && this.debounceNotify(ele);
            });
        }
        return scrollEleList.length;
    }
    debounceNotify?: debounceNotify;
    notify(ele: Element) {
        this.observers.forEach(observer => observer.onStayChange(ele));
    }
    resetInit() {
        this.debounceNotify && this.debounceNotify();
    }
}