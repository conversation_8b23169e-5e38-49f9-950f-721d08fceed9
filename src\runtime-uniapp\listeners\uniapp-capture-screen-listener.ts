import SensorsParaImpl from "@/runtime-core/sensors-para-impl";
import SubjectListenerImpl from "@/runtime-core/subject-listener-impl";

/**
 * 页面停留监听器 uniapp 端
 */
export default class UniAppCaptureScreenListener
  extends SubjectListenerImpl<CaptureObserver>
  implements CaptureScreenListener
{
  observers: CaptureObserver[] = [];
  notifyFunc: (e: any) => void;
  constructor() {
    super();
    this.notifyFunc = this.notify.bind(this);
  }

  init() {
    uni.onUserCaptureScreen(this.notifyFunc);
  }
  notify(e: any): void {
    this.observers.forEach((observer) => observer.onCaptureScreen(e));
  }
}
