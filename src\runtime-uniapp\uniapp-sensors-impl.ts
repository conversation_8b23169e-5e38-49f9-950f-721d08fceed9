import SensorsImpl, { SensorsImplConstructorArgs } from "@/runtime-core/sensors-impl";
import UniappSysAppListener from "./listeners/uniapp-sys-app-listener";
import { judgingEnvironment,queryAttr } from "@/utils";
import UniappSystemInfoGetter from "./uniapp-system-info-getter";

const lib = judgingEnvironment();

export interface UniappContructorArgs extends SensorsImplConstructorArgs {
    sysAppListener: UniappSysAppListener;
    systemInfoGetter: UniappSystemInfoGetter;
    captureScreenListener: CaptureScreenListener;
}

/**
 * 传感器 uniapp
 */
export default class UniappSensorsImpl extends SensorsImpl {
  para: SensorsPara;
  sysAppListener: UniappSysAppListener;
  systemInfoGetter: UniappSystemInfoGetter;
  captureScreenListener: CaptureScreenListener;
  constructor(props: UniappContructorArgs) {
    super(props);
    this.para = props.para;
    this.sysAppListener = props.sysAppListener;
    this.systemInfoGetter = props.systemInfoGetter;
    this.captureScreenListener = props.captureScreenListener;
  }
  install(app: any) {
    this.sysAppListener.notify(app);
  }
  captureSetupFunctionEvent(event: Event, funcName = "capture") {
    try {
      if (event?.target || event?.currentTarget) {
        const params = {
          ...((event.target as HTMLElement)?.dataset || {}),
          ...((event.currentTarget as HTMLElement)?.dataset || {}),
        };
        const positionSign = queryAttr(params, "position_sign");
        getCurrentPages().slice(-1)[0].$lastPositionSign = positionSign || "";
        const data = this.systemInfoGetter.getReportIncident(event, funcName);
        this.track(data as ReportIncident);
      }
    } catch (err) {
      console.log(err);
    }
  }

  /**
   * app Wi-Fi模块信息获取
   * 由于app权限规范问题，需要暴露方法在使用侧主动调用
   */
  setWiFiInfo() {
    // 初始化Wi-Fi信息
    this.systemInfoGetter.initWifiParams();
    // 监听Wi-Fi变化重新赋值
    this.systemInfoGetter.initWifiMonitor();
  }

  /**
   * app 截图监控listener初始化
   * 由于app权限规范问题，截图listener初始化在使用侧主动调用
   */
  initCaptureScreenListener() {
    this.captureScreenListener.init(this.para);
  }
}