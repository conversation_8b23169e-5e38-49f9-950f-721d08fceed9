import SubjectListenerImpl from "@/runtime-core/subject-listener-impl";
import UniappSensorsParaImpl from "../uniapp-sensors-para-impl";
import { App } from "vue";
import { libEnum } from "@/runtime-core/enum";
import { judgingEnvironment } from "@/utils";

const lib = judgingEnvironment();

/**
 * 监听小程序初始化 uniapp
 */
export default class UniappLaunchListener extends SubjectListenerImpl<LaunchObserver> implements LaunchListener, UniappSysVueComponentObserver {
    init(data: UniappSensorsParaImpl) {
        data.sysVueComponentListener.register(this);
    }
    onVueComponent(component: any): void {
        if (component.mpType === 'app') this.listenerOnLaunch(component);
    }
    /**
     * 监听App onLaunch方法
     */
     private listenerOnLaunch(app: any) {
        const oldOnLaunch = app.onLaunch || function () {};
        const that = this;
        app.onLaunch = function () {
            try {
                that.notify(...arguments);
            } catch(err) {
                console.warn('UniappLaunchListener -> listenerOnLaunch', err);
            }
            return oldOnLaunch.call(this, ...arguments);
        }
    }
    notify(...args: any) {
        this.observers.forEach(observer => observer.onBeforeLaunch && observer.onBeforeLaunch(...args));
        this.observers.forEach(observer => observer.onLaunch && observer.onLaunch(...args));
    }
}