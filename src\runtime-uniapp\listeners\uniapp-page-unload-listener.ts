import SubjectListenerImpl from "@/runtime-core/subject-listener-impl";
import UniappSensorsParaImpl from "../uniapp-sensors-para-impl";

/**
 * 页面销毁监听器
 */
export default class UniappPageUnloadListener extends SubjectListenerImpl<PageUnloadObserver> implements PageUnloadListener, UniappSysPageObserver {
    init(data: UniappSensorsParaImpl): void {
        data.sysPageListener.register(this);
    }
    onPage(page: any) {
        this.listenerPageOnUnload(page);
    }
    /**
     * 监听Page onUnload方法
     */
     listenerPageOnUnload(page: any) {
        const oldOnUnload = page.onUnload || function () {};
        const that = this;
        page.onUnload = function () {
            try {
                that.notify();
            } catch(err) {
                console.warn('UniappPageListener -> listenerPageOnShow', err);
            }
            oldOnUnload.call(this, ...arguments);
        }
    }
    notify(): void {
        this.observers.forEach(observer => {
            observer.onPageUnload && observer.onPageUnload();
        });
        this.observers.forEach(observer => {
            observer.onAfterPageUnload && observer.onAfterPageUnload();
        });
    }
}