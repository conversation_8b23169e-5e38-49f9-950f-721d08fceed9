import DataPollImpl, { destroySnapshot } from "@/runtime-core/data-poll-impl";

export default class JsDataPollImpl extends DataPollImpl {
    isTrySave = false;
    checkDestroySnapshot() {
        const pointData = this.storageHandler.get<PointData>(destroySnapshot);
        if (!pointData) return;
        if (document.referrer) {
            if (document.referrer.includes(location.hostname)) this.append(pointData);
        } else if (!this.systemInfoGetter?.getReferrer()) this.append(pointData);
    }
}