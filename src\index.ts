/*
 * @Author: itnewdata <EMAIL>
 * @Date: 2022-02-16 22:25:44
 * @LastEditors: itnewdata <EMAIL>
 * @LastEditTime: 2023-03-22 11:46:03
 * @FilePath: \data_analysis_sdk\src\index.ts
 * @Description: 全端输出
 */
import DefaultSensors from "./runtime-default/default-sensors";
import alipayMiniSensors from './main-alipay-mini';
import browserSensors from './main-browser';
import uniappSensors from './main-uniapp';

import { libEnum } from "./runtime-core/enum";
import { judgingEnvironment } from "./utils";


const lib = judgingEnvironment();

/**
 * 传感器
 */
let sensors: Sensors = new DefaultSensors();

// Web浏览器端
if (lib === libEnum.WEB) {
    sensors = browserSensors;
}

// 支付宝小程序端
if (lib === libEnum.ALIPAY_MINI) {
    sensors = alipayMiniSensors;
}

// uniapp app plus端
if (lib === libEnum.UNIAPP_APP_PLUS || lib === libEnum.UNIAPP_UNKNOWN) {
    sensors = uniappSensors;
}

export default sensors as Sensors;