import SubjectListenerImpl from "@/runtime-core/subject-listener-impl";
import UniappSensorsParaImpl from "../uniapp-sensors-para-impl";

/**
 * 拖动监听器
 */
export default class UniappTouchListener extends SubjectListenerImpl<TouchObserver> implements TouchListener, UniappSysPageObserver, UniappSysComponentObserver {
    init(data: UniappSensorsParaImpl): void {
        data.sysPageListener.register(this);
        data.sysComponentListener.register(this);
    }
    onPage(page: any): void {
        this.listenerPageOnTouch(page);
    }
    onComponent(component: any): void {
        this.listenerComponentOnTouch(component);
    }
    listenerPageOnTouch(page: any) {
        const oldOnTouch = page.onTouch || function() {};
        const that = this;
        page.onTouch = function (e: any) {
            try {
                that.notify(e);
            } catch(err) {
                console.warn("UniappTouchListener -> listenerOnTouch", err);
            }
            oldOnTouch.call(this, ...arguments);
        }
    }
    listenerComponentOnTouch(component: any) {
        const methods = component.methods || {};
        const oldOnTouch = methods.onTouch || function () {};
        const that = this;
        methods.onTouch = function (e: any) {
            try {
                that.notify(e);
            } catch(err) {
                console.warn("UniappTouchListener -> listenerOnTouch", err);
            }
            oldOnTouch.call(this, ...arguments);
        }
    }
    notify(e: any): void {
        this.observers.forEach(observer => observer.onTouch(e));
    }
}