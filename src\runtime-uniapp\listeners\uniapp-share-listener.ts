import SubjectListenerImpl from "@/runtime-core/subject-listener-impl";
import UniappSensorsParaImpl from "../uniapp-sensors-para-impl";
import { isFunction, isObject,judgingEnvironment } from "@/utils";
import { libEnum } from "@/runtime-core/enum";

const lib = judgingEnvironment();
/**
 * uniapp 分享监听
 */
export default class UniappShareListener
  extends SubjectListenerImpl<ShareObserver>
  implements ShareListener, AlipayMiniSysPageObserver
{
  init(data: UniappSensorsParaImpl): void {
    data.sysPageListener.register(this);
  }
  listenerOnShareAppMessage(component: any) {
      const isAppPlusOrH5 = lib === libEnum.UNIAPP_APP_PLUS || lib === libEnum.UNIAPP_H5;
      if (isAppPlusOrH5||!isObject(component)) return;
      const oldSetup = component.setup;
      const that = this;
      if (isFunction(component.setup)) {
        oldSetup &&
          (component.setup = function (_props: any, _defines: any) {
            const oldRender = oldSetup.call(component, _props, _defines);
            return function render(_ctx: any, _cache: any) {
              const instance = _ctx && _ctx.$scope;
              const oldOnShareAppMessage =_ctx?.$?.onShareAppMessage?.[0];
              // 增加标识，防止render调用多次后onShareAppMessage被多次拦截导致多次执行
              if(!instance.$rrzu_captureShareListener&&oldOnShareAppMessage){        
              instance.onShareAppMessage = function (options: any) {
                const result = oldOnShareAppMessage.apply(this, arguments)||{};
                try {
                  that.notify({ options, result });
                } catch (err) {
                  console.warn("UniappShareListener -> listenerOnShareAppMessage",err);
                }
                return result;
                };
              instance.$rrzu_captureShareListener=true
              }
              const bindingConfig = oldRender.call(component, _ctx, _cache);
              return bindingConfig;
            };
          });
      }
  }
  onPage(page: any) {
    return this.listenerOnShareAppMessage(page);
  }
  onComponent(component: any) {
    return this.listenerOnShareAppMessage(component);
  }
  notify(params: OverloadParams): void {
    this.observers.forEach((observer) => observer.onShare(params));
  }
}
