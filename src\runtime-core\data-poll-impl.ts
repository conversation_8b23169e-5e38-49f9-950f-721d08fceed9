import { eventEnum } from "./enum";
import { storageKey as paraStorageKey  } from "./sensors-para-impl";

// 数据池缓存
const storageKey = "datapoll";
// 数据池缓存对象类型
type Store = {
    list: Record<number, PointData>;
    index: number; // 索引
    atWorks: Record<number, number>; // 提交中列表
    atLeisures: number[]; // 休闲中列表
}
// 销毁快照缓存
export const destroySnapshot = "destroySnapshot";

/**
 * 数据池的实现
 */
export default class DataPollImpl implements DataPoll, UnloadObserver {
    list: Record<number, PointData> = {};
    index: number = 0; // 索引
    atWorks: Record<number, number> = {}; // 提交中列表
    atLeisures: number[] = []; // 休闲中列表
    storageHandler: StorageHandler;
    systemInfoGetter?: SystemInfoGetter;
    paraStore: SensorsParaStore = {
        open_id: '',
        distinct_id: '',
        union_id: '',
        user_id: '',
        initial_id: '',
        session_id: '',
        universal_id:'',
        commProperties: {},
        commGroupProperties: {}
    };
    isTrySave: boolean = true; // 是否建立快照的方式防止丢失数据
    private trySaveTime: number = 4000; // 建立快照时间
    private storeEtag: string = ''; // 缓存标识，用来判断缓存是否变化
    private requestHandler?: RequestHandler;
    private showLog?: boolean;
    private universalIdUrl?:string;
    constructor({ storageHandler }: { storageHandler: StorageHandler }) {
        this.storageHandler = storageHandler;
        this.initList();
    }
    /**
     * 初始化数据池
     */
    init(data: SensorsPara) {
        this.systemInfoGetter = data.systemInfoGetter;
        data.unloadListener.register(this);
        this.trySave(data);
        data.autoTrack.appHide && this.checkDestroySnapshot();
        this.requestHandler = data.requestHandler;
        this.showLog = data.showLog;
        this.paraStore = data.store;
        this.universalIdUrl = data.universalIdUrl;
    }
    /**
     * 建立快照
     */
    private trySave(data: SensorsPara) {
        setInterval(() => {
            if (this.isTrySave) {
                this.bufferDataPoll();
                data.autoTrack.appHide && this.destroySnapshot(data);
            } 
            if (this.atLeisures.length > 100 && this.requestHandler) {
                // 暂时先不这么做
                // const terminal = this.systemInfoGetter.getTerminial()
                // const { distinct_id, open_id } = this.paraStore
                // if(terminal === terminalEnum.ALIPAY_INDI && (!distinct_id || !open_id || distinct_id !== open_id)) {
                //     return
                // }
        
                // if(terminal === terminalEnum.WX_MINI && !distinct_id) {
                //     return
                // }
                const {keys, list} = this.getList();
                this.showLog && console.log("上报数据", list);
                const result = this.requestHandler.send(list);
                result && result.then(() => {
                    this.remove(keys);
                }).catch((err: any) => {
                    this.reset(keys);
                    this.showLog && console.log("上报失败", err);
                });
            }
        }, this.trySaveTime);
    }
    /**
     * 维持一个销毁快照
     * 维持一个长时间程序销毁事件
     * 因为程序销毁事件不一定能监听到，所有我们每隔一段时间保存一个程序销毁事件，
     * 如果监听到了程序销毁则删除改事件，否则在重新启动阶段会把该事件补充发送出去，从而稍微弥补没有程序关闭
     */
    private destroySnapshot(data: SensorsPara) {
        const pageHide = data.getTrackData({
            eventType: eventEnum.PAGE_LEAVE,
            key: "$PageLeave",
            $event_duration: this.systemInfoGetter ? this.systemInfoGetter.getPageDurationTime() : 0
        });
        const mpHide = data.getTrackData({
            key: eventEnum.MP_HIDE,
            eventType: eventEnum.MP_HIDE,
            $event_duration: (this.systemInfoGetter ? this.systemInfoGetter.getTimestamp() - this.systemInfoGetter.getLaunchTime() : 0)
        });
        this.storageHandler.set(destroySnapshot, [pageHide, mpHide]);
    }
    /**
     * 启动时检查销毁快照是否存在，如果存在则说明上次是没有销毁事件触发
     */
    checkDestroySnapshot() {
        const pointData = this.storageHandler.get<PointData>(destroySnapshot);
        if (pointData) this.append(pointData);
    }
    /**
     * 初始化列表 从缓存中获取
     */
    initList() {
        const store = this.storageHandler.get<Store>(storageKey);
        if (store) {
            this.list = store.list;
            this.index = store.index;
            this.atWorks = store.atWorks;
            this.atLeisures = store.atLeisures;
        }
    }
    // 程序卸载
    onUnload() {
        this.remove(Object.keys(this.atWorks) as any as number[]);
        this.bufferDataPoll();
        this.storageHandler.remove(destroySnapshot);
    }
    /**
     * 缓存数据池
     */
    bufferDataPoll() {
        const etag = this.getEtag();
        if (etag != this.storeEtag) {
            this.storageHandler.set(storageKey, {
                list: this.list,
                index: this.index,
                atWorks: this.atWorks,
                atLeisures: this.atLeisures
            });
            this.storeEtag = etag;
        }
    }
    /**
     * 获取缓存标识
     */
    getEtag() {
        let etag = "";
        for (const key in this.atWorks) etag += key + ',';
        etag += "|";
        this.atLeisures.forEach(item => etag += item + ',');
        return etag;
    }
    append(data: PointData | PointData[]) {
        if (Object.keys(this.list).length > 1000) return;
        const list = Array.isArray(data) ? data : [data];
        for (const item of list) {
            this.list[this.index] = item;
            this.atLeisures.push(this.index);
            this.index++;
        }
    };
    getList() {
        const result = {
            keys: [] as number[],
            list: [] as PointData[]
        };
        for (let i=0; i<100; i++) {
            const key = this.atLeisures.shift();
            if (key === undefined) break;
            result.keys.push(key);
            result.list.push(this.list[key]);
            this.atWorks[key] = 1;
        }
        return result;
    }
    remove(keys: number[] = []) {
        keys.forEach(key => {
            delete this.atWorks[key];
            delete this.list[key];
        });
    }
    reset(keys: number[] = []) {
        keys.forEach(key => {
            delete this.atWorks[key];
            this.list[key] && this.atLeisures.unshift(Number(key));
        });
    }
    modifyAny(fn: (data: PointData) => void) {
        for (const key in this.list) {
            const event: PointData = this.list[key];
            if (this.systemInfoGetter && event.event_millitime >= this.systemInfoGetter.getLaunchTime()) fn(event);
        }
    }
    modifyDisctincId(id: string) {
        for (const key in this.list) {
            const item = this.list[key] as PointData;
            item.distinct_id = id;
            item.open_id = id;
        }
    }
    modifyDisctincUnionId(data: IdentifyUnionInfo) {
        const { openid, unionid } = data
        for (const key in this.list) {
            const item = this.list[key] as PointData;
            // 有才替换 没有也记录一下方便排查
            unionid && (item.distinct_id = unionid)
            item.open_id = openid;
            item.union_id = unionid
        }
    }
    modifyUserId(id: string) {
        for (const key in this.list) {
            const item = this.list[key] as PointData;
            item.user_id = id;
        }
    }
    async updateUniversalId(open_id:string,user_id?:string) {
        try{
            let {universal_id:id}:any = (this.storageHandler.get(paraStorageKey)||{});
            const channel_type = this.systemInfoGetter?.getTerminial();
            if(!this.universalIdUrl||!channel_type||!open_id) return id;
            if(!id||user_id){
            const params:any = {
                    channel_type,
                    channel_user_id:open_id       
                }
            user_id&&(params.user_id = Number(user_id));
             const {data:{data:{universal_id}}}= await this.requestHandler!.getUniversalId(this.universalIdUrl, params)
                this.paraStore.universal_id = universal_id;
                id = universal_id;
            }
            for (const key in this.list) {
                const item = this.list[key] as PointData;
                if(!item.universal_id||item.event_name==='$Login'){
                    item.universal_id = id;
                }
            }
            return id;
        }catch(e){
            console.error(e);
        }
    }
}
