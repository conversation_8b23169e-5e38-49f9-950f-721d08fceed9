import SubjectListenerImpl from "@/runtime-core/subject-listener-impl";

/**
 * 页面销毁监听器
 */
export default class JsPageUnloadListener extends SubjectListenerImpl<PageUnloadObserver> implements PageUnloadListener, PageObserver {
    init(data: SensorsPara) {
        data.pageListener.register(this);
    }
    onAfterPageHide() {
        this.notify();
    }
    notify() {
        this.observers.forEach(observer => {
            observer.onPageUnload && observer.onPageUnload();
        });
        this.observers.forEach(observer => {
            observer.onAfterPageUnload && observer.onAfterPageUnload();
        });
    }
}