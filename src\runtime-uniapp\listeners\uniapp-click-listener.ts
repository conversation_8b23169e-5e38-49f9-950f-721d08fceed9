import SubjectListenerImpl from "@/runtime-core/subject-listener-impl";
import UniappSensorsParaImpl from "../uniapp-sensors-para-impl";
import { isFunction, isArray, isObject, isString } from "@/utils";
import { libEnum, uniappLib } from "@/runtime-core/enum";
import { judgingEnvironment } from "@/utils";

const lib = judgingEnvironment();

/**
 * 页面点击监听器 uniapp
 */
export default class UniappClickListener extends SubjectListenerImpl<ClickObserver> implements ClickListener, UniappSysPageObserver, UniappSysComponentObserver {
    eventWeakMap = new WeakMap()
    init(data: UniappSensorsParaImpl) {
        data.sysPageListener.register(this);
        data.sysComponentListener.register(this);
    }
    onPage(page: any): void {
        if (uniappLib.includes(lib as LibEnumType)) this.listenerSetupMethod(page);
    }
    onComponent(component: any) {
        if (uniappLib.includes(lib as LibEnumType)) this.listenerSetupMethod(component);
    }

    /**
     * 
     * @param inner 
     * @param item 
     * @description 不同环境下 编译后的函数的字符串形式不一样
     * @returns {boolean}
     */
    isInnterFunc(inner: any, item: string): boolean {
        const targets = [
          libEnum.UNIAPP_ALIPAY,
          libEnum.UNIAPP_BYTEDANCE,
          libEnum.UNIAPP_DING,
          libEnum.UNIAPP_KUAISHOU,
          libEnum.UNIAPP_JD,
        ];
        if (lib === libEnum.UNIAPP_WEIXIN) {
          return !inner || item.includes("_");
        } else if (targets.includes(lib as LibEnumType)) {
          return true;
        }
        return false;
    }

    getPropKeys(config: any, dest: any, inner = false) {
        const that = this
        if (!isArray(dest)) dest = [];
        if (isArray(config)) {
            config.forEach(function (item: any) {
                that.getPropKeys(item, dest, true);
            });
        } else if (isObject(config)) {
            Object.values(config).forEach(function (item) {
                if (!!item) {
                    if (isString(item) && (item as string).startsWith('e') && that.isInnterFunc(inner, (item as string))) {
                        dest.push(item);
                    } else if (isArray(item)) {
                        that.getPropKeys(item, dest, true);
                    }
                }
            });
        }
        return dest;
    }

    /**
     * @description 劫持函数传入确保事件对象的获取
     * @param instance 
     * @param bindingConfig 
     */
    variableClick(instance: any, bindingConfig: any) {
        const that = this
        if (isObject(instance) && isObject(bindingConfig)) {
            const propKeys = this.getPropKeys(bindingConfig, [], false);
            propKeys.forEach(function (propKey: string) {
                const invoker = instance[propKey];
                if (isFunction(invoker) && isFunction(invoker.value) && !invoker.value.__MPClickFlag) {
                    const oldValue = invoker.value;
                    invoker.value = function (event: MouseEvent, ...res: any[]) {
                        if (event && (lib === libEnum.UNIAPP_APP_PLUS ? event.type === "onClick" : event.type === "tap")) {
                            // 去重 避免emit传递事件对象时多次触发
                            if (!that.eventWeakMap.has(event)) {
                                that.notify(event, propKey);
                                that.eventWeakMap.set(event, true)
                            }
                        }
                        return oldValue.call(instance, event, ...res)
                    }
                    invoker.value.__MPClickFlag = true;
                }
            });
        }
    }

    listenerSetupMethod(component: any) {
        const oldSetup = component.setup;
        const that = this;
        /**
         * vue3源码中有对setup参数长度判断来创建setup上下文
         * vue3源码部分：const setupContext = instance.setupContext = setup.length > 1 ? createSetupContext(instance) : null;
         */
        if(!isObject(component)) return
        if (isFunction(component.render)) {
            const oldRender = component.render;
            component.render = function (_ctx: any, _cache: any, $props: any, $setup: any, $data: any, $options: any) {
                const instance = _ctx && _ctx.$scope;
                const bindingConfig = oldRender.call(component, _ctx, _cache, $props, $setup, $data, $options);
                that.variableClick(instance, bindingConfig)
                return bindingConfig;
            }
        } else if (isFunction(component.setup)) {
            oldSetup && (component.setup = function (_props: any, _defines: any) {
                const oldRender = oldSetup.call(component, _props, _defines);
                return function render(_ctx: any, _cache: any) {
                    const instance = _ctx && _ctx.$scope;
                    const bindingConfig = oldRender.call(component, _ctx, _cache);
                    that.variableClick(instance, bindingConfig)
                    return bindingConfig;
                }
            });
        }
        // oldSetup && (component.setup = function (props: any, options: any) {
        //     const result = oldSetup.call(this, ...arguments);
        //     for (const key in result) {
        //         if (typeof result[key] === 'function') {
        //             const oldMethod = result[key];
        //             console.log(oldMethod,'oldMethod');
            
        //             result[key] = function (e: any) {
        //                 try {
        //                     // 如果是点击事件
        //                     if (e && (lib === libEnum.UNIAPP_APP_PLUS ? e.type === "onClick" : e.type === "tap")) {
        //                         that.notify(e, key);
        //                     }
        //                 } catch(err) {
        //                     console.warn('UniappClickListener -> listenerMpComponentClick', err);
        //                 }
        //                 return oldMethod.call(this, ...arguments);
        //             }
        //         }
        //     }
        //     return result;
        // });
    }
    notify(e: MouseEvent, funcName: string) {
        this.observers.forEach(observer => observer.onClickChange(e, funcName));
    }
}