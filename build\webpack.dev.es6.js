import { fileURLToPath } from "url";
import path from "path";
import { VueLoaderPlugin } from "vue-loader";
import FormatPlugin from "../plugins/format-plugin.js";
import TerserPlugin from "terser-webpack-plugin";
import CopyDistBundlePlugin from "../plugins/copy-dist-bundle.js";
import webpack from "webpack";

const { DefinePlugin } = webpack;

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
function resolve(dir) {
  return path.join(__dirname, "../" + dir);
}

export default {
  mode: "production",
  entry: resolve("src/index.ts"),
  output: {
    path: resolve("dist/web"),
    filename: "data-analysis-sdk-es6-min.js",
    library: {
      type: "module",
    },
  },
  experiments: {
    outputModule: true,
  },
  plugins: [new VueLoaderPlugin()],
  module: {
    rules: [
      {
        test: /\.tsx?$/,
        loader: "babel-loader",
        options: {
          presets: [
            "@babel/preset-env",
            [
              "@babel/preset-typescript", // 引用Typescript插件
              {
                allExtensions: true, // ?支持所有文件扩展名
              },
            ],
          ],
        },
      },
      {
        test: /\.vue$/,
        use: ["vue-loader"],
      },
    ],
  },
  resolve: {
    extensions: [".ts", ".js"],
    alias: {
      "@": resolve("src"),
    },
  },
  plugins: [
    new FormatPlugin(),
    new CopyDistBundlePlugin(),
    new DefinePlugin({
      "process.env.LIB_ENV": JSON.stringify(process.env.LIB_ENV),
    }),
  ],
  optimization: {
    minimize: true,
    minimizer: [
      new TerserPlugin({
        exclude: /\.(t|j)s$/,
      }),
    ],
  },
  performance: {
    hints: false,
  },
};
