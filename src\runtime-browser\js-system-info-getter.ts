import { terminalEnum, eventEnum } from "../runtime-core/enum";
import { generateNanoid, camelCaseFormattedUnderscore, queryAttr, waterTap, getFingerprintId, getRouterMode,generateDataSetAttributes } from "@/utils";
import { judgingEnvironment, getQuery } from "@/utils";
import { libEnum } from "@/runtime-core/enum";
import { storageKey } from "@/runtime-core/sensors-para-impl";

const lib = judgingEnvironment();
const launchQueryKey = 'DC_LAUNCH_QUERY';

/**
 * 系统信息提取 浏览器端
 */
export default class JsSystemInfoGetter implements SystemInfoGetter, PageObserver, LaunchObserver {
    private isTrackSinglePage?: boolean;
    private trackUrlMap?: Record<string,string>;
    private pageTitle?: string;
    private preUrl?: string; // 上一个页面路径
    private tempUrl?: string; // 临时路径
    private launchTime = Date.now(); // 程序启动时间
    private fingerprintId = '';
    private para?: SensorsPara;
    private _detainedTime: number; // 上次鼠标移动时间
    private _duration_time: number = 0; // 停留时间
    private storageHandler?: StorageHandler;
    constructor() {
        this._detainedTime = Date.now();
    }
    init(data: SensorsPara) {
        this.para = data;
        this.isTrackSinglePage = data.isTrackSinglePage;
        this.trackUrlMap = data.trackUrlMap
        this.storageHandler = data.storageHandler;
        data.pageListener.register(this);
        data.launchListener.register(this);
        window.addEventListener('mousemove', waterTap(this.beDetained, 100, this), false);
    }
    generateRandomId() {
        if (!this.fingerprintId) {
            const id = getFingerprintId();
            this.fingerprintId = id;
            const sensonrsdata: any = this.para?.storageHandler?.get('sensonrsdata') || this.storageHandler?.get('sensonrsdata');
            const distinct_id = sensonrsdata?.distinct_id || id
            this.para && this.para.identify(distinct_id);
            this.para && this.para.dataPoll.modifyAny((data: PointData) => {
                data.distinct_id = distinct_id;
            });
        }
        return this.fingerprintId || generateNanoid();
    }

    getTerminial() {
        if (lib === libEnum.UNIAPP_H5) {
            return terminalEnum.H5_MINI;
        }
        if(/Android|webOS|iPhone|iPod|BlackBerry/i.test(navigator.userAgent)) {
            return terminalEnum.WAP;
        } else {
            return terminalEnum.PC;
        }
    }
    getViewportWidth() {
        return window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth || 0;
    }
    getViewportHeight() {
        return window.innerHeight || document.documentElement.clientHeight || document.body.clientHeight || 0;
    }
    getScreenHeight(ele?: Element) {
        if (ele) return ele.scrollHeight;
        return document.documentElement.scrollHeight || document.body.scrollHeight || 0;
    }
    getScrollTop(ele?: Element) {
        if (ele) return ele.scrollTop;
        return document.documentElement.scrollTop || document.body.scrollHeight || 0;
    }
    getLatestTrafficSourceType() {
        const url = window.location.href;
        const referrer = document.referrer;
        if (/(utm_source|utm_medium|utm_campaign|utm_content|utm_term)\=[^&]+/.test(url)) {
            return '付费广告流量';
        } else if (/(www\.baidu\.|m\.baidu\.|m\.sm\.cn|so\.com|sogou\.com|youdao\.com|google\.|yahoo.com\/|bing.com\/|ask.com\/)/.test(referrer)) {
            return '自然搜索流量';
        } else if (/(weibo\.com|renren\.com|kaixin001\.com|douban\.com|qzone\.qq\.com|zhihu\.com|tieba\.baidu\.com|weixin\.qq\.com)/.test(referrer)) {
            return '社交网站流量';
        } else if (!referrer || url === referrer) {
            return '直接流量';
        } else {
            return '';
        }
    }
    getLatestReferrer(): string {
        return this.preUrl || '';
    }
    getUrl() {
        if (lib === libEnum.UNIAPP_H5) {
            return this.getUrlPath()
        }
        return window.location.href;
    }
    getHost() {
        return window.location.host;
    }
    getReferrer() {
        return this.isTrackSinglePage ? this.preUrl || '' : document.referrer;
    }

    getUrlPath() {
        if (lib === libEnum.UNIAPP_H5) {
            const routeMode = getRouterMode();
            const pages = getCurrentPages();
            const route = pages.length && pages[pages.length - 1].route;
            let path = ''
            if (routeMode === 'hash'){
                path =  window.location.hash.slice(2).split('?')[0];
            } else if (routeMode === 'history'){
                path =  window.location.pathname.slice(1).split('?')[0];
            }
            if (!path && route) {
                path = route
            }
            if (this.trackUrlMap && this.trackUrlMap[path]){
                path = this.trackUrlMap[path]
            }
            return path
        }
        return document.location.pathname;
    }
    getTitle(event: string) {
        return (this.isTrackSinglePage && event === eventEnum.PAGE_LEAVE) ? (this.pageTitle || document.title) : document.title;
    }
    private getDomIndex(el: Element) {
        if (!el.parentNode) return -1;
        var i = 0;
        var nodeName = el.tagName;
        var list = el.parentNode.children;
        for (var n = 0; n < list.length; n++) {
          if (list[n].tagName === nodeName) {
            if (el === list[n]) {
              return i;
            } else {
              i++;
            }
          }
        }
        return -1;
    }
    private selector(el: any) {
        var i = el.parentNode && 9 == el.parentNode.nodeType ? -1 : this.getDomIndex(el);
        return el.tagName.toLowerCase() + (~i ? ':nth-of-type(' + (i + 1) + ')' : '');
    }
    getDomSelector(el: Element | ParentNode, arr?: string[]): string {
        if (!el || !el.parentNode || !el.parentNode.children) {
            return '';
        }
        arr = arr && Array.isArray(arr) ? arr : [];
        var name = el.nodeName.toLowerCase();
        if (!el || name === 'body' || 1 != el.nodeType) {
            arr.unshift('body');
            return arr.join(' > ');
        }
        arr.unshift(this.selector(el));
        return this.getDomSelector(el.parentNode, arr);
    }
    getElementPath(element: Document | ParentNode | any): string {
        var names = [];
        while (element.parentNode) {
            if (element === document.body) {
              names.unshift('body');
              break;
            } else {
              names.unshift(element.tagName.toLowerCase());
            }
            element = element.parentNode;
        }
        return names.join(' > ');
    }
    /**
     * @description: 组成path
     * @param {any} e
     * @return {*}
     */
    _composedPath(e: any) {
        // 存在则直接return
        if (e.path) { return e.path }
        // 不存在则遍历target节点
        let target = e.target;
        e.path = []
        while (target.parentNode !== null) {
        e.path.push(target)
        target = target.parentNode
        }
        // 最后补上document和window
        e.path.push(document, window)
        return e.path
    }
    /**
     * 获取点击事件上报属性
     */
    getClickReportIncident(e: any) {
        if (!e.path) e.path = this._composedPath(e);
        let target = e.target;
        const customProperties: any = {};
        const dataset: Record<string, string> = {};
        for (let i = 0; i < e.path.length; i++) {
            const uniDataset = e.path[i].__uniDataset || {}
            // 兼容uniapp h5端
            if (typeof uniDataset.params === 'string') {
                uniDataset.params = JSON.parse(uniDataset.params);
            }
            const datasetItem = {
                ...e.path[i].dataset,
                ...uniDataset
            }
            for (const key in datasetItem) {
                const regexp = /sensors_.+/.exec(key);
                if (regexp) {
                    const attrKey = regexp[0].slice(8);
                    customProperties[camelCaseFormattedUnderscore(attrKey)] = datasetItem[key];
                }
                dataset[key] = datasetItem[key];
            }
        }
        let key;
        if (target.dataset.sensors) key = target.dataset.sensors;
        else key = eventEnum.PAGE_CLICK;

        // 尝试直接获取到业务属性
        const positionSign = queryAttr(dataset, 'position_sign');
        if (lib === libEnum.UNIAPP_H5) {
            getCurrentPages().slice(-1)[0].$lastPositionSign = positionSign || '';
        }

        positionSign && (customProperties.positionSignId = positionSign);
        return {
            eventType: eventEnum.PAGE_CLICK,
            key,
            $element_type: target.tagName,
            $element_class_name: target.className,
            $element_content: target.textContent,
            $element_selector: this.getDomSelector(target),
            $element_path: this.getElementPath(target),
            $page_x: e.pageX,
            $page_y: e.pageY,
            $client_x: e.clientX,
            $client_y: e.clientY,
            customProperties
        };
    }
    /**
     * 获取曝光事件上报属性
     */
    getExposureResportIncident(e: any): ReportIncident | void {
        const target = e.target;
        const customProperties: any = generateDataSetAttributes(target.dataset);
        let eventName, eventType;
        if (e.target.classList.contains('sensors_exposure')) {
            eventName = target.dataset.sensorsExposure || target.dataset.sensorsexposure;
            eventType = eventEnum.PAGE_EXPOSURE;
        }
        if (e.target.classList.contains('sensors_touch')) {
            eventName = target.dataset.sensorsTouch || target.dataset.sensorstouch;
            eventType = eventEnum.TOUCH;
        }
        return {
            eventType,
            key: eventName || eventType,
            $element_type: target.tagName,
            $element_class_name: target.className,
            $element_content: target.textContent,
            $element_selector: this.getDomSelector(target),
            $element_path: this.getElementPath(target),
            customProperties
        }
    }
    /**
     * 获取事件属性
     */
    getReportIncident(e: any): ReportIncident | void {
        if (e.target && (
            e.target.classList.contains('sensors_exposure') ||
            e.target.classList.contains('sensors_touch')
        )) return this.getExposureResportIncident(e);
        else return this.getClickReportIncident(e);
    }
    getUserAgent() {
        return navigator.userAgent;
    }
    /**
     * @description: 不统计超过两分钟不移动操作的情况
     * @return {*}
     */
    beDetained() {
        const date = Date.now();
        if (date - this._detainedTime < 2 * 60 * 1000) {
            this._duration_time += date - this._detainedTime;
        }
        this._detainedTime = date;
    }
    getPageDurationTime() {
        return this._duration_time;
    }
    onBeforePageLoad() {
        // 如果页面参数中有statistical_from字段，把字段放到程序启动参数中
        const {statistical_from} = this.getOnLoadQuery();
        const launchQuery = this.getLaunchQuery();
        if (statistical_from&&statistical_from!=='undefined') {
            launchQuery.statistical_from = statistical_from;
            window.sessionStorage.setItem(launchQueryKey, JSON.stringify(launchQuery));
        }
    }
    onBeforeLaunch(options: Record<string, any>) {
        window.sessionStorage.setItem(launchQueryKey, JSON.stringify(options));
    }
    onBeforeShow() {
        if (this.isTrackSinglePage) {
            this.pageTitle = document.title;
        }
        if (this.tempUrl !== this.preUrl) {
            this.preUrl = this.tempUrl;
        }
        const date = Date.now();
        this._detainedTime = date;
        this._duration_time = 0;
    }
    onBeforeHide() {
        this.tempUrl = this.getUrl();
    }
    getLaunchQuery(): Record<string, string> {
        const queryStr = window.sessionStorage.getItem(launchQueryKey);
        return queryStr ? JSON.parse(queryStr) : {};
    }
    getOnLoadQuery(): Record<string, string> {
        return getQuery();
    }
    getTimestamp() {
        return Date.now();
    }
    getLaunchTime(): number {
        return this.launchTime;
    }
    getPageList(){
        if (lib !== libEnum.UNIAPP_H5) return []
        const routes = getCurrentPages();
        return routes?.map((p: any) => {
          let page = p && p.route ? p.route : "plugin://";
          if (this.trackUrlMap && this.trackUrlMap[page]) {
            page = this.trackUrlMap[page];
          }
          return page
        });
    }

    getPageVersion() {
        // 看看缓存里有没有存着的页面版本号
        const store = this.storageHandler?.get<SensorsParaStore>(storageKey);
        const commProperties = store?.commProperties || {};
        const page_version_obj = commProperties.page_version_obj ? JSON.parse(commProperties.page_version_obj) : {};
        const page_version = page_version_obj[this.tempUrl as string] || "";
        return page_version
      }

    getPositionList(){
        if (lib !== libEnum.UNIAPP_H5) return []
        let index = 0;
        const routes = getCurrentPages();
        return routes.map((p: any,idx: number) => {
            if (p.$lastPositionSign) {
                index=idx;
                return p.$lastPositionSign
            } else {
                return routes[index]&&routes[index].$lastPositionSign||'';
            }
        })
    }
}