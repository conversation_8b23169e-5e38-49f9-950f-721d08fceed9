import fs from "fs-extra";
import path from "path";
import { fileURLToPath } from "url";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 定义文件路径
const rootDir = __dirname;
const distDir = path.join(rootDir, "dist");


// 读取原始 package.json
const packageJson = await fs.readJSON(path.join(rootDir, "package.json"));

// 添加额外配置
const distPackageJson = {
  ...packageJson,
  exports: {
    "./alipay": "./alipay/data-analysis-sdk-alipay.js",
    "./uni-app": "./uniapp/data-analysis-sdk-uniapp.js",
    "./es6": "./web/data-analysis-sdk-es6-min.js",
    "./umd": "./web/data-analysis-sdk-umd-min.js",
  }
};

// 删除不需要的字段
delete distPackageJson.dependencies;
delete distPackageJson.devDependencies;
delete distPackageJson.scripts;
delete distPackageJson.main;

// 写入文件
await fs.ensureDir(distDir);
await fs.writeJSON(path.join(distDir, "package.json"), distPackageJson, { spaces: 2 });
await fs.copy(path.join(rootDir, "README.md"), path.join(distDir, "README.md"));

console.log("Successfully copied package.json and README.md to dist directory"); 