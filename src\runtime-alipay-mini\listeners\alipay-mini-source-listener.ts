import SubjectListenerImpl from "@/runtime-core/subject-listener-impl";
import AlipayMiniSensorsParaImpl from "../alipay-mini-sensors-para-impl";

/**
 * 来源监听器 支付宝小程序
 */
export default class AlipayMiniSourceListener extends SubjectListenerImpl<SourceObserver> implements PageLoadObserver {
    init(data: AlipayMiniSensorsParaImpl) {
        data.pageLoadListener.register(this);
    }
    onPageLoad(options: any) {
        if (options && options.distinctId) {
            this.notify({
                id: options.distinctId,
                latestTrafficSourceType: options.sourceTerminal, // 生活号目前用着method字段来表示来源类型
                latestReferrer: options.item_view_before
            });
        }
    }
    notify(options: any) {
        this.observers.forEach(observer => observer.onSource(options));
    }
}