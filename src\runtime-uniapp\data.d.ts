// uni-app对象
declare const uni: any;
declare const plus: any;
declare const wx: any;
declare const tt: any;
declare const ks: any;
declare const dd: any;
declare const jd: any;

interface UniappSysComponentObserver {
    onComponent(component: any): void;
}

/**
 * 页面滚动条订阅者
 */
 interface UniappScrollObserver extends Observer {
    onScrollChange(args: UniappScrollInfo): void;
}

/**
 * 滚动信息
 */
 interface UniappScrollInfo {
    scrollTop: number;
}

/**
 * Page重载订阅者
 */
 interface UniappSysPageObserver extends Observer {
    onPage(page: object): void;
}

/**
 * App重载订阅者
 */
interface UniappSysAppObserver extends Observer {
    onApp(app: object): void;
}

/**
 * Component重载订阅者
 */
interface UniappSysComponentOvserver extends Observer {
    onComponent(component: object): void;
}

/**
 * Uniapp系统Vue组件订阅者
 */
interface UniappSysVueComponentObserver {
    onVueComponent(component: object): void;
}