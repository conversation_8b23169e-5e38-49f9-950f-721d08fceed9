/*
 * @Author: itnewdata <EMAIL>
 * @Date: 2022-02-16 22:25:44
 * @LastEditors: itnewdata <EMAIL>
 * @LastEditTime: 2023-03-25 11:14:31
 * @FilePath: \data_analysis_sdk\src\runtime-browser\listener\js-page-listener.ts
 * @Description: 页面显示关闭监听
 */
import { libEnum } from "@/runtime-core/enum";
import SubjectListenerImpl from "@/runtime-core/subject-listener-impl";
import { isFunction, judgingEnvironment, waterTap } from "@/utils";
import JsSensorsParaImpl from "../js-sensors-para-impl";

enum NotifyType {
  show,
  hide
}

const lib = judgingEnvironment();

/**
 * 页面跳转监听器 浏览器端
 */
export default class JsPageListener extends SubjectListenerImpl<PageObserver> implements PageListener, UniappSysPageObserver {
  isTrackSinglePage: boolean = false;
  init(data: JsSensorsParaImpl) {
    this.isTrackSinglePage = data.isTrackSinglePage;
    if (this.isTrackSinglePage) {
      if (lib === libEnum.UNIAPP_H5) {
        data.sysPageListener?.register(this);
        setTimeout(() => {
          this.addSinglePageEvent();
        }, 100);
      } else {
        this.waterTapNotifyShow();
        setTimeout(() => {
          this.addSinglePageEvent();
        }, 100);
      }
      
    } else {
      window.addEventListener('load', () => {
        this.waterTapNotifyShow();
      });
      window.addEventListener('beforeunload', () => {
        this.waterTapNotifyHide();
      }, false);
    }
  }
  timerWaterTapNotifyShow() {
    // uniapp web端不使用该方法触发show
    if (lib === libEnum.UNIAPP_H5) return
    setTimeout(() => {
      this.waterTapNotifyShow();
    }, 0);
  }
  addSinglePageEvent() {
      const historyPushState = window.history.pushState;
      const historyReplaceState = window.history.replaceState;

      if (isFunction(window.history.pushState)) {
        window.history.pushState = (...args) => {
          this.waterTapNotifyHide();
          historyPushState.call(window.history, ...args);
          this.timerWaterTapNotifyShow()
        };
      }
      if (isFunction(window.history.replaceState)) {
        window.history.replaceState = (...args) => {
          this.waterTapNotifyHide();
          historyReplaceState.call(window.history, ...args);
          this.timerWaterTapNotifyShow()
        };
      }
  }
  onPage(page: any) {
    this.listenerPageOnShow(page);
  }
  /**
   * 监听Page onShow方法
   */
  listenerPageOnShow(page: any) {
      const oldOnShow = page.onShow || function () {};
      const that = this;
      page.onShow = function () {
          try {
              that.waterTapNotifyShow()
          } catch(err) {
              console.warn('UniappPageListener -> listenerPageOnShow', err);
          }
          oldOnShow.call(this, ...arguments);
      }
  }
  waterTapNotifyShow = waterTap(this.notifyShow, 100, this);
  waterTapNotifyHide = waterTap(this.notifyHide, 100, this);
  notifyShow() {
    this.notify(NotifyType.show);
  }
  notifyHide() {
    this.notify(NotifyType.hide);
  }
  notify(type: NotifyType) {
    try {
      this.observers.forEach(observer => {
        type === NotifyType.show && observer.onBeforeShow && observer.onBeforeShow();
        type === NotifyType.hide && observer.onBeforeHide && observer.onBeforeHide();
      });
      this.observers.forEach(observer => {
        type === NotifyType.show && observer.onPageShow && observer.onPageShow();
        type === NotifyType.hide && observer.onPageHide && observer.onPageHide();
      });
      this.observers.forEach(observer => {
        type === NotifyType.show && observer.onAfterPageShow && observer.onAfterPageShow();
        type === NotifyType.hide && observer.onAfterPageHide && observer.onAfterPageHide();
      });
    } catch(err) {
      console.warn('JsPageListener -> notify', err);
    }
  }
}