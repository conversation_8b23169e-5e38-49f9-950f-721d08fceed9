<template>
    <div class="touch">
        <div class="box sensors_touch" data-sensorsTouch="$TouchTest">
            <div v-for="item in list">{{ item }}</div>
        </div>
        <div class="box sensors_touch">
            <div v-for="item in list">{{ item }}</div>
        </div>
    </div>
</template>

<script setup lang="ts">
const list = [1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20];
</script>

<style scoped lang="scss">
    .box {
        box-sizing: border-box;
        padding: 10px;
        background-color: #EEE;
        overflow-x: auto;
        cursor: pointer;
        user-select: none;
        border: 1px solid #DDD;
        white-space: nowrap;
        > div {
            display: inline-block;
            width: 50px;
            height: 50px;
            text-align: center;
            line-height: 50px;
            background-color: #999;
        }
        > div + div {
            margin-left: 5px;
        }
    }
    .box::-webkit-scrollbar {
        display: none;
    }
</style>