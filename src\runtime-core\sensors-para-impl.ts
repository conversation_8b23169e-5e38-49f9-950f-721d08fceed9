import {
  camelCaseFormattedUnderscore,
  UnderscoreToCamelCase,
  isNumber,
  isObject,
  isEmpty,
  charCodeStr,
  generateUUID,
  judgingEnvironment,
  appendDynamicAttribute,
} from "@/utils";
import config from "@/config";
import { eventEnum, libEnum, terminalEnum, dynamicPropEnum } from "./enum";

// 缓存键
export const storageKey = "sensonrsdata";
export const distinctIdStoreKey = "DISTINCT_ID_STORE_KEY";

export interface SensorsParaConstructorArgs {
  dataPoll: DataPoll;
  requestHandler: RequestHandler;
  pageListener: PageListener;
  clickListener: ClickListener;
  stayListener: StayListener;
  systemInfoGetter: SystemInfoGetter;
  storageHandler: StorageHandler;
  pageLoadListener: PageLoadListener;
  pageUnloadListener: PageUnloadListener;
  launchListener: LaunchListener;
  unloadListener: UnloadListener;
  shareListener: ShareListener;
  exposureListener: ExposureListener;
  appearListener: AppearListener;
  touchListener: TouchListener;
  captureScreenListener?: CaptureScreenListener;
}

const lib = judgingEnvironment();

/**
 * 传感器配置数据
 */
export default class SensorsParaImpl
  implements SensorsPara, LaunchObserver, PageObserver, PageLoadObserver, PageUnloadObserver, UnloadObserver
{
  protected $terminal: TerminalEnumType;
  protected $lib_version: string = config.version;
  protected storageKey: string = "RRZU_SENSORS_DATA"; // 缓存数据key
  protected bufferPageProperties: any = {}; // 缓存页面属性
  protected preUrl?: string; // 上一个页面路径
  format?: Function; // 格式化
  autoTrack = {
    appLaunch: true,
    appHide: true,
    pageShow: true,
    pageLeave: true,
    pageClick: true,
    pageStay: true,
    pageShare: true,
    pageExposure: true,
    pageAppear: true,
  };
  serverUrl = "";
  universalIdUrl = "";
  needCookies?: boolean;
  customDownloadChannel?: string;
  isIFrame?: boolean; // 是否是嵌入的iframe页面
  bindServerUrl = "";
  showLog = false;
  batchSendTimeout = 3000;
  dataSendTimeout = 60000;
  scrollDelayTime = 4000;
  isTrackSinglePage = false;
  trackUrlMap?: Record<string, any>;
  extraRequestHeaders: Record<string, string> = {}; // 额外的请求头参数
  store: SensorsParaStore = {
    // 缓存数据
    open_id: "",
    distinct_id: "",
    union_id: "",
    user_id: "",
    initial_id: "",
    universal_id: "",
    commProperties: {}, // 公共属性
    commGroupProperties: {}, // 分组的公共属性
    is_login: false,
    session_id: "",
  };
  /**
   * 周期属性
   * 只在一个程序生命周期内有效
   */
  cycleProperties: Record<string, any> = {};
  launchParams: Record<string, string> = {}; // 程序启动参数
  loadParams: Record<string, string> = {}; // 页面启动参数
  pageTitleConfig: any = {}; // 页面标题配置
  pageProperties: Record<string, Record<string, string | number>> = {}; // 页面属性
  dataPoll: DataPoll;
  requestHandler: RequestHandler;
  pageLoadListener: PageLoadListener;
  pageUnloadListener: PageUnloadListener;
  pageListener: PageListener;
  clickListener: ClickListener;
  stayListener: StayListener;

  tabItemListener?: TabItemListener;
  captureScreenListener?: CaptureScreenListener;
  systemInfoGetter: SystemInfoGetter;
  storageHandler: StorageHandler;
  launchListener: LaunchListener;
  unloadListener: UnloadListener;
  shareListener: ShareListener;
  exposureListener: ExposureListener;
  appearListener: AppearListener;
  touchListener: TouchListener;
  inviteUserId?: string; // 邀请用户id
  inviteDistinctId?: string; // 邀请匿名id
  inviteTime?: number; // 邀请时间
  tabBarPositionMap?: Record<string, string>;
  constructor(args: SensorsParaConstructorArgs) {
    this.requestHandler = args.requestHandler;
    this.dataPoll = args.dataPoll;
    this.pageLoadListener = args.pageLoadListener;
    this.pageUnloadListener = args.pageUnloadListener;
    this.pageListener = args.pageListener;
    this.clickListener = args.clickListener;
    this.stayListener = args.stayListener;
    this.launchListener = args.launchListener;
    this.unloadListener = args.unloadListener;
    this.systemInfoGetter = args.systemInfoGetter;
    this.storageHandler = args.storageHandler;
    this.$terminal = this.systemInfoGetter.getTerminial();
    args.launchListener.register(this);
    args.pageLoadListener.register(this);
    args.pageListener.register(this);
    args.pageUnloadListener.register(this);
    args.unloadListener.register(this);
    this.shareListener = args.shareListener;
    this.exposureListener = args.exposureListener;
    this.appearListener = args.appearListener;
    this.touchListener = args.touchListener;
    this.captureScreenListener = args.captureScreenListener;
  }
  /**
   * 初始化
   */
  init(data: InitData) {
    // 初始化配置参数
    if (!data.serverUrl)
      return console.warn(
        "当前 serverUrl 为空或不正确，只在控制台打印日志，network 中不会发数据，请配置正确的 serverUrl"
      );
    if (!data.bindServerUrl) console.warn("当前 bindServerUrl 为空或不正确");
    if (!data.universalIdUrl) console.warn("当前 universalIdUrl 为空或不正确");
    this.initStore();
    if (data.terminal) {
      this.$terminal = data.terminal;
      this.systemInfoGetter.getTerminial = () => data.terminal!;
    }
    this.initSessionId()
    this.serverUrl = data.serverUrl;
    this.universalIdUrl = data.universalIdUrl || "";
    this.needCookies = data.needCookies;
    data.customDownloadChannel && (this.customDownloadChannel = data.customDownloadChannel);
    this.isIFrame = data.isIFrame;
    this.bindServerUrl = data.bindServerUrl || "";
    this.format = data.format;
    data.showLog && (this.showLog = data.showLog);
    data.isTrackSinglePage && (this.isTrackSinglePage = data.isTrackSinglePage);
    data.trackUrlMap && (this.trackUrlMap = data.trackUrlMap);
    isObject(data.tabBarPositionMap) && (this.tabBarPositionMap = data.tabBarPositionMap);
    isNumber(data.batchSendTimeout) && (this.batchSendTimeout = data.batchSendTimeout as number);
    isNumber(data.dataSendTimeout) && (this.dataSendTimeout = data.dataSendTimeout as number);
    isNumber(data.scrollDelayTime) && (this.scrollDelayTime = data.scrollDelayTime as number);
    if (data.autoTrack&&isObject(data.autoTrack)) {
      this.autoTrack={
        ...this.autoTrack,
        ...data.autoTrack
      }
    }
    if (data.pageTitleConfig) {
      if (!isObject(data.pageTitleConfig)) return console.warn("设置页面标题参数必须为对象");
      this.pageTitleConfig = data.pageTitleConfig as object;
    }
    this.systemInfoGetter.init(this);
    this.dataPoll.init(this);
    // 初始化请求处理器
    this.requestHandler.serverUrl = data.serverUrl;
    this.requestHandler.needCookies = data.needCookies;
    isNumber(this.dataSendTimeout) && (this.requestHandler.dataSendTimeout = this.dataSendTimeout as number);
    // 初始化监听器
    if (this.autoTrack.pageShow || this.autoTrack.pageStay) {
      this.pageListener.init(this);
    }
    this.pageLoadListener.init(this);
    this.pageUnloadListener.init(this);
    this.autoTrack.pageClick && this.clickListener.init(this);
    this.autoTrack.pageStay && this.stayListener.init(this);
    this.unloadListener.init(this);
    this.launchListener && this.launchListener.init(this);
    this.shareListener.init(this);
    this.exposureListener.init(this);
    this.appearListener.init(this);
    this.touchListener.init(this);
    // app端口截图监控由使用方主动触发
    if (lib !== libEnum.UNIAPP_APP_PLUS) {
      this.captureScreenListener?.init(this);
    }
    this.bufferStore();
  }
  /**
   * 初始化缓存
   */
  protected initStore() {
    const store = this.storageHandler.get<SensorsParaStore>(storageKey);
    if (store) {
      this.store = store;
      if (!store.commGroupProperties) this.store.commGroupProperties = {};
    }
  }
  /**
   * 初始化独立id
   */
  protected initSessionId() {
    const id = this.systemInfoGetter.generateRandomId();
    const terminal = this.systemInfoGetter.getTerminial();
    const alipayTerminalMap = [
      terminalEnum.WX_MINI,
      terminalEnum.ALIPAY_TP_VIVO,
      terminalEnum.ALIPAY_HONOR,
      terminalEnum.ALIPAY_HUAWEI,
      terminalEnum.ALIPAY_XIAOMI,
      terminalEnum.ALIPAY_OPPO,
    ];
    // 如果没有初始化id，初始化一次
    if (!this.store.initial_id) {
      this.store.initial_id = id;
    }
    // 不是微信端或者vivo、华为、荣耀、小米等品牌小程序才给匿名id
    if (!this.store.distinct_id && !alipayTerminalMap.includes(terminal)) {
      this.store.distinct_id = id;
    }
    // 每次都更新
    this.store.session_id = this.systemInfoGetter.generateRandomId();
  }

  /**
   * 缓存store
   */
  protected bufferStore() {
    this.storageHandler.set(storageKey, this.store);
  }
  /**
   * 注入启动参数
   */
  private appendLaunchQuery(pointData: PointData) {
    const launchQuery = this.systemInfoGetter.getLaunchQuery();
    for (const key in launchQuery) {
      if (!isEmpty(launchQuery[key])) {
        pointData.properties["launch_" + camelCaseFormattedUnderscore(key)] = launchQuery[key];
        // 兼容third_terminal字段到statistical_from
        if (key === "third_terminal") {
          pointData.properties["launch_statistical_from"] = launchQuery[key];
        }
      }
    }
  }
  /**
   * 登录
   */
  login(userId: string) {
    // 如果缓存到匿名id
    const distinctIdStore: any = this.storageHandler.get(distinctIdStoreKey) || {};
    // 切换登录
    if (this.store.user_id && this.store.user_id !== userId) {
      if (distinctIdStore && distinctIdStore[userId]) this.store.distinct_id = distinctIdStore[userId];
      this.store.user_id = userId;
    }
    // 普通登录
    else {
      this.store.user_id = userId;
      distinctIdStore[userId] = this.store.distinct_id;
    }
    this.storageHandler.set(distinctIdStoreKey, distinctIdStore);
    this.bufferStore();
  }
  /**
   * 自定义匿名id
   */
  identify(id: string) {
    if (!id) return;
    this.store.open_id = id;
    this.store.distinct_id = id;
    this.bufferStore();
  }

  /**
   * 自定义union匿名id
   */
  identifyUnion(data: IdentifyUnionInfo) {
    const { openid, unionid } = data;
    this.store.open_id = openid;
    this.store.union_id = unionid;
    // 有才替换 没有也记录一下方便排查
    unionid && (this.store.distinct_id = unionid);
    this.bufferStore();
  }

  /**
   * 组装上报数据
   */
  getTrackData(data: ReportIncidentInfo) {
    const pointData: PointData = {
      client_event_id: generateUUID(),
    } as PointData;
    this.store.user_id && (pointData.user_id = this.store.user_id);
    this.store.open_id && (pointData.open_id = this.store.open_id);
    this.store.union_id && (pointData.union_id = this.store.union_id);
    this.store.initial_id && (pointData.initial_id = this.store.initial_id);
    this.store.universal_id && (pointData.universal_id = this.store.universal_id);
    this.store.session_id && (pointData.session_id = this.store.session_id);

    // 如果是由跳转进来的，则匿名id取来源id
    pointData.distinct_id = this.store.latestTrafficSourceType
      ? this.store.sourceDistinctId || this.store.distinct_id
      : this.store.distinct_id;
    const timenow = this.systemInfoGetter.getTimestamp();
    pointData.lib = {
      $terminal: this.$terminal,
      $lib_version: config.version,
    };
    pointData.properties = {
      $latest_traffic_source_type:
        this.store.latestTrafficSourceType || this.systemInfoGetter.getLatestTrafficSourceType(),
      $latest_referrer: this.store.latestReferrer || this.systemInfoGetter.getLatestReferrer(),
      $timezone_offset: new Date().getTimezoneOffset(),
      $viewport_width: this.systemInfoGetter.getViewportWidth(),
      $viewport_height: this.systemInfoGetter.getViewportHeight(),
      $screen_height: this.systemInfoGetter.getScreenHeight(),
      $screen_top: this.systemInfoGetter.getScrollTop(),
      $url: this.systemInfoGetter.getUrl(),
      $url_path: this.systemInfoGetter.getUrlPath(),
      $referrer: this.systemInfoGetter.getReferrer(),
      $is_first_day: this.getIsFirstDay(timenow),
      $is_first_time: !this.store.firstVisitTime,
      $is_login: !!this.store.is_login,
    };
    // 注入启动参数
    this.appendLaunchQuery(pointData);
    // 注入生命周期属性
    for (const key in this.cycleProperties) {
      if (!isEmpty(this.cycleProperties[key]))
        pointData.properties[camelCaseFormattedUnderscore(key)] = this.cycleProperties[key];
    }
    const title = this.systemInfoGetter.getTitle();
    if (title) pointData.properties.$title = title;
    pointData.event_type = data.eventType || eventEnum.TRACK;
    pointData.event_name = data.key;
    pointData.event_time = Number(timenow.toString().slice(0, 10));
    pointData.event_date = pointData.event_time;
    pointData.event_millitime = timenow;
    pointData.properties.$event_duration = this.systemInfoGetter.getPageDurationTime();
    if (data.$appear_duration) pointData.properties.$appear_duration = data.$appear_duration;
    // 添加点击属性
    if (data.eventType === eventEnum.PAGE_CLICK) {
      pointData.properties.$element_type = data.$element_type;
      data.$page_x && (pointData.properties.$page_x = data.$page_x);
      data.$page_y && (pointData.properties.$page_y = data.$page_y);
      data.$client_x && (pointData.properties.$client_x = data.$client_x);
      data.$client_y && (pointData.properties.$client_y = data.$client_y);
      data.$element_class_name && (pointData.properties.$element_class_name = data.$element_class_name);
      data.$element_content && (pointData.properties.$element_content = data.$element_content);
      data.$element_selector && (pointData.properties.$element_selector = data.$element_selector);
      data.$element_path && (pointData.properties.$element_path = data.$element_path);
    }
    // 附加公共属性
    const commProperties = this.store.commProperties;
    Object.assign(pointData.properties, this.parseProperties(commProperties));
    const userRegister =
      commProperties && (commProperties["$user_register_time"] || commProperties["$userRegisterTime"]);
    pointData.properties.$register_less_24h = userRegister
      ? userRegister * 1000 + 24 * 60 * 60 * 1000 > timenow
        ? 1
        : 0
      : 0;
    // 附加特定的公共属性组
    if (Array.isArray(data.commPropertieGroup)) {
      for (const groupKey of data.commPropertieGroup) {
        const group = this.store.commGroupProperties[groupKey];
        if (group) Object.assign(pointData.properties, this.parseProperties(group));
      }
    } else if (typeof data.commPropertieGroup === "string") {
      const group = this.store.commGroupProperties[data.commPropertieGroup];
      Object.assign(pointData.properties, this.parseProperties(group));
    }
    // 附加页面属性
    const currentPageProperties = this.pageProperties[this.systemInfoGetter.getUrl()] || {};
    Object.assign(pointData.properties, this.parseProperties(currentPageProperties));

    // 附加自定义属性
    const customProperties: any = data.customProperties;
    Object.assign(pointData.properties, this.parseProperties(customProperties));

    // 附加邀请属性
    if (this.inviteTime && (this.inviteUserId || this.inviteDistinctId)) {
      pointData.properties.$invite_time = this.inviteTime;
      if (this.inviteUserId) pointData.properties.$invite_user_id = this.inviteUserId;
      if (this.inviteDistinctId) pointData.properties.$invite_distinct_id = this.inviteDistinctId;
    }
    // 更新缓存
    this.store.lastVisitTime = pointData.event_time;
    if (!this.store.firstVisitTime) {
      this.store.firstVisitTime = pointData.event_millitime;
      this.bufferStore();
    }
    return this.formatTrackData(pointData);
  }
  /**
   * 格式化上报数据
   */
  formatTrackData(data: PointData) {
    if (!this.format) return data;
    for (const key in data) {
      const item = (data as any)[key];
      const formatKey = this.format(key);
      (data as any)[formatKey] = isObject(item) ? this.formatTrackData(item) : item;
      formatKey !== key && delete (data as any)[key];
    }
    return data;
  }
  /**
   * 解析属性值
   */
  parseProperties(value: any, prefix: string = "") {
    const params: Record<string, string | number | boolean | undefined> = {};
    const prefixStr = prefix ? camelCaseFormattedUnderscore(prefix) + "_" : "";
    if (isObject(value) && !Array.isArray(value)) {
      for (const key in value) {
        if (dynamicPropEnum.includes(key)) {
          appendDynamicAttribute(params, key, value[key]);
        } else {
          Object.assign(
            params,
            this.parseProperties(value[key], prefixStr + (/^(\$|[a-z]|_|\d)+$/gi.test(key) ? key : charCodeStr(key)))
          );
        }
      }
    } else {
      params[camelCaseFormattedUnderscore(prefix)] = value;
    }
    return params;
  }
  /**
   * 判断是否首日访问
   */
  getIsFirstDay(timenow: number) {
    if (!this.store.firstVisitTime) return true;
    return new Date(this.store.firstVisitTime).getDate() === new Date(timenow).getDate();
  }
  onBeforeLaunch() {
    this.cycleProperties = {};
  }
  onLaunch() {
    // 获取启动参数-更新数据池数据
    this.dataPoll.modifyAny((pointData: PointData) => {
      this.appendLaunchQuery(pointData);
    });
  }
  onPageLoad() {
    const options = this.systemInfoGetter.getOnLoadQuery();
    // 邀请参数注入 邀请用户id 匿名id 邀请时间
    if (options.dc_st && (options.dc_uid || options.dc_did)) {
      this.inviteUserId = options.dc_uid as string;
      this.inviteDistinctId = options.dc_did as string;
      this.inviteTime = options.dc_st as number;
    }
    // 邀请的活动id、商品id注入,在商品详情异步获取未完成的情况下，至少保证goodsId能正常上报
    if (options.dc_acp || options.id) {
      this.appendCycleProperties({
        activityId: options.dc_acp,
        goodsId: options.id,
      });
    }
    // 注入页面启动参数到页面属性
    const onLoadQuery = this.systemInfoGetter.getOnLoadQuery();
    const properties: Record<string, any> = {};
    for (const key in onLoadQuery) {
      if ((onLoadQuery[key] && onLoadQuery[key] !== "undefined") || onLoadQuery[key] === 0)
        properties["onload_" + camelCaseFormattedUnderscore(key)] = onLoadQuery[key];
    }
    this.appendPageProperties(properties);
  }
  // 程序关闭
  onUnload() {
    this.storageHandler.set(storageKey, this.store);
  }
  /**
   * 页面显示
   */
  onBeforeShow() {
    if (lib !== libEnum.ALIPAY_MINI && lib !== libEnum.WEB) {
      const pages = getCurrentPages();
      const len = pages.length;
      if (len && len > 1) {
        const curPage = pages[len - 1];
        const lastPage = pages[len - 2];
        // 页面显示时候的路径和上一次路径相同，并且参数不变
        if (curPage && lastPage && curPage.route === lastPage.route && curPage.options === lastPage.options) {
          this.pageProperties[this.systemInfoGetter.getUrl()] = this.bufferPageProperties;
        }
      }
    } else {
      const url = this.systemInfoGetter.getUrl();
      if (url === this.preUrl) {
        // 页面显示时候的路径和上一次路径相同，说明没有切换页面
        this.pageProperties[this.systemInfoGetter.getUrl()] = this.bufferPageProperties;
      }
    }
  }
  /**
   * 页面隐藏
   */
  onPageHide() {
    this.preUrl = this.systemInfoGetter.getUrl();
  }
  /**
   * 页面关闭之后暂缓页面级缓存
   */
  onAfterPageHide() {
    this.bufferPageProperties = this.pageProperties[this.systemInfoGetter.getUrl()] || {};
  }
  /**
   * 页面销毁之后清除页面级缓存
   */
  onAfterPageUnload() {
    if (this.pageProperties[this.systemInfoGetter.getUrl()]) {
      delete this.pageProperties[this.systemInfoGetter.getUrl()];
    }
  }
  /**
   * 自定义来源匿名id
   */
  identifySource(arg: SourceArg) {
    if (!arg.id) return;
    this.store.sourceDistinctId = arg.id;
    this.store.latestTrafficSourceType = arg.latestTrafficSourceType;
    this.store.latestReferrer = arg.latestReferrer;
    this.bufferStore();
  }
  /**
   * 清除来源匿名id
   */
  clearSource() {
    delete this.store.sourceDistinctId;
    this.bufferStore();
  }
  /**
   * 最近一次站外流量来源类型
   */
  clearLatestTrafficSource() {
    delete this.store.latestTrafficSourceType;
    delete this.store.latestReferrer;
    this.bufferStore();
  }
  /**
   * 附加公共属性
   */
  appendCommProperties(data: any, groupKey: string) {
    if (groupKey) {
      for (const key in data) {
        const group = this.store.commGroupProperties[groupKey] || {};
        group[key] = data[key];
        this.store.commGroupProperties[groupKey] = group;
      }
    } else {
      for (const key in data) {
        this.store.commProperties[key] = data[key];
      }
    }

    // 如果是全局公共属性-更新数据池数据
    !groupKey &&
      this.dataPoll.modifyAny((pointData: PointData) => {
        Object.assign(pointData.properties, this.parseProperties(data));
      });
    this.bufferStore();
  }
  /**
   * 替换公共属性
   */
  replaceCommProperties(data: any, groupKey: string) {
    if (groupKey) {
      this.store.commGroupProperties[groupKey] && (this.store.commGroupProperties[groupKey] = {});
    } else {
      this.store.commProperties = {};
    }
    this.appendCommProperties(data, groupKey);
  }
  /**
   * 删除公共属性
   */
  removeCommProperties(...args: string[] | RemoveProperties[]) {
    if (args.findIndex((arg: string | RemoveProperties) => typeof arg !== "string") !== 0) {
      for (const group of args as RemoveProperties[]) {
        for (const key of group.value) {
          this.store.commGroupProperties[group.key] && delete this.store.commGroupProperties[group.key][key];
        }
      }
    } else {
      for (const key of args as string[]) {
        delete this.store.commProperties[key];
      }
    }
    this.bufferStore();
  }
  /**
   * 删除公共属性组
   */
  removeCommPropertiesGroup(...args: string[]) {
    for (const key of args) {
      delete this.store.commGroupProperties[key];
    }
    this.bufferStore();
  }
  /**
   * 附加页面属性
   */
  appendPageProperties(data: any) {
    const url = this.systemInfoGetter.getUrl();
    if (!this.pageProperties[this.systemInfoGetter.getUrl()]) {
      this.pageProperties[this.systemInfoGetter.getUrl()] = {};
    }
    Object.assign(this.pageProperties[this.systemInfoGetter.getUrl()], data);
    // 更新数据池数据
    this.dataPoll.modifyAny((pointData: PointData) => {
      if (pointData.properties.$url === url) Object.assign(pointData.properties, this.parseProperties(data));
    });
  }
  /**
   * 删除页面属性
   */
  removePageProperties(...args: string[]) {
    if (!this.pageProperties[this.systemInfoGetter.getUrl()]) return;
    for (const key in args) {
      delete this.pageProperties[this.systemInfoGetter.getUrl()][key];
    }
  }
  /**
   * 获取公共属性
   */
  getCommProperties(key: string, groupKey: string) {
    const commProperties = groupKey ? this.store.commGroupProperties[groupKey] || {} : this.store.commProperties;
    return (
      commProperties[key] ||
      commProperties[camelCaseFormattedUnderscore(key)] ||
      commProperties[UnderscoreToCamelCase(key)]
    );
  }
  /**
   * 附加公共属性到页面
   */
  appendCommPropertiesToPage(...args: string[]) {
    for (const groupKey of args || []) {
      const commProperties = this.store.commGroupProperties[groupKey] || {};
      this.appendPageProperties(commProperties);
    }
  }
  /**
   * 附加生命周期属性
   */
  appendCycleProperties(data: Record<string, any>): void {
    for (const key in data) {
      this.cycleProperties[key] = data[key];
    }
  }
  /**
   * 删除生命周期属性
   */
  removeCycleProperties(...args: string[]) {
    for (const key of args) {
      delete this.cycleProperties[key];
    }
  }
  /**
   * 获取生命周期属性
   */
  getCycleProperties(): Record<string, any> {
    return this.cycleProperties;
  }
}
