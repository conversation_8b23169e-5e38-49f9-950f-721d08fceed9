import { createApp } from "vue";
import App from "./App.vue";
import router from "./router";
import sensors from "../src/index";

export const app = createApp(App)

app.use(router).mount("#app");

sensors.init({
    serverUrl: 'https://data-analysis.rrzu.com/data-analysis/api/event/batch',
    bindServerUrl: 'https://data-analysis.rrzu.com/data-analysis/api/event/bind',
    isTrackSinglePage: true,
    showLog: true,
});