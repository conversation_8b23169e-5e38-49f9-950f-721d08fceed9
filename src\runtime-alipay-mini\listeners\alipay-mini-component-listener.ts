/**
 * Component重载监听器
 */
 export default class AlipayMiniSysComponentListener implements SubjectListener {
    observers: AlipayMiniSysComponentOvserver[] = [];
    register(observer: AlipayMiniSysComponentOvserver) {
        this.observers.push(observer);
    };
    remove(oberser: AlipayMiniSysComponentOvserver) {
        const index = this.observers.findIndex(item => item === oberser);
        if (index != -1) this.observers.splice(index, 1);
    };
    notify(component: object) {
        this.observers.forEach(observer => observer.onComponent(component));
    };

}