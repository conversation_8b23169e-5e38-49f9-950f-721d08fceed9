// 小程序Page方法
declare let Page: any;
// 小程序App方法
declare let App: any;
// 小程序组件方法
declare let Component: any;
// 支付宝小程序对象
declare const my: any;
// 获取路由栈
declare const getCurrentPages: any;

/**
 * 页面滚动条订阅者
 */
interface AlipayMiniScrollObserver extends Observer {
    onScrollChange(args: AlipayMiniScrollInfo): void;
}

/**
 * 滚动信息
 */
interface AlipayMiniScrollInfo {
    scrollHeight: number;
    scrollTop: number;
}

/**
 * Page重载订阅者
 */
interface AlipayMiniSysPageObserver extends Observer {
    onPage(definitionPage: object): void;
}

/**
 * App重载订阅者
 */
interface AlipayMiniSysAppObserver extends Observer {
    onApp(definitionApp: object): void;
}

/**
 * Component重载订阅者
 */
interface AlipayMiniSysComponentOvserver extends Observer {
    onComponent(definitionComponent: object): void;
}