import SubjectListenerImpl from "@/runtime-core/subject-listener-impl"
import UniappSensorsParaImpl from "../uniapp-sensors-para-impl";
import { libEnum } from "@/runtime-core/enum";
import { judgingEnvironment } from "@/utils";

const lib = judgingEnvironment();

/**
 * vue组件监听 Uniapp
 */
export default class UniappSysPageListener extends SubjectListenerImpl<UniappSysPageObserver> implements UniappSysVueComponentObserver {
    init(data: UniappSensorsParaImpl) {
        data.sysVueComponentListener.register(this);
    }
    /**
     * 监听app触发
     * @param app vue 对象
     */
    onVueComponent(component: any): void {
        lib === libEnum.UNIAPP_APP_PLUS && component.inheritAttrs === false && this.notify(component);
        lib === libEnum.UNIAPP_H5 && this.notify(component);
    }
    notify(page: any) {
        this.observers.forEach(observer => observer.onPage(page));
    }
}