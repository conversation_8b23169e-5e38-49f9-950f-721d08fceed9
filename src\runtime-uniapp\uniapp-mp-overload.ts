/*
 * @Author: itnewdata <EMAIL>
 * @Date: 2022-08-08 22:42:07
 * @LastEditors: itnewdata <EMAIL>
 * @LastEditTime: 2023-03-22 11:25:12
 * @FilePath: \data_analysis_sdk\src\runtime-uniapp\uniapp-mp-overload.ts
 * @Description: uniapp 系统方法重载
 */

import UniappSysComponentListener from "./listeners/uniapp-sys-component-listener";
import UniappSysPageListener from "./listeners/uniapp-sys-page-listener";
import { libEnum } from "@/runtime-core/enum";
import { judgingEnvironment } from "@/utils";

const lib = judgingEnvironment();

class UniappMpOverload {
  private oldPage: any; // 缓存系统Page函数
  private oldComponent: any; // 缓存系统Component函数
  private isAlreadyInit: boolean = false; // 是否已经初始化
  init(sysPageListener: UniappSysPageListener, componentListener: UniappSysComponentListener) {
    if (this.isAlreadyInit) return;

    const that = this;

    /**
     * 重载系统页面方法
     */
    function createPage(definitionPage: any) {
      sysPageListener.notify(definitionPage);
      return that.oldPage(definitionPage);
    }

    /**
     * 重载系统组件方法
     */
    function createComponent(definitionComponent: any) {
      componentListener.notify(definitionComponent);
      return that.oldComponent(definitionComponent);
    }

    if (lib === libEnum.UNIAPP_BYTEDANCE) {
      this.oldPage = tt.createPage;
      this.oldComponent = tt.createComponent;
      tt.createPage = createPage;
      tt.createComponent = createComponent;
    } else if (lib === libEnum.UNIAPP_WEIXIN) {
      this.oldPage = wx.createPage;
      this.oldComponent = wx.createComponent;
      wx.createPage = createPage;
      wx.createComponent = createComponent;
    } else if (lib === libEnum.UNIAPP_KUAISHOU) {
      this.oldPage = ks.createPage;
      this.oldComponent = ks.createComponent;
      ks.createPage = createPage;
      ks.createComponent = createComponent;
    } else if (lib === libEnum.UNIAPP_ALIPAY || lib === libEnum.UNIAPP_DING) {
      this.oldPage = my.createPage;
      this.oldComponent = my.createComponent;
      my.createPage = createPage;
      my.createComponent = createComponent;
    } else if (lib === libEnum.UNIAPP_JD) {
      this.oldPage = jd.createPage;
      this.oldComponent = jd.createComponent;
      jd.createPage = createPage;
      jd.createComponent = createComponent;
    } 

    this.isAlreadyInit = true;
  }
}

const overload = new UniappMpOverload();

export default overload;
