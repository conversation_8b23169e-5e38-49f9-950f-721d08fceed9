import { createRouter, createWeb<PERSON>ash<PERSON>ist<PERSON>, RouteRecordRaw } from "vue-router";

const routes: Array<RouteRecordRaw> = [
  {
    path: "/",
    name: "Routing",
    meta: {
      title: "路由页",
    },
    component: () => import("@d/views/routing/index.vue"),
  },
  {
    path: '/pageViewA',
    name: 'PageViewA',
    meta: {
      title: "$pageview 测试A页面"
    },
    component: () => import("@d/views/page-view/a.vue")
  },
  {
    path: '/pageviewB',
    name: 'PageViewB',
    meta: {
      title: "$pageview 测试B页面"
    },
    component: () => import("@d/views/page-view/b.vue")
  },
  {
    path: '/pageclick',
    name: 'PageClick',
    meta: {
      title: "$pageclick 测试页面"
    },
    component: () => import("@d/views/page-click/index.vue")
  },
  {
    path: '/pagestay',
    name: 'PageStay',
    meta: {
      title: "$pagestay 测试页面"
    },
    component: () => import("@d/views/page-stay/index.vue")
  },
  {
    path: '/mphide',
    name: 'MpHide',
    meta: {
      title: "$mphide 测试页面"
    },
    component: () => import("@d/views/mp-hide/index.vue")
  },
  {
    path: '/pageExposure',
    name: 'PageExposure',
    meta: {
      title: '$PageExposure 测试页面'
    },
    component: () => import("@d/views/page-exposure/index.vue")
  },
  {
    path: '/touch',
    name: 'Touch',
    meta: {
      title: '$Touch 测试页面'
    },
    component: () => import("@d/views/touch/index.vue")
  }
];

const router = createRouter({
  history: createWebHashHistory(),
  routes,
});

router.beforeEach((to, from, next) => {
  /* 路由发生变化时，遍历修改页面title */
  if (to.meta.title) {
    let dom:any = document;
    dom.title = to.meta.title;
  }
  next();
});

export default router;
