import SubjectListenerImpl from "@/runtime-core/subject-listener-impl";
import AlipayMiniSensorsParaImpl from "../alipay-mini-sensors-para-impl";

/**
 * 显现监听器 支付宝小程序
 */
export default class AlipayMiniAppearListener extends SubjectListenerImpl<AppearObserver> implements AppearListener,
    AlipayMiniSysPageObserver, AlipayMiniSysComponentOvserver, PageObserver {
    private map: Record<string, any> = {};
    private maxTimeStramp: number = 10000;
    init(data: AlipayMiniSensorsParaImpl) {
        data.sysPageListener.register(this);
        data.sysComponentListener.register(this);
        data.pageListener.register(this);
    }
    onPage(page: any) {
        this.listenerPage(page);
    }
    onComponent(component: any) {
        this.listenerComponent(component);
    }
    /**
     * 触发通知
     */
    handleNotify(key: string, disappearTimeStamp = Date.now()) {
        const appearEvent = this.map[key];
        if (appearEvent) {
            appearEvent.disappearTimeStamp = disappearTimeStamp;
            this.notify(appearEvent);
            delete this.map[key];
        }
    }
    /**
     * 监听页面
     */
    listenerPage(page: any) {
        const oldOnAppear = page.onAppear || function () {};
        const that = this;
        page.onAppear = function (e: any) {
            try {
                const key = JSON.stringify(e.currentTarget);
                that.map[key] = e;
                setTimeout(() => {
                    that.handleNotify(key);
                }, that.maxTimeStramp);
            } catch(err: any) {
                console.warn("AlipayMiniAppearListener -> listenerPage", err);
            }
            oldOnAppear.call(this, ...arguments);
        };
        const oldOnDisappear = page.onDisappear || function () { };
        page.onDisappear = function (e: any) {
            try {
                const key = JSON.stringify(e.currentTarget);
                that.handleNotify(key, e.timeStamp);
            } catch (err: any) {
                console.warn("AlipayMiniAppearListener -> listenerPage", err);
            }
            oldOnDisappear.call(this, ...arguments);
        };
    }
    /**
     * 监听组件
     */
    listenerComponent(component: any) {
        const methods = component.methods || {};
        const oldOnAppear = methods.onAppear || function () {};
        const that = this;
        methods.onAppear = function (e: any) {
            try {
                const key = JSON.stringify(e.currentTarget);
                that.map[key] = e;
                setTimeout(() => {
                    that.handleNotify(key);
                }, that.maxTimeStramp);
            } catch(err: any) {
                console.warn("AlipayMiniAppearListener -> listenerComponent", err);
            }
            oldOnAppear.call(this, ...arguments);
        };
        const oldOnDisappear = methods.onDisappear || function () { };
        methods.onDisappear = function (e: any) {
            try {
                const key = JSON.stringify(e.currentTarget);
                that.handleNotify(key, e.timeStamp);
            } catch (err: any) {
                console.warn("AlipayMiniAppearListener -> listenerPage", err);
            }
            oldOnDisappear.call(this, ...arguments);
        };
        component.methods = methods;
    }
    /**
     * 页面隐藏清除map缓存
     */
    onPageHide() {
        for (const key in this.map) {
            this.handleNotify(key);
        }
    }
    notify(appearEvent: any): void {
        this.observers.forEach(observer => observer.onAppear(appearEvent));
    }
}