import SubjectListenerImpl from "@/runtime-core/subject-listener-impl";
import { isFunction } from "@/utils";
import AlipayMiniSensorsParaImpl from "../alipay-mini-sensors-para-impl";

// 系统页面属性
const systemAttr: any = {
    onLoad: 1,
    onShow: 1,
    onHide: 1,
    onReady: 1,
    onUnload: 1,
    onTitleClick: 1,
    onPullDownRefresh: 1,
    onReachBottom: 1,
    onPageScroll: 1,
    onResize: 1,
    onTabItemTap: 1,
    onOptionMenuClick: 1,
    onPopMenuClick: 1,
    onPullIntercept: 1,
    onAddToFavorites: 1,
    onShareAppMessage: 1,
    onShareTimeline: 1,
    eventHandler: 1,
    data: 1
};

/**
 * 页面点击监听器 支付宝小程序端
 */
export default class AlipayMiniClickListener extends SubjectListenerImpl<ClickObserver> 
        implements ClickListener, AlipayMiniSysPageObserver, AlipayMiniSysComponentOvserver
{
    observers: ClickObserver[] = [];
    onPage(page: any) {
        this.listenerCustomMethod(page);
    }
    onComponent(component: any) {
        // 是否有自定义方法
        if (component.methods) {
            const that = this;
            for (const key in component.methods) {
                if (!isFunction(component.methods[key])) continue;
                const oldMethod = component.methods[key];
                component.methods[key] = function() {
                    try {
                        // 如果是点击事件
                        if (arguments[0] && arguments[0].type === "tap") {
                            that.notify(arguments[0], key);
                        }
                    } catch(err) {
                        console.warn(err);
                    }
                    return oldMethod.call(this, ...arguments);
                }
            }
        }
    }
    init(data: AlipayMiniSensorsParaImpl) {
        data.sysPageListener.register(this);
        data.sysComponentListener.register(this);
    }
    private listenerCustomMethod(page: any) {
        const that = this;
        for (const key in page) {
            if (systemAttr[key] || !isFunction(page[key])) continue;
            const oldMethod = page[key];
            page[key] = function() {
                try {
                    // 如果是点击事件
                    if (arguments[0] && arguments[0].type === "tap") {
                        that.notify(arguments[0], key);
                    }
                } catch(err) {
                    console.warn('AlipayMiniClickListener -> init', err);
                }
                return oldMethod.call(this, ...arguments);
            };
        }
    }
    notify(e: MouseEvent, funcName: string) {
        this.observers.forEach(observer => observer.onClickChange(e, funcName));
    }
}