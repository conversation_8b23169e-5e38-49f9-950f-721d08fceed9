import SensorsParaImpl from "@/runtime-core/sensors-para-impl";
import SubjectListenerImpl from "@/runtime-core/subject-listener-impl";

/**
 * 页面停留监听器 支付宝小程序端
 */
export default class AlipayMiniCaptureScreenListener
  extends SubjectListenerImpl<CaptureObserver>
  implements CaptureScreenListener
{
  observers: CaptureObserver[] = [];
  notifyFunc: (e: any) => void;
  constructor() {
    super();
    this.notifyFunc = this.notify.bind(this);
  }

  init() {
    my.onUserCaptureScreen(this.notifyFunc);
  }
  notify(e: any): void {
    this.observers.forEach((observer) => observer.onCaptureScreen(e));
  }
}
