import SubjectListenerImpl from "@/runtime-core/subject-listener-impl";
import JsSensorsParaImpl from "../js-sensors-para-impl";

/**
 * 曝光监听器 浏览器端
 */
export default class JsExposureListener extends SubjectListenerImpl<ExposureObserver> implements ExposureListener, PageObserver, SuddenChangeObserver {
    observer: any;
    init(data: JsSensorsParaImpl) {
        data.pageListener.register(this);
        data.suddenChangeListener.register(this);
    }
    onPageShow(): void {
        setTimeout(() => {
            this.listenerExposure();
        }, 0)
    }
    onSuddenChange(mutations: any): void {
        this.observer.disconnect();
        this.listenerExposure();
    }
    /**
     * 监听页面组件曝光
     */
    listenerExposure() {
        this.observer = new IntersectionObserver(arr => {
            for (let i=0; i<arr.length; i++) {
                if (arr[i].isIntersecting) {
                    if (arr[i].target.classList.contains('sensors_exposure_already')) return;
                    this.notify(arr[i]);
                    arr[i].target.classList.add('sensors_exposure_already');
                }
            }
        });
        const targets = document.querySelectorAll('.sensors_exposure');
        for (let i=0; i<targets.length; i++) this.observer.observe(targets[i]);
    }
    notify(e: any): void {
        this.observers.forEach(observer => observer.onExposure(e));
    }
}