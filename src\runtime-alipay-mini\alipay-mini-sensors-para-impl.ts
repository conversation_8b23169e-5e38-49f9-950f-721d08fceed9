import SensorsParaImpl, { SensorsParaConstructorArgs } from "@/runtime-core/sensors-para-impl";
import AlipayMiniSysPageListener from "./listeners/alipay-mini-sys-page-listener";
import AlipayMiniSysAppListener from "./listeners/alipay-mini-sys-app-listener";
import Ali<PERSON>yMiniScrollListener from "./listeners/alipay-mini-scroll-listener";
import AlipayMiniSystemInfoGetter from "./alipay-mini-system-info-getter";
import overload from "./alipay-mini-overload";
import AlipayMiniSysComponentListener from "./listeners/alipay-mini-component-listener";
import AlipayMiniSourceListener from "./listeners/alipay-mini-source-listener";

interface AlipayMiniSensorsParaConstructorArgs extends SensorsParaConstructorArgs {
    sysPageListener: AlipayMiniSysPageListener;
    sysAppListener: AlipayMiniSysAppListener;
    sysComponentListener: AlipayMiniSysComponentListener;
    scrollListener: Ali<PERSON>yMiniScrollListener;
    pageLoadListener: PageLoadListener;
    sourceListener: AlipayMiniSourceListener;
    tabItemListener:TabItemListener;
}

/**
 * 传感器配置数据 - 缓存数据 浏览器端
 */
 class AliapyMiniSensorsParaStore implements SensorsParaStore {
    distinct_id = '';
    user_id = '';
    open_id = '';
    session_id = '';
    initial_id = '';
    commProperties = {};
    commGroupProperties = {};
    sourceDistinctId?: string; // 来源匿名id
    latestTrafficSourceType?: string; // 最近一次站外流量来源类型
}

/**
 * 传感器配置数据 支付宝小程序端
 */
export default class AlipayMiniSensorsParaImpl extends SensorsParaImpl {
    sysPageListener: AlipayMiniSysPageListener;
    sysAppListener: AlipayMiniSysAppListener;
    sysComponentListener: AlipayMiniSysComponentListener;
    scrollListener: AlipayMiniScrollListener;
    sourceListener: AlipayMiniSourceListener;
    tabItemListener:TabItemListener;
    store = new AliapyMiniSensorsParaStore();
    
    constructor(props: AlipayMiniSensorsParaConstructorArgs) {
        super(props);
        this.sysPageListener = props.sysPageListener;
        this.sysAppListener = props.sysAppListener;
        this.sysComponentListener = props.sysComponentListener;
        this.scrollListener = props.scrollListener;
        this.sourceListener = props.sourceListener;
        this.tabItemListener=props.tabItemListener;
    }
    init(data: InitData) {
        overload.init(this.sysPageListener, this.sysAppListener, this.sysComponentListener);
        super.init(data);
        this.scrollListener.init(this);
        this.sourceListener.init(this);
        this.tabItemListener.init(this);
    }
    getTrackData(data: ReportIncidentInfo) {
        const pointData = super.getTrackData(data);
        const systemInfoGetter = this.systemInfoGetter as AlipayMiniSystemInfoGetter;
        pointData.lib.$model = systemInfoGetter.getModel();
        pointData.lib.$app_id = systemInfoGetter.getAppId();
        pointData.lib.$pixel_ratio = systemInfoGetter.getPixelRatio();
        pointData.lib.$language = systemInfoGetter.getLanguage();
        pointData.lib.$app_version = systemInfoGetter.getAppVersion();
        pointData.lib.$storage = systemInfoGetter.getStorage();
        pointData.lib.$current_battery = systemInfoGetter.getCurrentBattery();
        pointData.lib.$system_version = systemInfoGetter.getSystemVersion();
        pointData.lib.$system_platform = systemInfoGetter.getSystemPlatform();
        pointData.lib.$brand = systemInfoGetter.getBrand();
        pointData.lib.$font_size_setting = systemInfoGetter.getFontSizeSetting();
        pointData.lib.$version_code=systemInfoGetter.getVersionCode();
        pointData.lib.$wifi_ssid=systemInfoGetter.getWifiSsid();
        pointData.lib.$wifi_bssid=systemInfoGetter.getWifiBssid();
        const miniVersion = systemInfoGetter.getMiniVersion();
        miniVersion && (pointData.lib.$mini_version = miniVersion);
        const networkType = systemInfoGetter.getNetworkType();
        networkType && (pointData.lib.$network_type = networkType);
        pointData.properties.$is_visit_from_outside = systemInfoGetter.getVisitFromOutside();
        pointData.properties.$referrer_app_id = systemInfoGetter.getReferrerAppId();
        pointData.properties.$is_collected = systemInfoGetter.getIsCollected();
        pointData.properties.page_list = systemInfoGetter.getPageList();
        pointData.properties.page_version = systemInfoGetter.getPageVersion()
        pointData.properties.position_list=systemInfoGetter.getPositionList();
        pointData.properties.index_module_key_list = systemInfoGetter.getIndexModuleKeyList()
        pointData.properties.module_position_sort_list = systemInfoGetter.getModulePositionSortList()
        return pointData;
    }
}