import SubjectListenerImpl from "@/runtime-core/subject-listener-impl";
import UniappSensorsParaImpl from "../uniapp-sensors-para-impl";

/**
 * 页面onLoad监听器 uniapp
 */
export default class UniappPageLoadListener extends SubjectListenerImpl<PageLoadObserver> implements PageLoadListener, UniappSysPageObserver {
    init(data: UniappSensorsParaImpl) {
        data.sysPageListener.register(this);
    }
    onPage(page: any) {
        this.listenerPageOnLoad(page);
    }
    /**
     * 监听Page onLoad方法
     */
    listenerPageOnLoad(page: any) {
        const oldOnLoad = page.onLoad || function () {};
        const that = this;
        page.onLoad = function (options: any) {
            try {
                that.notify(options);
            } catch(err) {
                console.warn('UniappPageListener -> listenerPageOnShow', err);
            }
            oldOnLoad.call(this, ...arguments);
        }
    }
    notify(options: any) {
        this.observers.forEach(observer => observer.onBeforePageLoad && observer.onBeforePageLoad(options));
        this.observers.forEach(observer => observer.onPageLoad && observer.onPageLoad(options));
    }
}
