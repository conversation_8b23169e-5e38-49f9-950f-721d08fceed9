import { base64Encode, packageUrl } from "@/utils";

/**
 * 小程序请求处理器实现
 */
export default class AliPayMiniRequestHandler implements RequestHandler {
    serverUrl: string = '';
    dataSendTimeout: number = 3000;  // 上报请求超时时间
    extraRequestHeaders: Record<string, any> = {}; // 额外请求头
    encryption(list: PointData[] | PointData | any) {
        return base64Encode(encodeURIComponent(JSON.stringify(list)));
    }
    send(data: PointData[]) {
        return this.sendRequest(this.serverUrl, data);
    }
    sendSing(data: PointData) {
        return this.send([data]);
    }
    addExtraRequestHeaders(headers: Record<string, any>): void {
        this.extraRequestHeaders = headers;
    }
    sendRequest(url: string, data: object) {
        const base64Data = this.encryption(data);
        url = packageUrl(url, {}, {data: base64Data});
        return new Promise((resolve,reject) => {
            my.request({
                url,
                method: "POST",
                data: {
                    data: base64Data
                },
                headers: {
                    'content-type': 'application/x-www-form-urlencoded',
                    ...this.extraRequestHeaders
                },
                timeout: this.dataSendTimeout,
                success: (res: any) => {
                    resolve(res);
                },
                fail: (err: any) => {
                    reject(err);
                }
            });
        });
    }
    getUniversalId(url: string, data: {
        channel_user_id: string;
        channel_type: string;
        user_id?:number;
    }) {
        const base64Data = this.encryption(data);
        url = packageUrl(url, {}, {data: base64Data});
        return new Promise((resolve,reject) => {
            my.request({
                url,
                method: "POST",
                data:{
                    data: base64Data
                },
                headers: {
                    'content-type': 'application/x-www-form-urlencoded',
                    ...this.extraRequestHeaders
                },
                success: (res: any)=> resolve(res),
                fail: (err: any) => reject(err)
            });
        });
    }
}