import DataPollImpl from "./runtime-core/data-poll-impl";
import UniappSensorsImpl from "./runtime-uniapp/uniapp-sensors-impl";
import UniappSensorsParaImpl from "./runtime-uniapp/uniapp-sensors-para-impl";
import UniappSysAppListener from "./runtime-uniapp/listeners/uniapp-sys-app-listener";
import UniappStorageHandler from "./runtime-uniapp/uniapp-storage-handler";
import UniappRequestHandler from "./runtime-uniapp/uniapp-request-handler";
import UniappPageListener from "./runtime-uniapp/listeners/uniapp-page-listener";
import UniappClickListener from "./runtime-uniapp/listeners/uniapp-click-listener";
import UniappStayListener from "./runtime-uniapp/listeners/uniapp-stay-listener";
import UniappSystemInfoGetter from "./runtime-uniapp/uniapp-system-info-getter";
import UniappUnloadListener from "./runtime-uniapp/listeners/uniapp-unload-listener";
import UniappLaunchListener from "./runtime-uniapp/listeners/uniapp-launch-listener";
import UniappPageLoadListener from "./runtime-uniapp/listeners/uniapp-page-load-listener";
import UniappSysPageListener from "./runtime-uniapp/listeners/uniapp-sys-page-listener";
import UniappSysComponentListener from "./runtime-uniapp/listeners/uniapp-sys-component-listener";
import UniappScrollListener from "./runtime-uniapp/listeners/uniapp-scroll-listener";
import DefaultSensors from "./runtime-default/default-sensors";
import UniappPageUnloadListener from "./runtime-uniapp/listeners/uniapp-page-unload-listener";
import UniappShareListener from "./runtime-uniapp/listeners/uniapp-share-listener";
import UniappTouchListener from "./runtime-uniapp/listeners/uniapp-touch-listener";
import UniappExposureListener from "./runtime-uniapp/listeners/uniapp-exposure-listener";
import UniappAppearListener from "./runtime-uniapp/listeners/uniapp-appear-listener";
import UniappSysVueComponentListener from "./runtime-uniapp/listeners/uniapp-sys-vue-component-listener";
import SensorsImpl from "./runtime-core/sensors-impl";
import JsDataPollImpl from "./runtime-browser/js-data-poll-impl";
import JsSensorsParaImpl from "./runtime-browser/js-sensors-para-impl";
import JsPageListener from "./runtime-browser/listener/js-page-listener";
import JsSystemInfoGetter from "./runtime-browser/js-system-info-getter";
import JsStorage from "./runtime-browser/js-storage-handler";
import JsRequestHandler from "./runtime-browser/js-request-handler";
import JsClickListener from "./runtime-browser/listener/js-click-listener";
import JsStayListener from "./runtime-browser/listener/js-stay-listener";
import JsUnloadListener from "./runtime-browser/listener/js-unload-listener";
import JsLaunchListener from "./runtime-browser/listener/js-launch-listener";
import JsExposureListener from "./runtime-browser/listener/js-exposure-listener";
import JsSuddenChangeListener from "./runtime-browser/listener/js-sudden-change-listener";
import JsPageLoadListener from "./runtime-browser/listener/js-page-load-listener";
import JsPageUnloadListener from "./runtime-browser/listener/js-page-unload-listener";
import JsShareListener from "./runtime-browser/listener/js-share-listener";
import JsTouchListener from "./runtime-browser/listener/js-touch-listener";
import JsAppearListener from "./runtime-browser/listener/js-appear-listener";
import UniappTabItemListener from "./runtime-uniapp/listeners/uniapp-tab-item-listener";
import UniAppCaptureScreenListener from "./runtime-uniapp/listeners/uniapp-capture-screen-listener";
import { judgingEnvironment } from "./utils";
import { libEnum } from "./runtime-core/enum";

const lib = judgingEnvironment();

/**
 * 传感器
 */
let sensors: Sensors = new DefaultSensors();

const uniappAndAppLib = [
  libEnum.UNIAPP_APP_PLUS,
  libEnum.UNIAPP_WEIXIN,
  libEnum.UNIAPP_BYTEDANCE,
  libEnum.UNIAPP_ALIPAY,
  libEnum.UNIAPP_DING,
  libEnum.UNIAPP_KUAISHOU,
  libEnum.UNIAPP_JD,
];

// uniapp app plus端
if (uniappAndAppLib.includes(lib as LibEnumType)) {
  try {
    if (!uni.sensors) {
      const storageHandler = new UniappStorageHandler();
      const dataPoll = new DataPollImpl({ storageHandler });
      const sysAppListener = new UniappSysAppListener();
      const requestHandler = new UniappRequestHandler();
      const pageUnloadListener = new UniappPageUnloadListener();
      const pageLoadListener = new UniappPageLoadListener();
      const pageListener = new UniappPageListener();
      const clickListener = new UniappClickListener();
      const stayListener = new UniappStayListener();
      const systemInfoGetter = new UniappSystemInfoGetter();
      const launchListener = new UniappLaunchListener();
      const unloadListener = new UniappUnloadListener();
      const sysPageListener = new UniappSysPageListener();
      const sysVueComponentListener = new UniappSysVueComponentListener();
      const sysComponentListener = new UniappSysComponentListener();
      const scrollListener = new UniappScrollListener();
      const exposureListener = new UniappExposureListener();
      const appearListener = new UniappAppearListener();
      const shareListener = new UniappShareListener();
      const touchListener = new UniappTouchListener();
      const tabItemListener = new UniappTabItemListener();
      const captureScreenListener = new UniAppCaptureScreenListener();
      const para = new UniappSensorsParaImpl({
        dataPoll,
        requestHandler,
        pageListener,
        clickListener,
        stayListener,
        systemInfoGetter,
        storageHandler,
        unloadListener,
        sysAppListener,
        launchListener,
        pageLoadListener,
        pageUnloadListener,
        sysPageListener,
        sysComponentListener,
        scrollListener,
        exposureListener,
        appearListener,
        shareListener,
        touchListener,
        sysVueComponentListener,
        tabItemListener,
        captureScreenListener,
      });
      sensors = new UniappSensorsImpl({
        para,
        dataPoll,
        requestHandler,
        pageLoadListener,
        pageListener,
        clickListener,
        stayListener,
        systemInfoGetter,
        sysAppListener,
        launchListener,
        unloadListener,
        exposureListener,
        appearListener,
        shareListener,
        touchListener,
        tabItemListener,
        captureScreenListener,
      });
      uni.sensors = sensors;
    }
    sensors = uni.sensors;
  } catch (err) {
    console.warn(err);
    uni.sensors = sensors;
  }
}

// uniapp h5
if (lib === libEnum.UNIAPP_H5) {
  try {
    if (!uni.sensors) {
      const sysAppListener = new UniappSysAppListener();
      const sysVueComponentListener = new UniappSysVueComponentListener();
      const sysPageListener = new UniappSysPageListener();
      const tabItemListener = new UniappTabItemListener();
      const storageHandler = new JsStorage();
      const dataPoll = new JsDataPollImpl({ storageHandler });
      const requestHandler = new JsRequestHandler();
      const pageLoadListener = new JsPageLoadListener();
      const pageUnloadListener = new JsPageUnloadListener();
      const pageListener = new JsPageListener();
      const clickListener = new JsClickListener();
      const stayListener = new JsStayListener();
      const systemInfoGetter = new JsSystemInfoGetter();
      const unloadListener = new JsUnloadListener();
      const launchListener = new JsLaunchListener();
      const suddenChangeListener = new JsSuddenChangeListener();
      const exposureListener = new JsExposureListener();
      const appearListener = new JsAppearListener();
      const shareListener = new JsShareListener();
      const touchListener = new JsTouchListener();
      const para = new JsSensorsParaImpl({
        dataPoll,
        requestHandler,
        pageLoadListener,
        pageUnloadListener,
        pageListener,
        sysPageListener,
        clickListener,
        stayListener,
        systemInfoGetter,
        storageHandler,
        launchListener,
        unloadListener,
        exposureListener,
        appearListener,
        suddenChangeListener,
        shareListener,
        touchListener,
        sysAppListener,
        sysVueComponentListener,
        tabItemListener,
      });
      sensors = new SensorsImpl({
        para,
        dataPoll,
        requestHandler,
        pageLoadListener,
        pageListener,
        clickListener,
        stayListener,
        systemInfoGetter,
        launchListener,
        unloadListener,
        exposureListener,
        appearListener,
        touchListener,
        shareListener,
        sysAppListener,
        sysVueComponentListener,
        tabItemListener,
      });
      uni.sensors = sensors;
    }
    sensors = uni.sensors;
  } catch (err) {
    console.warn(err);
    uni.sensors = sensors;
  }
}

// uniapp 未知默认处理
if (lib === libEnum.UNIAPP_UNKNOWN) {
  try {
    if (!uni.sensors) {
      sensors = new DefaultSensors();
      uni.sensors = sensors;
    }
    sensors = uni.sensors;
  } catch (err) {
    console.warn(err);
    uni.sensors = sensors;
  }
}

export default sensors as Sensors;
