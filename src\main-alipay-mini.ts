import DataPollImpl from "./runtime-core/data-poll-impl";
import AlipayMiniSensorsParaImpl from "./runtime-alipay-mini/alipay-mini-sensors-para-impl";
import AlipayMiniStayListener from "./runtime-alipay-mini/listeners/alipay-mini-stay-listener";
import AliPayMiniRequestHandler from "./runtime-alipay-mini/alipay-mini-request-handler";
import AlipayMiniSystemInfoGetter from "./runtime-alipay-mini/alipay-mini-system-info-getter";
import AlipayMiniPageListener from "./runtime-alipay-mini/listeners/alipay-mini-page-listener";
import AliPayMiniStorage from "./runtime-alipay-mini/alipay-mini-storage-handler";
import AlipayMiniClickListener from "./runtime-alipay-mini/listeners/alipay-mini-click-listener";
import AlipayMiniUnloadListener from "./runtime-alipay-mini/listeners/alipay-mini-unload-listener";
import AlipayMiniSysAppListener from "./runtime-alipay-mini/listeners/alipay-mini-sys-app-listener";
import AlipayMiniSysPageListener from "./runtime-alipay-mini/listeners/alipay-mini-sys-page-listener";
import AlipayMiniSysComponentListener from "./runtime-alipay-mini/listeners/alipay-mini-component-listener";
import AlipayMiniScrollListener from "./runtime-alipay-mini/listeners/alipay-mini-scroll-listener";
import AlipayMiniLaunchListener from "./runtime-alipay-mini/listeners/alipay-mini-launch-listener";
import AlipayMiniShareListener from "./runtime-alipay-mini/listeners/alipay-mini-share-listener";
import AlipayMiniPageLoadListener from "./runtime-alipay-mini/listeners/alipay-mini-page-load-listener";
import AlipayMiniSourceListener from "./runtime-alipay-mini/listeners/alipay-mini-source-listener";
import DefaultSensors from "./runtime-default/default-sensors";
import AlipayMiniExposureListener from "./runtime-alipay-mini/listeners/alipay-mini-exposure-listener";
import SensorsImpl from "./runtime-core/sensors-impl";
import AlipayMiniPageUnloadListener from "./runtime-alipay-mini/listeners/alipay-mini-page-unload-listener";
import AlipayMiniTouchListener from "./runtime-alipay-mini/listeners/alipay-mini-touch-listener";
import AlipayMiniAppearListener from "./runtime-alipay-mini/listeners/alipay-mini-appear-listener";
import AlipayMiniTabItemListener from "./runtime-alipay-mini/listeners/alipay-mini-tab-item-listener";
import AlipayMiniCaptureScreenListener from "./runtime-alipay-mini/listeners/alipay-mini-capture-screen-listener";

import { judgingEnvironment } from "./utils";
import { libEnum } from "./runtime-core/enum";

const lib = judgingEnvironment();

/**
 * 传感器
 */
let sensors: Sensors = new DefaultSensors();

// 支付宝小程序端
if (lib === libEnum.ALIPAY_MINI) {
  try {
    if (!my.sensors) {
      const storageHandler = new AliPayMiniStorage();
      const dataPoll = new DataPollImpl({ storageHandler });
      const requestHandler = new AliPayMiniRequestHandler();
      const pageLoadListener = new AlipayMiniPageLoadListener();
      const pageUnloadListener = new AlipayMiniPageUnloadListener();
      const sourceListener = new AlipayMiniSourceListener();
      const pageListener = new AlipayMiniPageListener();
      const clickListener = new AlipayMiniClickListener();
      const stayListener = new AlipayMiniStayListener();
      const systemInfoGetter = new AlipayMiniSystemInfoGetter();
      const launchListener = new AlipayMiniLaunchListener();
      const unloadListener = new AlipayMiniUnloadListener();
      const sysAppListener = new AlipayMiniSysAppListener();
      const sysComponentListener = new AlipayMiniSysComponentListener();
      const sysPageListener = new AlipayMiniSysPageListener();
      const scrollListener = new AlipayMiniScrollListener();
      const exposureListener = new AlipayMiniExposureListener();
      const appearListener = new AlipayMiniAppearListener();
      const shareListener = new AlipayMiniShareListener();
      const touchListener = new AlipayMiniTouchListener();
      const tabItemListener = new AlipayMiniTabItemListener();
      const captureScreenListener = new AlipayMiniCaptureScreenListener();
      const para = new AlipayMiniSensorsParaImpl({
        dataPoll,
        requestHandler,
        pageListener,
        clickListener,
        stayListener,
        systemInfoGetter,
        storageHandler,
        launchListener,
        unloadListener,
        sysAppListener,
        sysPageListener,
        sysComponentListener,
        scrollListener,
        shareListener,
        pageLoadListener,
        pageUnloadListener,
        sourceListener,
        exposureListener,
        appearListener,
        touchListener,
        tabItemListener,
        captureScreenListener,
      });
      sensors = new SensorsImpl({
        para,
        dataPoll,
        requestHandler,
        pageListener,
        clickListener,
        stayListener,
        systemInfoGetter,
        launchListener,
        unloadListener,
        shareListener,
        pageLoadListener,
        sourceListener,
        exposureListener,
        touchListener,
        appearListener,
        tabItemListener,
        captureScreenListener,
      });
      my.sensors = sensors;
    }
    sensors = my.sensors;
  } catch (err) {
    console.warn(err);
    my.sensors = sensors;
  }
}

export default sensors as Sensors;
