import SubjectListenerImpl from "@/runtime-core/subject-listener-impl";
import UniappSensorsParaImpl from "../uniapp-sensors-para-impl";
import { isFunction } from "@/utils";

/**
 * tabBar点击监听器 支付宝小程序端
 */
export default class AlipayMiniTabItemListener
  extends SubjectListenerImpl<TabItemObserver>
  implements AlipayMiniSysPageObserver
{
  observers: TabItemObserver[] = [];
  onPage(page: any) {
    this.listenerTabItemTap(page);
  }

  init(data: UniappSensorsParaImpl) {
    data.sysPageListener.register(this);
  }

  private listenerTabItemTap(page: any) {
    const that = this;
    const oldOnTapItemTap = page.onTabItemTap;
    page.onTabItemTap = function (option: {
      pagePath: string;
      [x: string]: any;
    }) {
      isFunction(oldOnTapItemTap) && oldOnTapItemTap.apply(this, arguments);
      //   用户手动点击以及在tabBar页点击才算
      getCurrentPages().length === 1 && that.notify(option.pagePath);
    };
  }
  notify(pagePath: string) {
    this.observers.forEach((observer) => {
        observer.onTabItemClick(pagePath)
    });
  }
}
