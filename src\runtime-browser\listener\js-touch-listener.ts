import SubjectListenerImpl from "@/runtime-core/subject-listener-impl";
import { waterTap } from '@/utils';

/**
 * 浏览器拖动监听器
 */
export default class JsTouchListener extends SubjectListenerImpl<TouchObserver> implements TouchListener, PageObserver {
    listenerEvents: { target: Element, event: any }[] = []; // 监听事件
    init(data: SensorsPara): void {
        data.pageListener.register(this);
    }
    onPageShow() {
        setTimeout(() => {
            this.listenerOnTouch();
        }, 0);
    }
    onPageHide() {
        this.listenerEvents.forEach(({target, event}) => {
            target.removeEventListener('scroll', event); 
        });
    }
    listenerScroll(target: Element) {
        const watchTapNotify = waterTap(this.notify, 1000, this);
        this.listenerEvents.push({ target, event: watchTapNotify });
        target.addEventListener('scroll', e => {
            watchTapNotify(e);
        });
    }
    listenerOnTouch() {
        const targets = document.querySelectorAll('.sensors_touch');
        const that = this;
        for (let i=0; i<targets.length; i++) {
            this.listenerScroll(targets[i]);
        }
    }
    notify(e: any): void {
        this.observers.forEach(observer => observer.onTouch(e));
    }
}