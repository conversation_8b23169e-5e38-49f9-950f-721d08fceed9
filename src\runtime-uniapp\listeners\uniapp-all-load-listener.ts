import SubjectListenerImpl from "@/runtime-core/subject-listener-impl";
import UniappSensorsParaImpl from "../uniapp-sensors-para-impl";

export default class UniappAllLoadListener extends SubjectListenerImpl<AllLoadObserver> implements AllLoadListener, AlipayMiniSysPageObserver {
    init(data: UniappSensorsParaImpl) {
        data.sysPageListener.register(this);
    }
    /**
     * 监听Page onShow方法
     */
    listenerOnLoad(page: any) {
        const oldOnLoad = page.onLoad || function () {};
        const that = this;
        page.onShow = function (options: any) {
            try {
                that.notify(options);
            } catch(err) {
                console.warn('UniappPageListener -> listenerPageOnShow', err);
            }
            oldOnLoad.call(this, ...arguments);
        }
    }
    onPage(definitionPage: object): void {
        this.listenerOnLoad(definitionPage);
    }
    onComponent(component: any) {
        this.listenerOnLoad(component);
    }
    notify(options: any) {
        this.observers.forEach(observer => observer.onAllLoad(options));
    }
}