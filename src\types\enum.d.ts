type LibEnumType =
  | "js"
  | "AlipayMini"
  | "uniapp-app-plus"
  | "uniapp-weixin"
  | "uniapp-alipay"
  | "uniapp-baidu"
  | "uniapp-byte-dance"
  | "uniapp-lark"
  | "uniapp-qq"
  | "uniapp-kuaishou"
  | "uniapp-jd"
  | "uniapp-360"
  | "uniapp-mp"
  | "uniapp-h5"
  | "uniapp-unknown"
  | "uniapp-ding"
  | "uniapp-jd"

type EventEnumType =
  | "$Login"
  | "$PageView"
  | "$PageClick"
  | "$PageStay"
  | "$PageLeave"
  | "$MpLaunc"
  | "$PageShare"
  | "$PageInvite"
  | "$Error"
  | "$PageExposure"
  | "$MpLaunch"
  | "$MpHide"
  | "$Touch"
  | "$Track"
  | "$CaptureScreen";

type TerminalEnumType =
  | (
      | "pc"
      | "live"
      | "wap"
      | "alipay.indi"
      | "alipay.mini"
      | "app.v1"
      | "wx.mini"
      | "codewx.mini"
      | "alipay.code"
      | "alipay.tp.lenovo"
      | "alipay.tp.apple"
      | "alipay.code.trip"
      | "alipay.merchant"
      | "alipay.trip"
      | "alipay.tp.ezule"
      | "pc.back"
      | "pc.merchant"
      | "tt.mini"
      | "Toutiao"
      | "dingtalk.mini"
      | "h5.mini"
      | "app.goofish"
      | "alipay.tp.vivo"
      | "alipay.honor"
      | "alipay.huawei"
      | "alipay.tp.xiaomi"
      | "jd.mini"
    )
  | (string & {});
