import SubjectListenerImpl from "@/runtime-core/subject-listener-impl";
import UniappSensorsParaImpl from "../uniapp-sensors-para-impl";
import { isFunction, isObject, judgingEnvironment } from "@/utils";
import { libEnum } from "@/runtime-core/enum";

const lib = judgingEnvironment();

/**
 * uniapp 曝光监听器
 */
export default class UniappExposureListener extends SubjectListenerImpl<ExposureObserver> implements ExposureListener, UniappSysPageObserver, UniappSysComponentObserver {
    exposureMap = new Map()
    init(data: UniappSensorsParaImpl): void {
        data.pageListener.register(this);
        data.sysPageListener.register(this);
        data.sysComponentListener.register(this);
    }
    onPage(page: any) {
        if([libEnum.UNIAPP_WEIXIN, libEnum.UNIAPP_BYTEDANCE,libEnum.UNIAPP_JD].includes(lib as LibEnumType)) {
            this.listenerSetupMethod(page);
        }
        
    }
    onComponent(component: any) {
        if([libEnum.UNIAPP_WEIXIN, libEnum.UNIAPP_BYTEDANCE,libEnum.UNIAPP_JD].includes(lib as LibEnumType)) {
            this.listenerSetupMethod(component);
        } 
    }

    onPageShow() {
        // app alipay
        if([libEnum.UNIAPP_APP_PLUS, libEnum.UNIAPP_ALIPAY,libEnum.UNIAPP_JD].includes(lib as LibEnumType)) {
            setTimeout(() => {
                this.listenerExposure()
            }, 0);
        }
    }

    listenerSetupMethod(component: any) {
        const oldSetup = component.setup;
        const that = this;
        /**
         * vue3源码中有对setup参数长度判断来创建setup上下文
         * vue3源码部分：const setupContext = instance.setupContext = setup.length > 1 ? createSetupContext(instance) : null;
         */
        if(!isObject(component)) return
        if (isFunction(component.render)) {
            const oldRender = component.render;
            component.render = function (_ctx: any, _cache: any, $props: any, $setup: any, $data: any, $options: any) {
                const instance = _ctx && _ctx.$scope;
                const bindingConfig = oldRender.call(component, _ctx, _cache, $props, $setup, $data, $options);
                that.variableExposure(instance, component)
                return bindingConfig;
            }
        } else if (isFunction(component.setup)) {
            oldSetup && (component.setup = function (_props: any, _defines: any) {
                const oldRender = oldSetup.call(component, _props, _defines);
                return function render(_ctx: any, _cache: any) {
                    const instance = _ctx && _ctx.$scope;
                    const bindingConfig = oldRender.call(component, _ctx, _cache);
                    that.variableExposure(instance, component)
                    return bindingConfig;
                }
            });
        }
    }

    /**
     * @description 劫持 instance 监听曝光
     * @param instance 
     * @param bindingConfig 
     */
    variableExposure(instance: any, component: any) {
        const observer = uni.createIntersectionObserver(instance, { observeAll: true, dataset: true })
        observer.relativeToViewport().observe('.sensors_exposure', (res: any) => {
            this.handleExposureNotify(res, component)
        })
    }

    listenerExposure() {
        const observer = uni.createIntersectionObserver(undefined, { observeAll: true , dataset: true })
        observer.relativeToViewport().observe('.sensors_exposure', (res: any) => {
            this.handleExposureNotify(res)
        })
        
    }

    handleExposureNotify(res: any, component?: any) {
        const that = this
        const isAppear = res.intersectionRatio > 0
        let dataset = res.dataset

        // 钉钉端特殊处理
        // if(lib === libEnum.UNIAPP_DING){
        //     if(!component?.sdkExposurePageDatasetMap) {
        //         return
        //     } else {
        //         const sdkExposurePageDatasetMap = component.sdkExposurePageDatasetMap
        //         const customDataset = sdkExposurePageDatasetMap[res.id] || {}
        //         dataset = {...dataset, ...customDataset}
        //     }
        // }

        const e = {
            ...res,
            type: 'firstAppear',
            currentTarget: {
                dataset,
            }
        }

        if(that.exposureMap.has(res.id)) {
            const exposureItem = that.exposureMap.get(res.id)
            if(!exposureItem.isFirstAppear) {
                exposureItem.isFirstAppear = true
                that.notify(e);
            } 
            that.exposureMap.set(res.id, exposureItem)
        } else {
            // 初始化
            if(isAppear) {
                that.exposureMap.set(res.id, {
                    isAppear,
                    isFirstAppear: true
                })
                that.notify(e);
            }
        }
    }

    notify(e: any): void {
        this.observers.forEach(observer => observer.onExposure(e));
    }
}