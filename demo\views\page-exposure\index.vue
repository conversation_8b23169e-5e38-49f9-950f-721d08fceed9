<template>
    <div class="sensors_mutations">
        <div v-if="isShowBox">
            <div class="sensors_exposure" data-sensorsExposure="$ExposureTest" data-sensors_id="1">曝光元素</div>
        </div>
    </div>
    <div class="sensors_exposure" data-sensors_id="2">曝光元素2</div>
    <div style="height: 100vh"></div>
    <div class="sensors_exposure" data-sensors_id="3">曝光元素3</div>
</template>

<script setup lang="ts">
import { ref } from 'vue';

const isShowBox = ref<boolean>(false);
setTimeout(() => {
    isShowBox.value = !isShowBox.value;
}, 2000);
</script>