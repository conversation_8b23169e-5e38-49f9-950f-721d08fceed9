<template>
    <div class="page-stay">
        <div>
            $pagestay 页面停留事件测试
            <router-link :to="{name: 'Routing'}">返回路由页</router-link>
        </div>
        <div class="sensors-scroll">
            <div class="content"></div>
        </div>
    </div>
</template>

<script lang="ts"></script>

<style lang="scss">
    .page-stay {
        display: flex;
        justify-content: center;
        flex-direction: column;
        padding-top: 10%;
    }
    .sensors-scroll {
        background-color: #f3f3f3;
        height: 200px;
        width: 100%;
        overflow-y: auto;
        .content {
            width: 200px;
            height: 1000px;
            background-color: #AAA;
        }
    }
</style>