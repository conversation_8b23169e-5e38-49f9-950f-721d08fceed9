import SubjectListenerImpl from "@/runtime-core/subject-listener-impl";
import AlipayMiniSensorsParaImpl from "../alipay-mini-sensors-para-impl";

/**
 * 页面onLoad监听器 支付宝小程序
 */
export default class AlipayMiniPageLoadListener extends SubjectListenerImpl<PageLoadObserver> {
    init(data: AlipayMiniSensorsParaImpl) {
        data.sysPageListener.register(this);
    }
    onPage(page: any) {
        this.listenerOnLoad(page);
    }
    /**
     * 监听页面onLoad事件
     */
    listenerOnLoad(page: any) {
        const oldOnLoad = page.onLoad || function () {};
        const that = this;
        page.onLoad = function (options: any) {
            try {
                that.notify(options);
            } catch(err) {
                console.warn('AlipayMiniPageLoadListener -> listenerPageOnLoad', err);
            }
            oldOnLoad.call(this, ...arguments);
        }
    }
    notify(options: any) {
        this.observers.forEach(observer => observer.onBeforePageLoad && observer.onBeforePageLoad(options));
        this.observers.forEach(observer => observer.onPageLoad && observer.onPageLoad(options));
    }
}