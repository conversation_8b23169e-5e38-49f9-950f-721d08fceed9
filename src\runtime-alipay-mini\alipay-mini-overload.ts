import AlipayMiniSysPageListener from "./listeners/alipay-mini-sys-page-listener";
import AlipayMiniSysAppListener from "./listeners/alipay-mini-sys-app-listener";
import AlipayMiniSysComponentListener from "./listeners/alipay-mini-component-listener";

/**
 * 支付宝小程序 系统方法重载
 */
class AlipayMiniOverload {
    private oldPage: any; // 缓存系统Page函数
    private oldApp: any; // 缓存系统App函数
    private oldComponent: any; // 缓存系统Component函数
    private isAlreadyInit: boolean = false; // 是否已经初始化
    init(sysPageListener: AlipayMiniSysPageListener, sysAppListener: AlipayMiniSysAppListener, sysComponentListener: AlipayMiniSysComponentListener) {
        if (this.isAlreadyInit) return;

        const that = this;
        this.oldPage = Page;
        this.oldApp = App;
        this.oldComponent = Component;

        /**
         * 重载系统页面方法
         */
        Page = function (definitionPage: any) {
            sysPageListener.notify(definitionPage);
            return that.oldPage(definitionPage);
        }

        /**
         * 重载系统app方法
         */
        App = function (definitionApp: any) {
            sysAppListener.notify(definitionApp);
            return that.oldApp(definitionApp);
        }

        /**
         * 重载系统Component方法
         */
        Component = function (definitionComponent: any) {
            sysComponentListener.notify(definitionComponent);
            return that.oldComponent(definitionComponent);
        }

        this.isAlreadyInit = true;
    }
}

const overload = new AlipayMiniOverload();

export default overload;