/**
 * 阿里小程序格式化插件
 */
import fse from "fs-extra";
import path from "path";
import { fileURLToPath } from "url";

const config = fileURLToPath(import.meta.url);
const packageFile = path.resolve(config, "../..", "package.json");
const packageJson = JSON.parse(fse.readFileSync(packageFile, "utf8"));

export default class AlipayMiniFormatPlugin {
    // 在构造函数中获取用户给该插件传入的配置
    constructor(options){
    }
  
    // Webpack 会调用 BasicPlugin 实例的 apply 方法给插件实例传入 compiler 对象
    apply(compiler) {
        compiler.hooks.emit.tap("emit", compilation => {
            // 循环格式化所有文件
            for (const filename in compilation.assets) {
                if (/^data(\.|-)analysis(\.|-)sdk.*\.js$/.test(filename)) {
                    const content = compilation.assets[filename].source();
                    let withoutComments = content.toString();
                    withoutComments = `// sdk_version:${packageJson.version}\n/* eslint-disable */` + withoutComments;
                    compilation.assets[filename] = {
                      source: () => withoutComments,
                      size: withoutComments.length
                    }
                }
            }
        });
    }
}
