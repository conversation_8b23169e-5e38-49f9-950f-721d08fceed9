// sdk_version:1.3.17
/* eslint-disable *//*! For license information please see data-analysis-sdk-alipay.js.LICENSE.txt */
var e={820:function(e,t,r){var n,o,i,a;i=this,a=function(){void 0===Array.isArray&&(Array.isArray=function(e){return"[object Array]"===Object.prototype.toString.call(e)});var e=function(e,t){e=[e[0]>>>16,65535&e[0],e[1]>>>16,65535&e[1]],t=[t[0]>>>16,65535&t[0],t[1]>>>16,65535&t[1]];var r=[0,0,0,0];return r[3]+=e[3]+t[3],r[2]+=r[3]>>>16,r[3]&=65535,r[2]+=e[2]+t[2],r[1]+=r[2]>>>16,r[2]&=65535,r[1]+=e[1]+t[1],r[0]+=r[1]>>>16,r[1]&=65535,r[0]+=e[0]+t[0],r[0]&=65535,[r[0]<<16|r[1],r[2]<<16|r[3]]},t=function(e,t){e=[e[0]>>>16,65535&e[0],e[1]>>>16,65535&e[1]],t=[t[0]>>>16,65535&t[0],t[1]>>>16,65535&t[1]];var r=[0,0,0,0];return r[3]+=e[3]*t[3],r[2]+=r[3]>>>16,r[3]&=65535,r[2]+=e[2]*t[3],r[1]+=r[2]>>>16,r[2]&=65535,r[2]+=e[3]*t[2],r[1]+=r[2]>>>16,r[2]&=65535,r[1]+=e[1]*t[3],r[0]+=r[1]>>>16,r[1]&=65535,r[1]+=e[2]*t[2],r[0]+=r[1]>>>16,r[1]&=65535,r[1]+=e[3]*t[1],r[0]+=r[1]>>>16,r[1]&=65535,r[0]+=e[0]*t[3]+e[1]*t[2]+e[2]*t[1]+e[3]*t[0],r[0]&=65535,[r[0]<<16|r[1],r[2]<<16|r[3]]},r=function(e,t){return 32==(t%=64)?[e[1],e[0]]:t<32?[e[0]<<t|e[1]>>>32-t,e[1]<<t|e[0]>>>32-t]:(t-=32,[e[1]<<t|e[0]>>>32-t,e[0]<<t|e[1]>>>32-t])},n=function(e,t){return 0==(t%=64)?e:t<32?[e[0]<<t|e[1]>>>32-t,e[1]<<t]:[e[1]<<t-32,0]},o=function(e,t){return[e[0]^t[0],e[1]^t[1]]},i=function(e){return e=o(e,[0,e[0]>>>1]),e=t(e,[4283543511,3981806797]),e=o(e,[0,e[0]>>>1]),e=t(e,[3301882366,444984403]),o(e,[0,e[0]>>>1])},a=function(a,s){s=s||0;for(var u=(a=a||"").length%16,c=a.length-u,l=[0,s],f=[0,s],p=[0,0],h=[0,0],y=[2277735313,289559509],d=[1291169091,658871167],v=0;v<c;v+=16)p=[255&a.charCodeAt(v+4)|(255&a.charCodeAt(v+5))<<8|(255&a.charCodeAt(v+6))<<16|(255&a.charCodeAt(v+7))<<24,255&a.charCodeAt(v)|(255&a.charCodeAt(v+1))<<8|(255&a.charCodeAt(v+2))<<16|(255&a.charCodeAt(v+3))<<24],h=[255&a.charCodeAt(v+12)|(255&a.charCodeAt(v+13))<<8|(255&a.charCodeAt(v+14))<<16|(255&a.charCodeAt(v+15))<<24,255&a.charCodeAt(v+8)|(255&a.charCodeAt(v+9))<<8|(255&a.charCodeAt(v+10))<<16|(255&a.charCodeAt(v+11))<<24],p=t(p,y),p=r(p,31),p=t(p,d),l=o(l,p),l=r(l,27),l=e(l,f),l=e(t(l,[0,5]),[0,1390208809]),h=t(h,d),h=r(h,33),h=t(h,y),f=o(f,h),f=r(f,31),f=e(f,l),f=e(t(f,[0,5]),[0,944331445]);switch(p=[0,0],h=[0,0],u){case 15:h=o(h,n([0,a.charCodeAt(v+14)],48));case 14:h=o(h,n([0,a.charCodeAt(v+13)],40));case 13:h=o(h,n([0,a.charCodeAt(v+12)],32));case 12:h=o(h,n([0,a.charCodeAt(v+11)],24));case 11:h=o(h,n([0,a.charCodeAt(v+10)],16));case 10:h=o(h,n([0,a.charCodeAt(v+9)],8));case 9:h=o(h,[0,a.charCodeAt(v+8)]),h=t(h,d),h=r(h,33),h=t(h,y),f=o(f,h);case 8:p=o(p,n([0,a.charCodeAt(v+7)],56));case 7:p=o(p,n([0,a.charCodeAt(v+6)],48));case 6:p=o(p,n([0,a.charCodeAt(v+5)],40));case 5:p=o(p,n([0,a.charCodeAt(v+4)],32));case 4:p=o(p,n([0,a.charCodeAt(v+3)],24));case 3:p=o(p,n([0,a.charCodeAt(v+2)],16));case 2:p=o(p,n([0,a.charCodeAt(v+1)],8));case 1:p=o(p,[0,a.charCodeAt(v)]),p=t(p,y),p=r(p,31),p=t(p,d),l=o(l,p)}return l=o(l,[0,a.length]),f=o(f,[0,a.length]),l=e(l,f),f=e(f,l),l=i(l),f=i(f),l=e(l,f),f=e(f,l),("00000000"+(l[0]>>>0).toString(16)).slice(-8)+("00000000"+(l[1]>>>0).toString(16)).slice(-8)+("00000000"+(f[0]>>>0).toString(16)).slice(-8)+("00000000"+(f[1]>>>0).toString(16)).slice(-8)},s={preprocessor:null,audio:{timeout:1e3,excludeIOS11:!0},fonts:{swfContainerId:"fingerprintjs2",swfPath:"flash/compiled/FontList.swf",userDefinedFonts:[],extendedJsFonts:!1},screen:{detectScreenOrientation:!0},plugins:{sortPluginsFor:[/palemoon/i],excludeIE:!1},extraComponents:[],excludes:{enumerateDevices:!0,pixelRatio:!0,doNotTrack:!0,fontsFlash:!0,adBlock:!0},NOT_AVAILABLE:"not available",ERROR:"error",EXCLUDED:"excluded"},u=function(e,t){if(Array.prototype.forEach&&e.forEach===Array.prototype.forEach)e.forEach(t);else if(e.length===+e.length)for(var r=0,n=e.length;r<n;r++)t(e[r],r,e);else for(var o in e)e.hasOwnProperty(o)&&t(e[o],o,e)},c=function(e,t){var r=[];return null==e?r:Array.prototype.map&&e.map===Array.prototype.map?e.map(t):(u(e,(function(e,n,o){r.push(t(e,n,o))})),r)},l=function(e){if(null==navigator.plugins)return e.NOT_AVAILABLE;for(var t=[],r=0,n=navigator.plugins.length;r<n;r++)navigator.plugins[r]&&t.push(navigator.plugins[r]);return f(e)&&(t=t.sort((function(e,t){return e.name>t.name?1:e.name<t.name?-1:0}))),c(t,(function(e){var t=c(e,(function(e){return[e.type,e.suffixes]}));return[e.name,e.description,t]}))},f=function(e){for(var t=!1,r=0,n=e.plugins.sortPluginsFor.length;r<n;r++){var o=e.plugins.sortPluginsFor[r];if(navigator.userAgent.match(o)){t=!0;break}}return t},p=function(){var e=document.createElement("canvas");return!(!e.getContext||!e.getContext("2d"))},h=function(){if(!p())return!1;var e=d(),t=!!window.WebGLRenderingContext&&!!e;return v(e),t},y=function(){return("msWriteProfilerMark"in window)+("msLaunchUri"in navigator)+("msSaveBlob"in navigator)>=2},d=function(){var e=document.createElement("canvas"),t=null;try{t=e.getContext("webgl")||e.getContext("experimental-webgl")}catch(e){}return t||(t=null),t},v=function(e){var t=e.getExtension("WEBGL_lose_context");null!=t&&t.loseContext()},g=[{key:"userAgent",getData:function(e){e(navigator.userAgent)}},{key:"webdriver",getData:function(e,t){e(null==navigator.webdriver?t.NOT_AVAILABLE:navigator.webdriver)}},{key:"language",getData:function(e,t){e(navigator.language||navigator.userLanguage||navigator.browserLanguage||navigator.systemLanguage||t.NOT_AVAILABLE)}},{key:"colorDepth",getData:function(e,t){e(window.screen.colorDepth||t.NOT_AVAILABLE)}},{key:"deviceMemory",getData:function(e,t){e(navigator.deviceMemory||t.NOT_AVAILABLE)}},{key:"pixelRatio",getData:function(e,t){e(window.devicePixelRatio||t.NOT_AVAILABLE)}},{key:"hardwareConcurrency",getData:function(e,t){e(function(e){return navigator.hardwareConcurrency?navigator.hardwareConcurrency:e.NOT_AVAILABLE}(t))}},{key:"screenResolution",getData:function(e,t){e(function(e){var t=[window.screen.width,window.screen.height];return e.screen.detectScreenOrientation&&t.sort().reverse(),t}(t))}},{key:"availableScreenResolution",getData:function(e,t){e(function(e){if(window.screen.availWidth&&window.screen.availHeight){var t=[window.screen.availHeight,window.screen.availWidth];return e.screen.detectScreenOrientation&&t.sort().reverse(),t}return e.NOT_AVAILABLE}(t))}},{key:"timezoneOffset",getData:function(e){e((new Date).getTimezoneOffset())}},{key:"timezone",getData:function(e,t){window.Intl&&window.Intl.DateTimeFormat?e((new window.Intl.DateTimeFormat).resolvedOptions().timeZone||t.NOT_AVAILABLE):e(t.NOT_AVAILABLE)}},{key:"sessionStorage",getData:function(e,t){e(function(e){try{return!!window.sessionStorage}catch(t){return e.ERROR}}(t))}},{key:"localStorage",getData:function(e,t){e(function(e){try{return!!window.localStorage}catch(t){return e.ERROR}}(t))}},{key:"indexedDb",getData:function(e,t){e(function(e){if(y())return e.EXCLUDED;try{return!!window.indexedDB}catch(t){return e.ERROR}}(t))}},{key:"addBehavior",getData:function(e){e(!!window.HTMLElement.prototype.addBehavior)}},{key:"openDatabase",getData:function(e){e(!!window.openDatabase)}},{key:"cpuClass",getData:function(e,t){e(function(e){return navigator.cpuClass||e.NOT_AVAILABLE}(t))}},{key:"platform",getData:function(e,t){e(function(e){return navigator.platform?navigator.platform:e.NOT_AVAILABLE}(t))}},{key:"doNotTrack",getData:function(e,t){e(function(e){return navigator.doNotTrack?navigator.doNotTrack:navigator.msDoNotTrack?navigator.msDoNotTrack:window.doNotTrack?window.doNotTrack:e.NOT_AVAILABLE}(t))}},{key:"plugins",getData:function(e,t){"Microsoft Internet Explorer"===navigator.appName||"Netscape"===navigator.appName&&/Trident/.test(navigator.userAgent)?t.plugins.excludeIE?e(t.EXCLUDED):e(function(e){var t=[];return Object.getOwnPropertyDescriptor&&Object.getOwnPropertyDescriptor(window,"ActiveXObject")||"ActiveXObject"in window?t=c(["AcroPDF.PDF","Adodb.Stream","AgControl.AgControl","DevalVRXCtrl.DevalVRXCtrl.1","MacromediaFlashPaper.MacromediaFlashPaper","Msxml2.DOMDocument","Msxml2.XMLHTTP","PDF.PdfCtrl","QuickTime.QuickTime","QuickTimeCheckObject.QuickTimeCheck.1","RealPlayer","RealPlayer.RealPlayer(tm) ActiveX Control (32-bit)","RealVideo.RealVideo(tm) ActiveX Control (32-bit)","Scripting.Dictionary","SWCtl.SWCtl","Shell.UIHelper","ShockwaveFlash.ShockwaveFlash","Skype.Detection","TDCCtl.TDCCtl","WMPlayer.OCX","rmocx.RealPlayer G2 Control","rmocx.RealPlayer G2 Control.1"],(function(t){try{return new window.ActiveXObject(t),t}catch(t){return e.ERROR}})):t.push(e.NOT_AVAILABLE),navigator.plugins&&(t=t.concat(l(e))),t}(t)):e(l(t))}},{key:"canvas",getData:function(e,t){p()?e(function(e){var t=[],r=document.createElement("canvas");r.width=2e3,r.height=200,r.style.display="inline";var n=r.getContext("2d");return n.rect(0,0,10,10),n.rect(2,2,6,6),t.push("canvas winding:"+(!1===n.isPointInPath(5,5,"evenodd")?"yes":"no")),n.textBaseline="alphabetic",n.fillStyle="#f60",n.fillRect(125,1,62,20),n.fillStyle="#069",e.dontUseFakeFontInCanvas?n.font="11pt Arial":n.font="11pt no-real-font-123",n.fillText("Cwm fjordbank glyphs vext quiz, 😃",2,15),n.fillStyle="rgba(102, 204, 0, 0.2)",n.font="18pt Arial",n.fillText("Cwm fjordbank glyphs vext quiz, 😃",4,45),n.globalCompositeOperation="multiply",n.fillStyle="rgb(255,0,255)",n.beginPath(),n.arc(50,50,50,0,2*Math.PI,!0),n.closePath(),n.fill(),n.fillStyle="rgb(0,255,255)",n.beginPath(),n.arc(100,50,50,0,2*Math.PI,!0),n.closePath(),n.fill(),n.fillStyle="rgb(255,255,0)",n.beginPath(),n.arc(75,100,50,0,2*Math.PI,!0),n.closePath(),n.fill(),n.fillStyle="rgb(255,0,255)",n.arc(75,75,75,0,2*Math.PI,!0),n.arc(75,75,25,0,2*Math.PI,!0),n.fill("evenodd"),r.toDataURL&&t.push("canvas fp:"+r.toDataURL()),t}(t)):e(t.NOT_AVAILABLE)}},{key:"webgl",getData:function(e,t){h()?e(function(){var e,t=function(t){return e.clearColor(0,0,0,1),e.enable(e.DEPTH_TEST),e.depthFunc(e.LEQUAL),e.clear(e.COLOR_BUFFER_BIT|e.DEPTH_BUFFER_BIT),"["+t[0]+", "+t[1]+"]"};if(!(e=d()))return null;var r=[],n=e.createBuffer();e.bindBuffer(e.ARRAY_BUFFER,n);var o=new Float32Array([-.2,-.9,0,.4,-.26,0,0,.*********,0]);e.bufferData(e.ARRAY_BUFFER,o,e.STATIC_DRAW),n.itemSize=3,n.numItems=3;var i=e.createProgram(),a=e.createShader(e.VERTEX_SHADER);e.shaderSource(a,"attribute vec2 attrVertex;varying vec2 varyinTexCoordinate;uniform vec2 uniformOffset;void main(){varyinTexCoordinate=attrVertex+uniformOffset;gl_Position=vec4(attrVertex,0,1);}"),e.compileShader(a);var s=e.createShader(e.FRAGMENT_SHADER);e.shaderSource(s,"precision mediump float;varying vec2 varyinTexCoordinate;void main() {gl_FragColor=vec4(varyinTexCoordinate,0,1);}"),e.compileShader(s),e.attachShader(i,a),e.attachShader(i,s),e.linkProgram(i),e.useProgram(i),i.vertexPosAttrib=e.getAttribLocation(i,"attrVertex"),i.offsetUniform=e.getUniformLocation(i,"uniformOffset"),e.enableVertexAttribArray(i.vertexPosArray),e.vertexAttribPointer(i.vertexPosAttrib,n.itemSize,e.FLOAT,!1,0,0),e.uniform2f(i.offsetUniform,1,1),e.drawArrays(e.TRIANGLE_STRIP,0,n.numItems);try{r.push(e.canvas.toDataURL())}catch(e){}r.push("extensions:"+(e.getSupportedExtensions()||[]).join(";")),r.push("webgl aliased line width range:"+t(e.getParameter(e.ALIASED_LINE_WIDTH_RANGE))),r.push("webgl aliased point size range:"+t(e.getParameter(e.ALIASED_POINT_SIZE_RANGE))),r.push("webgl alpha bits:"+e.getParameter(e.ALPHA_BITS)),r.push("webgl antialiasing:"+(e.getContextAttributes().antialias?"yes":"no")),r.push("webgl blue bits:"+e.getParameter(e.BLUE_BITS)),r.push("webgl depth bits:"+e.getParameter(e.DEPTH_BITS)),r.push("webgl green bits:"+e.getParameter(e.GREEN_BITS)),r.push("webgl max anisotropy:"+function(e){var t=e.getExtension("EXT_texture_filter_anisotropic")||e.getExtension("WEBKIT_EXT_texture_filter_anisotropic")||e.getExtension("MOZ_EXT_texture_filter_anisotropic");if(t){var r=e.getParameter(t.MAX_TEXTURE_MAX_ANISOTROPY_EXT);return 0===r&&(r=2),r}return null}(e)),r.push("webgl max combined texture image units:"+e.getParameter(e.MAX_COMBINED_TEXTURE_IMAGE_UNITS)),r.push("webgl max cube map texture size:"+e.getParameter(e.MAX_CUBE_MAP_TEXTURE_SIZE)),r.push("webgl max fragment uniform vectors:"+e.getParameter(e.MAX_FRAGMENT_UNIFORM_VECTORS)),r.push("webgl max render buffer size:"+e.getParameter(e.MAX_RENDERBUFFER_SIZE)),r.push("webgl max texture image units:"+e.getParameter(e.MAX_TEXTURE_IMAGE_UNITS)),r.push("webgl max texture size:"+e.getParameter(e.MAX_TEXTURE_SIZE)),r.push("webgl max varying vectors:"+e.getParameter(e.MAX_VARYING_VECTORS)),r.push("webgl max vertex attribs:"+e.getParameter(e.MAX_VERTEX_ATTRIBS)),r.push("webgl max vertex texture image units:"+e.getParameter(e.MAX_VERTEX_TEXTURE_IMAGE_UNITS)),r.push("webgl max vertex uniform vectors:"+e.getParameter(e.MAX_VERTEX_UNIFORM_VECTORS)),r.push("webgl max viewport dims:"+t(e.getParameter(e.MAX_VIEWPORT_DIMS))),r.push("webgl red bits:"+e.getParameter(e.RED_BITS)),r.push("webgl renderer:"+e.getParameter(e.RENDERER)),r.push("webgl shading language version:"+e.getParameter(e.SHADING_LANGUAGE_VERSION)),r.push("webgl stencil bits:"+e.getParameter(e.STENCIL_BITS)),r.push("webgl vendor:"+e.getParameter(e.VENDOR)),r.push("webgl version:"+e.getParameter(e.VERSION));try{var c=e.getExtension("WEBGL_debug_renderer_info");c&&(r.push("webgl unmasked vendor:"+e.getParameter(c.UNMASKED_VENDOR_WEBGL)),r.push("webgl unmasked renderer:"+e.getParameter(c.UNMASKED_RENDERER_WEBGL)))}catch(e){}return e.getShaderPrecisionFormat?(u(["FLOAT","INT"],(function(t){u(["VERTEX","FRAGMENT"],(function(n){u(["HIGH","MEDIUM","LOW"],(function(o){u(["precision","rangeMin","rangeMax"],(function(i){var a=e.getShaderPrecisionFormat(e[n+"_SHADER"],e[o+"_"+t])[i];"precision"!==i&&(i="precision "+i);var s=["webgl ",n.toLowerCase()," shader ",o.toLowerCase()," ",t.toLowerCase()," ",i,":",a].join("");r.push(s)}))}))}))})),v(e),r):(v(e),r)}()):e(t.NOT_AVAILABLE)}},{key:"webglVendorAndRenderer",getData:function(e){h()?e(function(){try{var e=d(),t=e.getExtension("WEBGL_debug_renderer_info"),r=e.getParameter(t.UNMASKED_VENDOR_WEBGL)+"~"+e.getParameter(t.UNMASKED_RENDERER_WEBGL);return v(e),r}catch(e){return null}}()):e()}},{key:"adBlock",getData:function(e){e(function(){var e=document.createElement("div");e.innerHTML="&nbsp;",e.className="adsbox";var t=!1;try{document.body.appendChild(e),t=0===document.getElementsByClassName("adsbox")[0].offsetHeight,document.body.removeChild(e)}catch(e){t=!1}return t}())}},{key:"hasLiedLanguages",getData:function(e){e(function(){if(void 0!==navigator.languages)try{if(navigator.languages[0].substr(0,2)!==navigator.language.substr(0,2))return!0}catch(e){return!0}return!1}())}},{key:"hasLiedResolution",getData:function(e){e(window.screen.width<window.screen.availWidth||window.screen.height<window.screen.availHeight)}},{key:"hasLiedOs",getData:function(e){e(function(){var e,t=navigator.userAgent.toLowerCase(),r=navigator.oscpu,n=navigator.platform.toLowerCase();if(e=t.indexOf("windows phone")>=0?"Windows Phone":t.indexOf("windows")>=0||t.indexOf("win16")>=0||t.indexOf("win32")>=0||t.indexOf("win64")>=0||t.indexOf("win95")>=0||t.indexOf("win98")>=0||t.indexOf("winnt")>=0||t.indexOf("wow64")>=0?"Windows":t.indexOf("android")>=0?"Android":t.indexOf("linux")>=0||t.indexOf("cros")>=0||t.indexOf("x11")>=0?"Linux":t.indexOf("iphone")>=0||t.indexOf("ipad")>=0||t.indexOf("ipod")>=0||t.indexOf("crios")>=0||t.indexOf("fxios")>=0?"iOS":t.indexOf("macintosh")>=0||t.indexOf("mac_powerpc)")>=0?"Mac":"Other",("ontouchstart"in window||navigator.maxTouchPoints>0||navigator.msMaxTouchPoints>0)&&"Windows"!==e&&"Windows Phone"!==e&&"Android"!==e&&"iOS"!==e&&"Other"!==e&&-1===t.indexOf("cros"))return!0;if(void 0!==r){if((r=r.toLowerCase()).indexOf("win")>=0&&"Windows"!==e&&"Windows Phone"!==e)return!0;if(r.indexOf("linux")>=0&&"Linux"!==e&&"Android"!==e)return!0;if(r.indexOf("mac")>=0&&"Mac"!==e&&"iOS"!==e)return!0;if((-1===r.indexOf("win")&&-1===r.indexOf("linux")&&-1===r.indexOf("mac"))!=("Other"===e))return!0}return n.indexOf("win")>=0&&"Windows"!==e&&"Windows Phone"!==e||(n.indexOf("linux")>=0||n.indexOf("android")>=0||n.indexOf("pike")>=0)&&"Linux"!==e&&"Android"!==e||(n.indexOf("mac")>=0||n.indexOf("ipad")>=0||n.indexOf("ipod")>=0||n.indexOf("iphone")>=0)&&"Mac"!==e&&"iOS"!==e||!(n.indexOf("arm")>=0&&"Windows Phone"===e)&&!(n.indexOf("pike")>=0&&t.indexOf("opera mini")>=0)&&((n.indexOf("win")<0&&n.indexOf("linux")<0&&n.indexOf("mac")<0&&n.indexOf("iphone")<0&&n.indexOf("ipad")<0&&n.indexOf("ipod")<0)!=("Other"===e)||void 0===navigator.plugins&&"Windows"!==e&&"Windows Phone"!==e)}())}},{key:"hasLiedBrowser",getData:function(e){e(function(){var e,t=navigator.userAgent.toLowerCase(),r=navigator.productSub;if(t.indexOf("edge/")>=0||t.indexOf("iemobile/")>=0)return!1;if(t.indexOf("opera mini")>=0)return!1;if(("Chrome"==(e=t.indexOf("firefox/")>=0?"Firefox":t.indexOf("opera/")>=0||t.indexOf(" opr/")>=0?"Opera":t.indexOf("chrome/")>=0?"Chrome":t.indexOf("safari/")>=0?t.indexOf("android 1.")>=0||t.indexOf("android 2.")>=0||t.indexOf("android 3.")>=0||t.indexOf("android 4.")>=0?"AOSP":"Safari":t.indexOf("trident/")>=0?"Internet Explorer":"Other")||"Safari"===e||"Opera"===e)&&"20030107"!==r)return!0;var n,o=eval.toString().length;if(37===o&&"Safari"!==e&&"Firefox"!==e&&"Other"!==e)return!0;if(39===o&&"Internet Explorer"!==e&&"Other"!==e)return!0;if(33===o&&"Chrome"!==e&&"AOSP"!==e&&"Opera"!==e&&"Other"!==e)return!0;try{throw"a"}catch(e){try{e.toSource(),n=!0}catch(e){n=!1}}return n&&"Firefox"!==e&&"Other"!==e}())}},{key:"touchSupport",getData:function(e){e(function(){var e,t=0;void 0!==navigator.maxTouchPoints?t=navigator.maxTouchPoints:void 0!==navigator.msMaxTouchPoints&&(t=navigator.msMaxTouchPoints);try{document.createEvent("TouchEvent"),e=!0}catch(t){e=!1}return[t,e,"ontouchstart"in window]}())}},{key:"fonts",getData:function(e,t){var r=["monospace","sans-serif","serif"],n=["Andale Mono","Arial","Arial Black","Arial Hebrew","Arial MT","Arial Narrow","Arial Rounded MT Bold","Arial Unicode MS","Bitstream Vera Sans Mono","Book Antiqua","Bookman Old Style","Calibri","Cambria","Cambria Math","Century","Century Gothic","Century Schoolbook","Comic Sans","Comic Sans MS","Consolas","Courier","Courier New","Geneva","Georgia","Helvetica","Helvetica Neue","Impact","Lucida Bright","Lucida Calligraphy","Lucida Console","Lucida Fax","LUCIDA GRANDE","Lucida Handwriting","Lucida Sans","Lucida Sans Typewriter","Lucida Sans Unicode","Microsoft Sans Serif","Monaco","Monotype Corsiva","MS Gothic","MS Outlook","MS PGothic","MS Reference Sans Serif","MS Sans Serif","MS Serif","MYRIAD","MYRIAD PRO","Palatino","Palatino Linotype","Segoe Print","Segoe Script","Segoe UI","Segoe UI Light","Segoe UI Semibold","Segoe UI Symbol","Tahoma","Times","Times New Roman","Times New Roman PS","Trebuchet MS","Verdana","Wingdings","Wingdings 2","Wingdings 3"];t.fonts.extendedJsFonts&&(n=n.concat(["Abadi MT Condensed Light","Academy Engraved LET","ADOBE CASLON PRO","Adobe Garamond","ADOBE GARAMOND PRO","Agency FB","Aharoni","Albertus Extra Bold","Albertus Medium","Algerian","Amazone BT","American Typewriter","American Typewriter Condensed","AmerType Md BT","Andalus","Angsana New","AngsanaUPC","Antique Olive","Aparajita","Apple Chancery","Apple Color Emoji","Apple SD Gothic Neo","Arabic Typesetting","ARCHER","ARNO PRO","Arrus BT","Aurora Cn BT","AvantGarde Bk BT","AvantGarde Md BT","AVENIR","Ayuthaya","Bandy","Bangla Sangam MN","Bank Gothic","BankGothic Md BT","Baskerville","Baskerville Old Face","Batang","BatangChe","Bauer Bodoni","Bauhaus 93","Bazooka","Bell MT","Bembo","Benguiat Bk BT","Berlin Sans FB","Berlin Sans FB Demi","Bernard MT Condensed","BernhardFashion BT","BernhardMod BT","Big Caslon","BinnerD","Blackadder ITC","BlairMdITC TT","Bodoni 72","Bodoni 72 Oldstyle","Bodoni 72 Smallcaps","Bodoni MT","Bodoni MT Black","Bodoni MT Condensed","Bodoni MT Poster Compressed","Bookshelf Symbol 7","Boulder","Bradley Hand","Bradley Hand ITC","Bremen Bd BT","Britannic Bold","Broadway","Browallia New","BrowalliaUPC","Brush Script MT","Californian FB","Calisto MT","Calligrapher","Candara","CaslonOpnface BT","Castellar","Centaur","Cezanne","CG Omega","CG Times","Chalkboard","Chalkboard SE","Chalkduster","Charlesworth","Charter Bd BT","Charter BT","Chaucer","ChelthmITC Bk BT","Chiller","Clarendon","Clarendon Condensed","CloisterBlack BT","Cochin","Colonna MT","Constantia","Cooper Black","Copperplate","Copperplate Gothic","Copperplate Gothic Bold","Copperplate Gothic Light","CopperplGoth Bd BT","Corbel","Cordia New","CordiaUPC","Cornerstone","Coronet","Cuckoo","Curlz MT","DaunPenh","Dauphin","David","DB LCD Temp","DELICIOUS","Denmark","DFKai-SB","Didot","DilleniaUPC","DIN","DokChampa","Dotum","DotumChe","Ebrima","Edwardian Script ITC","Elephant","English 111 Vivace BT","Engravers MT","EngraversGothic BT","Eras Bold ITC","Eras Demi ITC","Eras Light ITC","Eras Medium ITC","EucrosiaUPC","Euphemia","Euphemia UCAS","EUROSTILE","Exotc350 Bd BT","FangSong","Felix Titling","Fixedsys","FONTIN","Footlight MT Light","Forte","FrankRuehl","Fransiscan","Freefrm721 Blk BT","FreesiaUPC","Freestyle Script","French Script MT","FrnkGothITC Bk BT","Fruitger","FRUTIGER","Futura","Futura Bk BT","Futura Lt BT","Futura Md BT","Futura ZBlk BT","FuturaBlack BT","Gabriola","Galliard BT","Gautami","Geeza Pro","Geometr231 BT","Geometr231 Hv BT","Geometr231 Lt BT","GeoSlab 703 Lt BT","GeoSlab 703 XBd BT","Gigi","Gill Sans","Gill Sans MT","Gill Sans MT Condensed","Gill Sans MT Ext Condensed Bold","Gill Sans Ultra Bold","Gill Sans Ultra Bold Condensed","Gisha","Gloucester MT Extra Condensed","GOTHAM","GOTHAM BOLD","Goudy Old Style","Goudy Stout","GoudyHandtooled BT","GoudyOLSt BT","Gujarati Sangam MN","Gulim","GulimChe","Gungsuh","GungsuhChe","Gurmukhi MN","Haettenschweiler","Harlow Solid Italic","Harrington","Heather","Heiti SC","Heiti TC","HELV","Herald","High Tower Text","Hiragino Kaku Gothic ProN","Hiragino Mincho ProN","Hoefler Text","Humanst 521 Cn BT","Humanst521 BT","Humanst521 Lt BT","Imprint MT Shadow","Incised901 Bd BT","Incised901 BT","Incised901 Lt BT","INCONSOLATA","Informal Roman","Informal011 BT","INTERSTATE","IrisUPC","Iskoola Pota","JasmineUPC","Jazz LET","Jenson","Jester","Jokerman","Juice ITC","Kabel Bk BT","Kabel Ult BT","Kailasa","KaiTi","Kalinga","Kannada Sangam MN","Kartika","Kaufmann Bd BT","Kaufmann BT","Khmer UI","KodchiangUPC","Kokila","Korinna BT","Kristen ITC","Krungthep","Kunstler Script","Lao UI","Latha","Leelawadee","Letter Gothic","Levenim MT","LilyUPC","Lithograph","Lithograph Light","Long Island","Lydian BT","Magneto","Maiandra GD","Malayalam Sangam MN","Malgun Gothic","Mangal","Marigold","Marion","Marker Felt","Market","Marlett","Matisse ITC","Matura MT Script Capitals","Meiryo","Meiryo UI","Microsoft Himalaya","Microsoft JhengHei","Microsoft New Tai Lue","Microsoft PhagsPa","Microsoft Tai Le","Microsoft Uighur","Microsoft YaHei","Microsoft Yi Baiti","MingLiU","MingLiU_HKSCS","MingLiU_HKSCS-ExtB","MingLiU-ExtB","Minion","Minion Pro","Miriam","Miriam Fixed","Mistral","Modern","Modern No. 20","Mona Lisa Solid ITC TT","Mongolian Baiti","MONO","MoolBoran","Mrs Eaves","MS LineDraw","MS Mincho","MS PMincho","MS Reference Specialty","MS UI Gothic","MT Extra","MUSEO","MV Boli","Nadeem","Narkisim","NEVIS","News Gothic","News GothicMT","NewsGoth BT","Niagara Engraved","Niagara Solid","Noteworthy","NSimSun","Nyala","OCR A Extended","Old Century","Old English Text MT","Onyx","Onyx BT","OPTIMA","Oriya Sangam MN","OSAKA","OzHandicraft BT","Palace Script MT","Papyrus","Parchment","Party LET","Pegasus","Perpetua","Perpetua Titling MT","PetitaBold","Pickwick","Plantagenet Cherokee","Playbill","PMingLiU","PMingLiU-ExtB","Poor Richard","Poster","PosterBodoni BT","PRINCETOWN LET","Pristina","PTBarnum BT","Pythagoras","Raavi","Rage Italic","Ravie","Ribbon131 Bd BT","Rockwell","Rockwell Condensed","Rockwell Extra Bold","Rod","Roman","Sakkal Majalla","Santa Fe LET","Savoye LET","Sceptre","Script","Script MT Bold","SCRIPTINA","Serifa","Serifa BT","Serifa Th BT","ShelleyVolante BT","Sherwood","Shonar Bangla","Showcard Gothic","Shruti","Signboard","SILKSCREEN","SimHei","Simplified Arabic","Simplified Arabic Fixed","SimSun","SimSun-ExtB","Sinhala Sangam MN","Sketch Rockwell","Skia","Small Fonts","Snap ITC","Snell Roundhand","Socket","Souvenir Lt BT","Staccato222 BT","Steamer","Stencil","Storybook","Styllo","Subway","Swis721 BlkEx BT","Swiss911 XCm BT","Sylfaen","Synchro LET","System","Tamil Sangam MN","Technical","Teletype","Telugu Sangam MN","Tempus Sans ITC","Terminal","Thonburi","Traditional Arabic","Trajan","TRAJAN PRO","Tristan","Tubular","Tunga","Tw Cen MT","Tw Cen MT Condensed","Tw Cen MT Condensed Extra Bold","TypoUpright BT","Unicorn","Univers","Univers CE 55 Medium","Univers Condensed","Utsaah","Vagabond","Vani","Vijaya","Viner Hand ITC","VisualUI","Vivaldi","Vladimir Script","Vrinda","Westminster","WHITNEY","Wide Latin","ZapfEllipt BT","ZapfHumnst BT","ZapfHumnst Dm BT","Zapfino","Zurich BlkEx BT","Zurich Ex BT","ZWAdobeF"])),n=(n=n.concat(t.fonts.userDefinedFonts)).filter((function(e,t){return n.indexOf(e)===t}));var o=document.getElementsByTagName("body")[0],i=document.createElement("div"),a=document.createElement("div"),s={},u={},c=function(){var e=document.createElement("span");return e.style.position="absolute",e.style.left="-9999px",e.style.fontSize="72px",e.style.fontStyle="normal",e.style.fontWeight="normal",e.style.letterSpacing="normal",e.style.lineBreak="auto",e.style.lineHeight="normal",e.style.textTransform="none",e.style.textAlign="left",e.style.textDecoration="none",e.style.textShadow="none",e.style.whiteSpace="normal",e.style.wordBreak="normal",e.style.wordSpacing="normal",e.innerHTML="mmmmmmmmmmlli",e},l=function(e,t){var r=c();return r.style.fontFamily="'"+e+"',"+t,r},f=function(e){for(var t=!1,n=0;n<r.length;n++)if(t=e[n].offsetWidth!==s[r[n]]||e[n].offsetHeight!==u[r[n]])return t;return t},p=function(){for(var e=[],t=0,n=r.length;t<n;t++){var o=c();o.style.fontFamily=r[t],i.appendChild(o),e.push(o)}return e}();o.appendChild(i);for(var h=0,y=r.length;h<y;h++)s[r[h]]=p[h].offsetWidth,u[r[h]]=p[h].offsetHeight;var d=function(){for(var e={},t=0,o=n.length;t<o;t++){for(var i=[],s=0,u=r.length;s<u;s++){var c=l(n[t],r[s]);a.appendChild(c),i.push(c)}e[n[t]]=i}return e}();o.appendChild(a);for(var v=[],g=0,m=n.length;g<m;g++)f(d[n[g]])&&v.push(n[g]);o.removeChild(a),o.removeChild(i),e(v)},pauseBefore:!0},{key:"fontsFlash",getData:function(e,t){return void 0!==window.swfobject?window.swfobject.hasFlashPlayerVersion("9.0.0")?t.fonts.swfPath?void function(e,t){var r="___fp_swf_loaded";window[r]=function(t){e(t)};var n,o=t.fonts.swfContainerId;(n=document.createElement("div")).setAttribute("id",(void 0).fonts.swfContainerId),document.body.appendChild(n);var i={onReady:r};window.swfobject.embedSWF(t.fonts.swfPath,o,"1","1","9.0.0",!1,i,{allowScriptAccess:"always",menu:"false"},{})}((function(t){e(t)}),t):e("missing options.fonts.swfPath"):e("flash not installed"):e("swf object not loaded")},pauseBefore:!0},{key:"audio",getData:function(e,t){var r=t.audio;if(r.excludeIOS11&&navigator.userAgent.match(/OS 11.+Version\/11.+Safari/))return e(t.EXCLUDED);var n=window.OfflineAudioContext||window.webkitOfflineAudioContext;if(null==n)return e(t.NOT_AVAILABLE);var o=new n(1,44100,44100),i=o.createOscillator();i.type="triangle",i.frequency.setValueAtTime(1e4,o.currentTime);var a=o.createDynamicsCompressor();u([["threshold",-50],["knee",40],["ratio",12],["reduction",-20],["attack",0],["release",.25]],(function(e){void 0!==a[e[0]]&&"function"==typeof a[e[0]].setValueAtTime&&a[e[0]].setValueAtTime(e[1],o.currentTime)})),i.connect(a),a.connect(o.destination),i.start(0),o.startRendering();var s=setTimeout((function(){return console.warn('Audio fingerprint timed out. Please report bug at https://github.com/fingerprintjs/fingerprintjs with your user agent: "'+navigator.userAgent+'".'),o.oncomplete=function(){},o=null,e("audioTimeout")}),r.timeout);o.oncomplete=function(t){var r;try{clearTimeout(s),r=t.renderedBuffer.getChannelData(0).slice(4500,5e3).reduce((function(e,t){return e+Math.abs(t)}),0).toString(),i.disconnect(),a.disconnect()}catch(t){return void e(t)}e(r)}}},{key:"enumerateDevices",getData:function(e,t){if(!navigator.mediaDevices||!navigator.mediaDevices.enumerateDevices)return e(t.NOT_AVAILABLE);navigator.mediaDevices.enumerateDevices().then((function(t){e(t.map((function(e){return"id="+e.deviceId+";gid="+e.groupId+";"+e.kind+";"+e.label})))})).catch((function(t){e(t)}))}}],m=function(e){throw new Error("'new Fingerprint()' is deprecated, see https://github.com/fingerprintjs/fingerprintjs#upgrade-guide-from-182-to-200")};return m.get=function(e,t){t?e||(e={}):(t=e,e={}),function(e,t){if(null==t)return e;var r,n;for(n in t)null==(r=t[n])||Object.prototype.hasOwnProperty.call(e,n)||(e[n]=r)}(e,s),e.components=e.extraComponents.concat(g);var r={data:[],addPreprocessedComponent:function(t,n){"function"==typeof e.preprocessor&&(n=e.preprocessor(t,n)),r.data.push({key:t,value:n})}},n=-1,o=function(i){if((n+=1)>=e.components.length)t(r.data);else{var a=e.components[n];if(e.excludes[a.key])o(!1);else{if(!i&&a.pauseBefore)return n-=1,void setTimeout((function(){o(!0)}),1);try{a.getData((function(e){r.addPreprocessedComponent(a.key,e),o(!1)}),e)}catch(e){r.addPreprocessedComponent(a.key,String(e)),o(!1)}}}};o(!1)},m.getPromise=function(e){return new Promise((function(t,r){m.get(e,t)}))},m.getV18=function(e,t){return null==t&&(t=e,e={}),m.get(e,(function(r){for(var n=[],o=0;o<r.length;o++){var i=r[o];if(i.value===(e.NOT_AVAILABLE||"not available"))n.push({key:i.key,value:"unknown"});else if("plugins"===i.key)n.push({key:"plugins",value:c(i.value,(function(e){var t=c(e[2],(function(e){return e.join?e.join("~"):e})).join(",");return[e[0],e[1],t].join("::")}))});else if(-1!==["canvas","webgl"].indexOf(i.key)&&Array.isArray(i.value))n.push({key:i.key,value:i.value.join("~")});else if(-1!==["sessionStorage","localStorage","indexedDb","addBehavior","openDatabase"].indexOf(i.key)){if(!i.value)continue;n.push({key:i.key,value:1})}else i.value?n.push(i.value.join?{key:i.key,value:i.value.join(";")}:i):n.push({key:i.key,value:i.value})}var s=a(c(n,(function(e){return e.value})).join("~~~"),31);t(s,n)}))},m.x64hash128=a,m.VERSION="2.1.4",m},"undefined"!=typeof window&&r.amdO?void 0===(o="function"==typeof(n=a)?n.call(t,r,t,e):n)||(e.exports=o):e.exports?e.exports=a():i.exports?i.exports=a():i.Fingerprint2=a()},294:function(e){e.exports=function(e){function t(n){if(r[n])return r[n].exports;var o=r[n]={i:n,l:!1,exports:{}};return e[n].call(o.exports,o,o.exports,t),o.l=!0,o.exports}var r={};return t.m=e,t.c=r,t.i=function(e){return e},t.d=function(e,r,n){t.o(e,r)||Object.defineProperty(e,r,{configurable:!1,enumerable:!0,get:n})},t.n=function(e){var r=e&&e.__esModule?function(){return e.default}:function(){return e};return t.d(r,"a",r),r},t.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},t.p="",t(t.s=1)}([function(e,t,r){Object.defineProperty(t,"__esModule",{value:!0});var n=function(){function e(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,r,n){return r&&e(t.prototype,r),n&&e(t,n),t}}(),o=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e)}return n(e,null,[{key:"hash",value:function(t){return e.hex(e.md51(t))}},{key:"md5cycle",value:function(t,r){var n=t[0],o=t[1],i=t[2],a=t[3];n=e.ff(n,o,i,a,r[0],7,-680876936),a=e.ff(a,n,o,i,r[1],12,-389564586),i=e.ff(i,a,n,o,r[2],17,606105819),o=e.ff(o,i,a,n,r[3],22,-1044525330),n=e.ff(n,o,i,a,r[4],7,-176418897),a=e.ff(a,n,o,i,r[5],12,1200080426),i=e.ff(i,a,n,o,r[6],17,-1473231341),o=e.ff(o,i,a,n,r[7],22,-45705983),n=e.ff(n,o,i,a,r[8],7,1770035416),a=e.ff(a,n,o,i,r[9],12,-1958414417),i=e.ff(i,a,n,o,r[10],17,-42063),o=e.ff(o,i,a,n,r[11],22,-1990404162),n=e.ff(n,o,i,a,r[12],7,1804603682),a=e.ff(a,n,o,i,r[13],12,-40341101),i=e.ff(i,a,n,o,r[14],17,-1502002290),o=e.ff(o,i,a,n,r[15],22,1236535329),n=e.gg(n,o,i,a,r[1],5,-165796510),a=e.gg(a,n,o,i,r[6],9,-1069501632),i=e.gg(i,a,n,o,r[11],14,643717713),o=e.gg(o,i,a,n,r[0],20,-373897302),n=e.gg(n,o,i,a,r[5],5,-701558691),a=e.gg(a,n,o,i,r[10],9,38016083),i=e.gg(i,a,n,o,r[15],14,-660478335),o=e.gg(o,i,a,n,r[4],20,-405537848),n=e.gg(n,o,i,a,r[9],5,568446438),a=e.gg(a,n,o,i,r[14],9,-1019803690),i=e.gg(i,a,n,o,r[3],14,-187363961),o=e.gg(o,i,a,n,r[8],20,1163531501),n=e.gg(n,o,i,a,r[13],5,-1444681467),a=e.gg(a,n,o,i,r[2],9,-51403784),i=e.gg(i,a,n,o,r[7],14,1735328473),o=e.gg(o,i,a,n,r[12],20,-1926607734),n=e.hh(n,o,i,a,r[5],4,-378558),a=e.hh(a,n,o,i,r[8],11,-2022574463),i=e.hh(i,a,n,o,r[11],16,1839030562),o=e.hh(o,i,a,n,r[14],23,-35309556),n=e.hh(n,o,i,a,r[1],4,-1530992060),a=e.hh(a,n,o,i,r[4],11,1272893353),i=e.hh(i,a,n,o,r[7],16,-155497632),o=e.hh(o,i,a,n,r[10],23,-1094730640),n=e.hh(n,o,i,a,r[13],4,681279174),a=e.hh(a,n,o,i,r[0],11,-358537222),i=e.hh(i,a,n,o,r[3],16,-722521979),o=e.hh(o,i,a,n,r[6],23,76029189),n=e.hh(n,o,i,a,r[9],4,-640364487),a=e.hh(a,n,o,i,r[12],11,-421815835),i=e.hh(i,a,n,o,r[15],16,530742520),o=e.hh(o,i,a,n,r[2],23,-995338651),n=e.ii(n,o,i,a,r[0],6,-198630844),a=e.ii(a,n,o,i,r[7],10,1126891415),i=e.ii(i,a,n,o,r[14],15,-1416354905),o=e.ii(o,i,a,n,r[5],21,-57434055),n=e.ii(n,o,i,a,r[12],6,1700485571),a=e.ii(a,n,o,i,r[3],10,-1894986606),i=e.ii(i,a,n,o,r[10],15,-1051523),o=e.ii(o,i,a,n,r[1],21,-2054922799),n=e.ii(n,o,i,a,r[8],6,1873313359),a=e.ii(a,n,o,i,r[15],10,-30611744),i=e.ii(i,a,n,o,r[6],15,-1560198380),o=e.ii(o,i,a,n,r[13],21,1309151649),n=e.ii(n,o,i,a,r[4],6,-145523070),a=e.ii(a,n,o,i,r[11],10,-1120210379),i=e.ii(i,a,n,o,r[2],15,718787259),o=e.ii(o,i,a,n,r[9],21,-343485551),t[0]=n+t[0]&4294967295,t[1]=o+t[1]&4294967295,t[2]=i+t[2]&4294967295,t[3]=a+t[3]&4294967295}},{key:"cmn",value:function(e,t,r,n,o,i){return((t=(t+e&4294967295)+(n+i&4294967295)&4294967295)<<o|t>>>32-o)+r&4294967295}},{key:"ff",value:function(t,r,n,o,i,a,s){return e.cmn(r&n|~r&o,t,r,i,a,s)}},{key:"gg",value:function(t,r,n,o,i,a,s){return e.cmn(r&o|n&~o,t,r,i,a,s)}},{key:"hh",value:function(t,r,n,o,i,a,s){return e.cmn(r^n^o,t,r,i,a,s)}},{key:"ii",value:function(t,r,n,o,i,a,s){return e.cmn(n^(r|~o),t,r,i,a,s)}},{key:"md51",value:function(t){for(var r,n=t.length,o=[1732584193,-271733879,-1732584194,271733878],i=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],a=64;a<=n;a+=64)e.md5cycle(o,e.md5blk(t.substring(a-64,a)));for(t=t.substring(a-64),a=0,r=t.length;a<r;a++)i[a>>2]|=t.charCodeAt(a)<<(a%4<<3);if(i[a>>2]|=128<<(a%4<<3),a>55)for(e.md5cycle(o,i),a=0;a<16;a++)i[a]=0;return i[14]=8*n,e.md5cycle(o,i),o}},{key:"md5blk",value:function(e){for(var t=[],r=0;r<64;r+=4)t[r>>2]=e.charCodeAt(r)+(e.charCodeAt(r+1)<<8)+(e.charCodeAt(r+2)<<16)+(e.charCodeAt(r+3)<<24);return t}},{key:"rhex",value:function(t){var r="";return r+=e.hexArray[t>>4&15]+e.hexArray[t>>0&15],r+=e.hexArray[t>>12&15]+e.hexArray[t>>8&15],(r+=e.hexArray[t>>20&15]+e.hexArray[t>>16&15])+(e.hexArray[t>>28&15]+e.hexArray[t>>24&15])}},{key:"hex",value:function(t){for(var r=t.length,n=0;n<r;n++)t[n]=e.rhex(t[n]);return t.join("")}}]),e}();o.hexArray=["0","1","2","3","4","5","6","7","8","9","a","b","c","d","e","f"],t.default=o},function(e,t,r){e.exports=r(0)}])},737:function(e){e.exports=function(e){function t(n){if(r[n])return r[n].exports;var o=r[n]={i:n,l:!1,exports:{}};return e[n].call(o.exports,o,o.exports,t),o.l=!0,o.exports}var r={};return t.m=e,t.c=r,t.i=function(e){return e},t.d=function(e,r,n){t.o(e,r)||Object.defineProperty(e,r,{configurable:!1,enumerable:!0,get:n})},t.n=function(e){var r=e&&e.__esModule?function(){return e.default}:function(){return e};return t.d(r,"a",r),r},t.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},t.p="",t(t.s=1)}([function(e,t,r){Object.defineProperty(t,"__esModule",{value:!0});var n=function(){function e(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,r,n){return r&&e(t.prototype,r),n&&e(t,n),t}}(),o=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e)}return n(e,null,[{key:"hash",value:function(t){return e.stringToHex(e.arrayToString(e.run(e.stringToArray(t),8*t.length)))}},{key:"run",value:function(t,r){var n=[1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298],o=15+(r+64>>9<<4),i=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],a=0,s=1779033703,u=-1150833019,c=1013904242,l=-1521486534,f=1359893119,p=-1694144372,h=528734635,y=1541459225,d=s,v=u,g=c,m=l,b=f,w=p,P=h,T=y;for(t[r>>5]|=128<<24-r%32,t[o]=r;a<o;a+=16){s=d,u=v,c=g,l=m,f=b,p=w,h=P,y=T;for(var k=0,O=null,S=null;k<64;k+=1)i[k]=k<16?t[k+a]:e.add(e.add(e.add(e.gamma1256(i[k-2]),i[k-7]),e.gamma0256(i[k-15])),i[k-16]),O=e.add(e.add(e.add(e.add(T,e.sigma1256(b)),e.ch(b,w,P)),n[k]),i[k]),S=e.add(e.sigma0256(d),e.maj(d,v,g)),T=P,P=w,w=b,b=e.add(m,O),m=g,g=v,v=d,d=e.add(O,S);d=e.add(d,s),v=e.add(v,u),g=e.add(g,c),m=e.add(m,l),b=e.add(b,f),w=e.add(w,p),P=e.add(P,h),T=e.add(T,y)}return[d,v,g,m,b,w,P,T]}},{key:"arrayToString",value:function(e){for(var t=32*e.length,r=0,n="";r<t;r+=8)n+=String.fromCharCode(e[r>>5]>>>24-r%32&255);return n}},{key:"stringToArray",value:function(e){for(var t=8*e.length,r=Array(e.length>>2),n=r.length,o=0;o<n;o+=1)r[o]=0;for(o=0;o<t;o+=8)r[o>>5]|=(255&e.charCodeAt(o/8))<<24-o%32;return r}},{key:"stringToHex",value:function(e){for(var t="0123456789abcdef",r=e.length,n="",o=null,i=0;i<r;i+=1)o=e.charCodeAt(i),n+=t.charAt(o>>>4&15)+t.charAt(15&o);return n}},{key:"rotl",value:function(e,t){return e>>>t|e<<32-t}},{key:"rotr",value:function(e,t){return e>>>t}},{key:"ch",value:function(e,t,r){return e&t^~e&r}},{key:"maj",value:function(e,t,r){return e&t^e&r^t&r}},{key:"sigma0256",value:function(t){return e.rotl(t,2)^e.rotl(t,13)^e.rotl(t,22)}},{key:"sigma1256",value:function(t){return e.rotl(t,6)^e.rotl(t,11)^e.rotl(t,25)}},{key:"gamma0256",value:function(t){return e.rotl(t,7)^e.rotl(t,18)^e.rotr(t,3)}},{key:"gamma1256",value:function(t){return e.rotl(t,17)^e.rotl(t,19)^e.rotr(t,10)}},{key:"add",value:function(e,t){var r=(65535&e)+(65535&t);return(e>>16)+(t>>16)+(r>>16)<<16|65535&r}}]),e}();t.default=o},function(e,t,r){e.exports=r(0)}])}},t={};function r(n){var o=t[n];if(void 0!==o)return o.exports;var i=t[n]={exports:{}};return e[n].call(i.exports,i,i.exports,r),i.exports}r.amdO={},r.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return r.d(t,{a:t}),t},r.d=(e,t)=>{for(var n in t)r.o(t,n)&&!r.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},r.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t);var n={};(()=>{r.d(n,{Z:()=>Dn});var e="AlipayMini",t="uniapp-h5",o="$Login",i="$PageView",a="$PageClick",s="$PageStay",u="$PageLeave",c="$MpLaunch",l="$MpHide",f="$PageShare",p="$PageInvite",h="$PageExposure",y="$Touch",d="alipay.indi",v="wx.mini",g="alipay.tp.vivo",m="alipay.honor",b="alipay.huawei",w="alipay.tp.xiaomi",P="alipay.tp.oppo",T=["dynamicStrProps","dynamicIntProps","dynamicFloatProps"],k=r(737),O=r.n(k),S=r(294),_=r.n(S);function L(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=A(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var n=0,o=function(){};return{s:o,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,a=!0,s=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return a=e.done,e},e:function(e){s=!0,i=e},f:function(){try{a||null==r.return||r.return()}finally{if(s)throw i}}}}function A(e,t){if(e){if("string"==typeof e)return C(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?C(e,t):void 0}}function C(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function x(e){return x="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},x(e)}function E(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function I(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?E(Object(r),!0).forEach((function(t){j(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):E(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function j(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function B(e){return"[object Object]"===Object.prototype.toString.call(e)}function R(){return Date.now()+"-"+Math.floor(1e7*Math.random())+"-"+Math.random().toString(16).replace(".","")+"-"+String(31242*Math.random()).replace(".","").slice(0,8)}function D(){var r="undefined"!=typeof uni,n="undefined"!=typeof my;try{if(n&&!r)return e;if(r){var o=uni.getSystemInfoSync().uniPlatform;return"undefined"!=typeof wx&&"mp-weixin"===o?"uniapp-weixin":"undefined"!=typeof tt?"uniapp-byte-dance":"undefined"!=typeof ks?"uniapp-kuaishou":"undefined"!=typeof dd?"uniapp-ding":n?"uniapp-alipay":"undefined"!=typeof plus?"uniapp-app-plus":"web"===o?t:"uniapp-unknown"}if(void 0!==window)return"js"}catch(e){return console.warn("环境判断出错:",e)}}function M(e,t,r){var n=null;return function(){for(var o=arguments.length,i=new Array(o),a=0;a<o;a++)i[a]=arguments[a];return n&&clearTimeout(n),new Promise((function(o){n=setTimeout((function(){o(e.call.apply(e,[r].concat(i))),n=null}),t)}))}}function U(e){if(!e)return!1;var t=toString.call(e);return"[object Function]"==t||"[object AsyncFunction]"==t}function G(e){return""!==e&&null!=e&&!isNaN(e)}function N(e){if(!B(e))return"";for(var t=Object.keys(e).sort(),r="",n=0;n<t.length;n++){var o=e[t[n]];r+=(r?"&":"")+(B(o)?JSON.stringify(o):o)}return O().hash(r)}function H(e){if(!B(e))return"";for(var t=Object.keys(e).sort(),r="",n=0;n<t.length;n++){var o=e[t[n]];r+=(r?"&":"")+(B(o)?JSON.stringify(o):o)}return _().hash(r).toString()}function $(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=arguments.length>2?arguments[2]:void 0,n=Date.now(),o=R(),i=N(r),a="jjpiewjf;k029q3-1*ksk3323m",s=H(I({microtime:n,nonstr:o,hash_data:i,secret:a},t));return"".concat(e,"?microtime=").concat(n,"&nonstr=").concat(o,"&hash_data=").concat(i,"&sign=").concat(s)}function F(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return e.replace(/([A-Z])/g,"_$1").toLowerCase()}function V(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if("object"!==x(e)||null==e)return e;var t=Array.isArray(e)?[]:{};for(var r in e)e.hasOwnProperty(r)&&(t[r]=V(e[r]));return t}function W(e){return void 0===e||""===e||null===e||"number"==typeof e&&isNaN(e)}function X(){var e,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",r="",n=L(t);try{for(n.s();!(e=n.n()).done;){var o=e.value;r+=o.charCodeAt(0)}}catch(e){n.e(e)}finally{n.f()}return r}function K(e,t){var r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if("object"===x(e)&&null!=e)for(var n in e){var o=void 0,i=/sensors_.+/.exec(n);if(i&&(o=F(i[0].slice(8))),e.hasOwnProperty(n)&&(n===t||r&&o===t))return e[n];if("object"===x(e[n])){var a=K(e[n],t);if(a)return a}}}function q(e,t,r){if(B(r)){var n=t.replace(/.*?dynamic(.+?)Props$/,"$1").toLowerCase();Object.entries(r).forEach((function(t,r){var o,i,a=(i=2,function(e){if(Array.isArray(e))return e}(o=t)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i=[],a=!0,s=!1;try{for(r=r.call(e);!(a=(n=r.next()).done)&&(i.push(n.value),!t||i.length!==t);a=!0);}catch(e){s=!0,o=e}finally{try{a||null==r.return||r.return()}finally{if(s)throw o}}return i}}(o,i)||A(o,i)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()),s=a[0],u=a[1];e["dynamic_".concat(n,"_key_").concat(r+1)]=s,e["dynamic_".concat(n,"_value_").concat(r+1)]=u}))}}r(820);const z="1.3.17";function Q(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=function(e,t){if(e){if("string"==typeof e)return J(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?J(e,t):void 0}}(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var n=0,o=function(){};return{s:o,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,a=!0,s=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return a=e.done,e},e:function(e){s=!0,i=e},f:function(){try{a||null==r.return||r.return()}finally{if(s)throw i}}}}function J(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function Z(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function Y(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var ee="sensonrsdata",te="DISTINCT_ID_STORE_KEY",re=D(),ne=function(){function t(e){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t),Y(this,"$terminal",void 0),Y(this,"$lib_version",z),Y(this,"storageKey","RRZU_SENSORS_DATA"),Y(this,"bufferPageProperties",{}),Y(this,"preUrl",void 0),Y(this,"format",void 0),Y(this,"autoTrack",{appLaunch:!0,appHide:!0,pageShow:!0,pageLeave:!0,pageClick:!0,pageStay:!0,pageShare:!0,pageExposure:!0,pageAppear:!0}),Y(this,"serverUrl",""),Y(this,"universalIdUrl",""),Y(this,"needCookies",void 0),Y(this,"customDownloadChannel",void 0),Y(this,"isIFrame",void 0),Y(this,"bindServerUrl",""),Y(this,"showLog",!1),Y(this,"batchSendTimeout",3e3),Y(this,"dataSendTimeout",6e4),Y(this,"scrollDelayTime",4e3),Y(this,"isTrackSinglePage",!1),Y(this,"trackUrlMap",void 0),Y(this,"store",{open_id:"",distinct_id:"",union_id:"",user_id:"",initial_id:"",universal_id:"",commProperties:{},commGroupProperties:{},is_login:!1}),Y(this,"cycleProperties",{}),Y(this,"launchParams",{}),Y(this,"loadParams",{}),Y(this,"pageTitleConfig",{}),Y(this,"pageProperties",{}),Y(this,"dataPoll",void 0),Y(this,"requestHandler",void 0),Y(this,"pageLoadListener",void 0),Y(this,"pageUnloadListener",void 0),Y(this,"pageListener",void 0),Y(this,"clickListener",void 0),Y(this,"stayListener",void 0),Y(this,"tabItemListener",void 0),Y(this,"systemInfoGetter",void 0),Y(this,"storageHandler",void 0),Y(this,"launchListener",void 0),Y(this,"unloadListener",void 0),Y(this,"shareListener",void 0),Y(this,"exposureListener",void 0),Y(this,"appearListener",void 0),Y(this,"touchListener",void 0),Y(this,"inviteUserId",void 0),Y(this,"inviteDistinctId",void 0),Y(this,"inviteTime",void 0),Y(this,"tabBarPositionMap",void 0),this.requestHandler=e.requestHandler,this.dataPoll=e.dataPoll,this.pageLoadListener=e.pageLoadListener,this.pageUnloadListener=e.pageUnloadListener,this.pageListener=e.pageListener,this.clickListener=e.clickListener,this.stayListener=e.stayListener,this.launchListener=e.launchListener,this.unloadListener=e.unloadListener,this.systemInfoGetter=e.systemInfoGetter,this.storageHandler=e.storageHandler,this.$terminal=this.systemInfoGetter.getTerminial(),e.launchListener.register(this),e.pageLoadListener.register(this),e.pageListener.register(this),e.pageUnloadListener.register(this),e.unloadListener.register(this),this.shareListener=e.shareListener,this.exposureListener=e.exposureListener,this.appearListener=e.appearListener,this.touchListener=e.touchListener}var r,n;return r=t,n=[{key:"init",value:function(e){if(!e.serverUrl)return console.warn("当前 serverUrl 为空或不正确，只在控制台打印日志，network 中不会发数据，请配置正确的 serverUrl");if(e.bindServerUrl||console.warn("当前 bindServerUrl 为空或不正确"),e.universalIdUrl||console.warn("当前 universalIdUrl 为空或不正确"),this.initStore(),e.terminal&&(this.$terminal=e.terminal,this.systemInfoGetter.getTerminial=function(){return e.terminal}),this.initDistinctId(),this.serverUrl=e.serverUrl,this.universalIdUrl=e.universalIdUrl||"",this.needCookies=e.needCookies,e.customDownloadChannel&&(this.customDownloadChannel=e.customDownloadChannel),this.isIFrame=e.isIFrame,this.bindServerUrl=e.bindServerUrl||"",this.format=e.format,e.showLog&&(this.showLog=e.showLog),e.isTrackSinglePage&&(this.isTrackSinglePage=e.isTrackSinglePage),e.trackUrlMap&&(this.trackUrlMap=e.trackUrlMap),B(e.tabBarPositionMap)&&(this.tabBarPositionMap=e.tabBarPositionMap),G(e.batchSendTimeout)&&(this.batchSendTimeout=e.batchSendTimeout),G(e.dataSendTimeout)&&(this.dataSendTimeout=e.dataSendTimeout),G(e.scrollDelayTime)&&(this.scrollDelayTime=e.scrollDelayTime),e.autoTrack&&(e.autoTrack.appLaunch&&(this.autoTrack.appLaunch=e.autoTrack.appLaunch),e.autoTrack.appHide&&(this.autoTrack.appHide=e.autoTrack.appHide),e.autoTrack.pageShow&&(this.autoTrack.pageShow=e.autoTrack.pageShow),e.autoTrack.pageLeave&&(this.autoTrack.pageLeave=e.autoTrack.pageLeave),e.autoTrack.pageClick&&(this.autoTrack.pageClick=e.autoTrack.pageClick)),e.pageTitleConfig){if(!B(e.pageTitleConfig))return console.warn("设置页面标题参数必须为对象");this.pageTitleConfig=e.pageTitleConfig}this.systemInfoGetter.init(this),this.dataPoll.init(this),this.requestHandler.serverUrl=e.serverUrl,this.requestHandler.needCookies=e.needCookies,G(this.dataSendTimeout)&&(this.requestHandler.dataSendTimeout=this.dataSendTimeout),(this.autoTrack.pageShow||this.autoTrack.pageStay)&&this.pageListener.init(this),this.pageLoadListener.init(this),this.pageUnloadListener.init(this),this.autoTrack.pageClick&&this.clickListener.init(this),this.autoTrack.pageStay&&this.stayListener.init(this),this.unloadListener.init(this),this.launchListener&&this.launchListener.init(this),this.shareListener.init(this),this.exposureListener.init(this),this.appearListener.init(this),this.touchListener.init(this),this.bufferStore()}},{key:"initStore",value:function(){var e=this.storageHandler.get(ee);e&&(this.store=e,e.commGroupProperties||(this.store.commGroupProperties={}))}},{key:"initDistinctId",value:function(){var e=this.systemInfoGetter.generateRandomId(),t=this.systemInfoGetter.getTerminial(),r=[v,g,m,b,w,P];this.store.distinct_id||r.includes(t)||(this.store.distinct_id=e),this.store.initial_id=e}},{key:"bufferStore",value:function(){this.storageHandler.set(ee,this.store)}},{key:"appendLaunchQuery",value:function(e){var t=this.systemInfoGetter.getLaunchQuery();for(var r in t)W(t[r])||(e.properties["launch_"+F(r)]=t[r],"third_terminal"===r&&(e.properties.launch_statistical_from=t[r]))}},{key:"login",value:function(e){var t=this.storageHandler.get(te)||{};this.store.user_id&&this.store.user_id!==e?(t&&t[e]&&(this.store.distinct_id=t[e]),this.store.user_id=e):(this.store.user_id=e,t[e]=this.store.distinct_id),this.storageHandler.set(te,t),this.bufferStore()}},{key:"identify",value:function(e){e&&(this.store.open_id=e,this.store.distinct_id=e,this.bufferStore())}},{key:"identifyUnion",value:function(e){var t=e.openid,r=e.unionid;this.store.open_id=t,this.store.union_id=r,r&&(this.store.distinct_id=r),this.bufferStore()}},{key:"getTrackData",value:function(e){var t={client_event_id:R()};this.store.user_id&&(t.user_id=this.store.user_id),this.store.open_id&&(t.open_id=this.store.open_id),this.store.union_id&&(t.union_id=this.store.union_id),this.store.initial_id&&(t.initial_id=this.store.initial_id),this.store.universal_id&&(t.universal_id=this.store.universal_id),t.distinct_id=this.store.latestTrafficSourceType&&this.store.sourceDistinctId||this.store.distinct_id;var r=this.systemInfoGetter.getTimestamp();for(var n in t.lib={$terminal:this.$terminal,$lib_version:z},t.properties={$latest_traffic_source_type:this.store.latestTrafficSourceType||this.systemInfoGetter.getLatestTrafficSourceType(),$latest_referrer:this.store.latestReferrer||this.systemInfoGetter.getLatestReferrer(),$timezone_offset:(new Date).getTimezoneOffset(),$viewport_width:this.systemInfoGetter.getViewportWidth(),$viewport_height:this.systemInfoGetter.getViewportHeight(),$screen_height:this.systemInfoGetter.getScreenHeight(),$screen_top:this.systemInfoGetter.getScrollTop(),$url:this.systemInfoGetter.getUrl(),$url_path:this.systemInfoGetter.getUrlPath(),$referrer:this.systemInfoGetter.getReferrer(),$is_first_day:this.getIsFirstDay(r),$is_first_time:!this.store.firstVisitTime,$is_login:!!this.store.is_login},this.appendLaunchQuery(t),this.cycleProperties)W(this.cycleProperties[n])||(t.properties[F(n)]=this.cycleProperties[n]);var o=this.systemInfoGetter.getTitle();o&&(t.properties.$title=o),t.event_type=e.eventType||"$Track",t.event_name=e.key,t.event_time=Number(r.toString().slice(0,10)),t.event_date=t.event_time,t.event_millitime=r,t.properties.$event_duration=this.systemInfoGetter.getPageDurationTime(),e.$appear_duration&&(t.properties.$appear_duration=e.$appear_duration),e.eventType===a&&(t.properties.$element_type=e.$element_type,e.$page_x&&(t.properties.$page_x=e.$page_x),e.$page_y&&(t.properties.$page_y=e.$page_y),e.$client_x&&(t.properties.$client_x=e.$client_x),e.$client_y&&(t.properties.$client_y=e.$client_y),e.$element_class_name&&(t.properties.$element_class_name=e.$element_class_name),e.$element_content&&(t.properties.$element_content=e.$element_content),e.$element_selector&&(t.properties.$element_selector=e.$element_selector),e.$element_path&&(t.properties.$element_path=e.$element_path));var i=this.store.commProperties;Object.assign(t.properties,this.parseProperties(i));var s=i&&(i.$user_register_time||i.$userRegisterTime);if(t.properties.$register_less_24h=s&&1e3*s+864e5>r?1:0,Array.isArray(e.commPropertieGroup)){var u,c=Q(e.commPropertieGroup);try{for(c.s();!(u=c.n()).done;){var l=u.value,f=this.store.commGroupProperties[l];f&&Object.assign(t.properties,this.parseProperties(f))}}catch(e){c.e(e)}finally{c.f()}}else if("string"==typeof e.commPropertieGroup){var p=this.store.commGroupProperties[e.commPropertieGroup];Object.assign(t.properties,this.parseProperties(p))}var h=this.pageProperties[this.systemInfoGetter.getUrl()]||{};Object.assign(t.properties,this.parseProperties(h));var y=e.customProperties;return Object.assign(t.properties,this.parseProperties(y)),this.inviteTime&&(this.inviteUserId||this.inviteDistinctId)&&(t.properties.$invite_time=this.inviteTime,this.inviteUserId&&(t.properties.$invite_user_id=this.inviteUserId),this.inviteDistinctId&&(t.properties.$invite_distinct_id=this.inviteDistinctId)),this.store.lastVisitTime=t.event_time,this.store.firstVisitTime||(this.store.firstVisitTime=t.event_millitime,this.bufferStore()),this.formatTrackData(t)}},{key:"formatTrackData",value:function(e){if(!this.format)return e;for(var t in e){var r=e[t],n=this.format(t);e[n]=B(r)?this.formatTrackData(r):r,n!==t&&delete e[t]}return e}},{key:"parseProperties",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",r={},n=t?F(t)+"_":"";if(B(e)&&!Array.isArray(e))for(var o in e)T.includes(o)?q(r,o,e[o]):!W(e[o])&&Object.assign(r,this.parseProperties(e[o],n+(/^(\$|[a-z]|_|\d)+$/gi.test(o)?o:X(o))));else W(e)||(r[F(t)]=e);return r}},{key:"getIsFirstDay",value:function(e){return!this.store.firstVisitTime||new Date(this.store.firstVisitTime).getDate()===new Date(e).getDate()}},{key:"onBeforeLaunch",value:function(){this.cycleProperties={}}},{key:"onLaunch",value:function(){var e=this;this.dataPoll.modifyAny((function(t){e.appendLaunchQuery(t)}))}},{key:"onPageLoad",value:function(){var e=this.systemInfoGetter.getOnLoadQuery();e.dc_st&&(e.dc_uid||e.dc_did)&&(this.inviteUserId=e.dc_uid,this.inviteDistinctId=e.dc_did,this.inviteTime=e.dc_st),(e.dc_acp||e.id)&&this.appendCycleProperties({activityId:e.dc_acp,goodsId:e.id});var t=this.systemInfoGetter.getOnLoadQuery(),r={};for(var n in t)(t[n]&&"undefined"!==t[n]||0===t[n])&&(r["onload_"+F(n)]=t[n]);this.appendPageProperties(r)}},{key:"onUnload",value:function(){this.storageHandler.set(ee,this.store)}},{key:"onBeforeShow",value:function(){if(re!==e&&"js"!==re){var t=getCurrentPages(),r=t.length;if(r&&r>1){var n=t[r-1],o=t[r-2];n&&o&&n.route===o.route&&n.options===o.options&&(this.pageProperties[this.systemInfoGetter.getUrl()]=this.bufferPageProperties)}}else this.systemInfoGetter.getUrl()===this.preUrl&&(this.pageProperties[this.systemInfoGetter.getUrl()]=this.bufferPageProperties)}},{key:"onPageHide",value:function(){this.preUrl=this.systemInfoGetter.getUrl()}},{key:"onAfterPageHide",value:function(){this.bufferPageProperties=this.pageProperties[this.systemInfoGetter.getUrl()]||{}}},{key:"onAfterPageUnload",value:function(){this.pageProperties[this.systemInfoGetter.getUrl()]&&delete this.pageProperties[this.systemInfoGetter.getUrl()]}},{key:"identifySource",value:function(e){e.id&&(this.store.sourceDistinctId=e.id,this.store.latestTrafficSourceType=e.latestTrafficSourceType,this.store.latestReferrer=e.latestReferrer,this.bufferStore())}},{key:"clearSource",value:function(){delete this.store.sourceDistinctId,this.bufferStore()}},{key:"clearLatestTrafficSource",value:function(){delete this.store.latestTrafficSourceType,delete this.store.latestReferrer,this.bufferStore()}},{key:"appendCommProperties",value:function(e,t){var r=this;if(t)for(var n in e){var o=this.store.commGroupProperties[t]||{};o[n]=e[n],this.store.commGroupProperties[t]=o}else for(var i in e)this.store.commProperties[i]=e[i];!t&&this.dataPoll.modifyAny((function(t){Object.assign(t.properties,r.parseProperties(e))})),this.bufferStore()}},{key:"replaceCommProperties",value:function(e,t){t?this.store.commGroupProperties[t]&&(this.store.commGroupProperties[t]={}):this.store.commProperties={},this.appendCommProperties(e,t)}},{key:"removeCommProperties",value:function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];if(0!==t.findIndex((function(e){return"string"!=typeof e})))for(var n=0,o=t;n<o.length;n++){var i,a=o[n],s=Q(a.value);try{for(s.s();!(i=s.n()).done;){var u=i.value;this.store.commGroupProperties[a.key]&&delete this.store.commGroupProperties[a.key][u]}}catch(e){s.e(e)}finally{s.f()}}else for(var c=0,l=t;c<l.length;c++){var f=l[c];delete this.store.commProperties[f]}this.bufferStore()}},{key:"removeCommPropertiesGroup",value:function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];for(var n=0,o=t;n<o.length;n++){var i=o[n];delete this.store.commGroupProperties[i]}this.bufferStore()}},{key:"appendPageProperties",value:function(e){var t=this,r=this.systemInfoGetter.getUrl();this.pageProperties[this.systemInfoGetter.getUrl()]||(this.pageProperties[this.systemInfoGetter.getUrl()]={}),Object.assign(this.pageProperties[this.systemInfoGetter.getUrl()],e),this.dataPoll.modifyAny((function(n){n.properties.$url===r&&Object.assign(n.properties,t.parseProperties(e))}))}},{key:"removePageProperties",value:function(){if(this.pageProperties[this.systemInfoGetter.getUrl()]){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];for(var n in t)delete this.pageProperties[this.systemInfoGetter.getUrl()][n]}}},{key:"getCommProperties",value:function(e,t){var r=t?this.store.commGroupProperties[t]||{}:this.store.commProperties;return r[e]||r[F(e)]||r[function(){return(arguments.length>0&&void 0!==arguments[0]?arguments[0]:"").replace(/\_(\w)/g,(function(e,t){return t.toUpperCase()}))}(e)]}},{key:"appendCommPropertiesToPage",value:function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];for(var n=0,o=t||[];n<o.length;n++){var i=o[n],a=this.store.commGroupProperties[i]||{};this.appendPageProperties(a)}}},{key:"appendCycleProperties",value:function(e){for(var t in e)this.cycleProperties[t]=e[t]}},{key:"removeCycleProperties",value:function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];for(var n in t)delete this.cycleProperties[n]}}],n&&Z(r.prototype,n),Object.defineProperty(r,"prototype",{writable:!1}),t}();function oe(e){return oe="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},oe(e)}function ie(){ie=function(){return e};var e={},t=Object.prototype,r=t.hasOwnProperty,n="function"==typeof Symbol?Symbol:{},o=n.iterator||"@@iterator",i=n.asyncIterator||"@@asyncIterator",a=n.toStringTag||"@@toStringTag";function s(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{s({},"")}catch(e){s=function(e,t,r){return e[t]=r}}function u(e,t,r,n){var o=t&&t.prototype instanceof f?t:f,i=Object.create(o.prototype),a=new k(n||[]);return i._invoke=function(e,t,r){var n="suspendedStart";return function(o,i){if("executing"===n)throw new Error("Generator is already running");if("completed"===n){if("throw"===o)throw i;return{value:void 0,done:!0}}for(r.method=o,r.arg=i;;){var a=r.delegate;if(a){var s=w(a,r);if(s){if(s===l)continue;return s}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if("suspendedStart"===n)throw n="completed",r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n="executing";var u=c(e,t,r);if("normal"===u.type){if(n=r.done?"completed":"suspendedYield",u.arg===l)continue;return{value:u.arg,done:r.done}}"throw"===u.type&&(n="completed",r.method="throw",r.arg=u.arg)}}}(e,r,a),i}function c(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}e.wrap=u;var l={};function f(){}function p(){}function h(){}var y={};s(y,o,(function(){return this}));var d=Object.getPrototypeOf,v=d&&d(d(O([])));v&&v!==t&&r.call(v,o)&&(y=v);var g=h.prototype=f.prototype=Object.create(y);function m(e){["next","throw","return"].forEach((function(t){s(e,t,(function(e){return this._invoke(t,e)}))}))}function b(e,t){function n(o,i,a,s){var u=c(e[o],e,i);if("throw"!==u.type){var l=u.arg,f=l.value;return f&&"object"==oe(f)&&r.call(f,"__await")?t.resolve(f.__await).then((function(e){n("next",e,a,s)}),(function(e){n("throw",e,a,s)})):t.resolve(f).then((function(e){l.value=e,a(l)}),(function(e){return n("throw",e,a,s)}))}s(u.arg)}var o;this._invoke=function(e,r){function i(){return new t((function(t,o){n(e,r,t,o)}))}return o=o?o.then(i,i):i()}}function w(e,t){var r=e.iterator[t.method];if(void 0===r){if(t.delegate=null,"throw"===t.method){if(e.iterator.return&&(t.method="return",t.arg=void 0,w(e,t),"throw"===t.method))return l;t.method="throw",t.arg=new TypeError("The iterator does not provide a 'throw' method")}return l}var n=c(r,e.iterator,t.arg);if("throw"===n.type)return t.method="throw",t.arg=n.arg,t.delegate=null,l;var o=n.arg;return o?o.done?(t[e.resultName]=o.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,l):o:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,l)}function P(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function T(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function k(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(P,this),this.reset(!0)}function O(e){if(e){var t=e[o];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var n=-1,i=function t(){for(;++n<e.length;)if(r.call(e,n))return t.value=e[n],t.done=!1,t;return t.value=void 0,t.done=!0,t};return i.next=i}}return{next:S}}function S(){return{value:void 0,done:!0}}return p.prototype=h,s(g,"constructor",h),s(h,"constructor",p),p.displayName=s(h,a,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===p||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,h):(e.__proto__=h,s(e,a,"GeneratorFunction")),e.prototype=Object.create(g),e},e.awrap=function(e){return{__await:e}},m(b.prototype),s(b.prototype,i,(function(){return this})),e.AsyncIterator=b,e.async=function(t,r,n,o,i){void 0===i&&(i=Promise);var a=new b(u(t,r,n,o),i);return e.isGeneratorFunction(r)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},m(g),s(g,a,"Generator"),s(g,o,(function(){return this})),s(g,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=[];for(var r in e)t.push(r);return t.reverse(),function r(){for(;t.length;){var n=t.pop();if(n in e)return r.value=n,r.done=!1,r}return r.done=!0,r}},e.values=O,k.prototype={constructor:k,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(T),!e)for(var t in this)"t"===t.charAt(0)&&r.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function n(r,n){return a.type="throw",a.arg=e,t.next=r,n&&(t.method="next",t.arg=void 0),!!n}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],a=i.completion;if("root"===i.tryLoc)return n("end");if(i.tryLoc<=this.prev){var s=r.call(i,"catchLoc"),u=r.call(i,"finallyLoc");if(s&&u){if(this.prev<i.catchLoc)return n(i.catchLoc,!0);if(this.prev<i.finallyLoc)return n(i.finallyLoc)}else if(s){if(this.prev<i.catchLoc)return n(i.catchLoc,!0)}else{if(!u)throw new Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return n(i.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=e,a.arg=t,i?(this.method="next",this.next=i.finallyLoc,l):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),l},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),T(r),l}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if("throw"===n.type){var o=n.arg;T(r)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,r){return this.delegate={iterator:O(e),resultName:t,nextLoc:r},"next"===this.method&&(this.arg=void 0),l}},e}function ae(e,t,r,n,o,i,a){try{var s=e[i](a),u=s.value}catch(e){return void r(e)}s.done?t(u):Promise.resolve(u).then(n,o)}function se(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function ue(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function ce(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var le="datapoll",fe="destroySnapshot",pe=function(){function e(t){var r=t.storageHandler;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),ce(this,"list",{}),ce(this,"index",0),ce(this,"atWorks",{}),ce(this,"atLeisures",[]),ce(this,"storageHandler",void 0),ce(this,"systemInfoGetter",void 0),ce(this,"paraStore",{open_id:"",distinct_id:"",union_id:"",user_id:"",initial_id:"",universal_id:"",commProperties:{},commGroupProperties:{}}),ce(this,"isTrySave",!0),ce(this,"trySaveTime",4e3),ce(this,"storeEtag",""),ce(this,"requestHandler",void 0),ce(this,"showLog",void 0),ce(this,"universalIdUrl",void 0),this.storageHandler=r,this.initList()}var t,r,n,o;return t=e,r=[{key:"init",value:function(e){this.systemInfoGetter=e.systemInfoGetter,e.unloadListener.register(this),this.trySave(e),e.autoTrack.appHide&&this.checkDestroySnapshot(),this.requestHandler=e.requestHandler,this.showLog=e.showLog,this.paraStore=e.store,this.universalIdUrl=e.universalIdUrl}},{key:"trySave",value:function(e){var t=this;setInterval((function(){if(t.isTrySave&&(t.bufferDataPoll(),e.autoTrack.appHide&&t.destroySnapshot(e)),t.atLeisures.length>100&&t.requestHandler){var r=t.getList(),n=r.keys,o=r.list;t.showLog&&console.log("上报数据",o);var i=t.requestHandler.send(o);i&&i.then((function(){t.remove(n)})).catch((function(e){t.reset(n),t.showLog&&console.log("上报失败",e)}))}}),this.trySaveTime)}},{key:"destroySnapshot",value:function(e){var t=e.getTrackData({eventType:u,key:"$PageLeave",$event_duration:this.systemInfoGetter?this.systemInfoGetter.getPageDurationTime():0}),r=e.getTrackData({key:l,eventType:l,$event_duration:this.systemInfoGetter?this.systemInfoGetter.getTimestamp()-this.systemInfoGetter.getLaunchTime():0});this.storageHandler.set(fe,[t,r])}},{key:"checkDestroySnapshot",value:function(){var e=this.storageHandler.get(fe);e&&this.append(e)}},{key:"initList",value:function(){var e=this.storageHandler.get(le);e&&(this.list=e.list,this.index=e.index,this.atWorks=e.atWorks,this.atLeisures=e.atLeisures)}},{key:"onUnload",value:function(){this.remove(Object.keys(this.atWorks)),this.bufferDataPoll(),this.storageHandler.remove(fe)}},{key:"bufferDataPoll",value:function(){var e=this.getEtag();e!=this.storeEtag&&(this.storageHandler.set(le,{list:this.list,index:this.index,atWorks:this.atWorks,atLeisures:this.atLeisures}),this.storeEtag=e)}},{key:"getEtag",value:function(){var e="";for(var t in this.atWorks)e+=t+",";return e+="|",this.atLeisures.forEach((function(t){return e+=t+","})),e}},{key:"append",value:function(e){if(!(Object.keys(this.list).length>1e3)){var t,r=function(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=function(e,t){if(e){if("string"==typeof e)return se(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?se(e,t):void 0}}(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var n=0,o=function(){};return{s:o,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,a=!0,s=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return a=e.done,e},e:function(e){s=!0,i=e},f:function(){try{a||null==r.return||r.return()}finally{if(s)throw i}}}}(Array.isArray(e)?e:[e]);try{for(r.s();!(t=r.n()).done;){var n=t.value;this.list[this.index]=n,this.atLeisures.push(this.index),this.index++}}catch(e){r.e(e)}finally{r.f()}}}},{key:"getList",value:function(){for(var e={keys:[],list:[]},t=0;t<100;t++){var r=this.atLeisures.shift();if(void 0===r)break;e.keys.push(r),e.list.push(this.list[r]),this.atWorks[r]=1}return e}},{key:"remove",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];t.forEach((function(t){delete e.atWorks[t],delete e.list[t]}))}},{key:"reset",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];t.forEach((function(t){delete e.atWorks[t],e.list[t]&&e.atLeisures.unshift(Number(t))}))}},{key:"modifyAny",value:function(e){for(var t in this.list){var r=this.list[t];this.systemInfoGetter&&r.event_millitime>=this.systemInfoGetter.getLaunchTime()&&e(r)}}},{key:"modifyDisctincId",value:function(e){for(var t in this.list){var r=this.list[t];r.distinct_id=e,r.open_id=e}}},{key:"modifyDisctincUnionId",value:function(e){var t=e.openid,r=e.unionid;for(var n in this.list){var o=this.list[n];r&&(o.distinct_id=r),o.open_id=t,o.union_id=r}}},{key:"modifyUserId",value:function(e){for(var t in this.list)this.list[t].user_id=e}},{key:"updateUniversalId",value:(n=ie().mark((function e(t,r){var n,o,i,a,s,u,c,l,f;return ie().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(e.prev=0,o=this.storageHandler.get(ee)||{},i=o.universal_id,a=null===(n=this.systemInfoGetter)||void 0===n?void 0:n.getTerminial(),this.universalIdUrl&&a&&t){e.next=5;break}return e.abrupt("return",i);case 5:if(i&&!r){e.next=14;break}return s={channel_type:a,channel_user_id:t},r&&(s.user_id=Number(r)),e.next=10,this.requestHandler.getUniversalId(this.universalIdUrl,s);case 10:u=e.sent,c=u.data.data.universal_id,this.paraStore.universal_id=c,i=c;case 14:for(l in this.list)(f=this.list[l]).universal_id&&"$Login"!==f.event_name||(f.universal_id=i);return e.abrupt("return",i);case 18:e.prev=18,e.t0=e.catch(0),console.error(e.t0);case 21:case"end":return e.stop()}}),e,this,[[0,18]])})),o=function(){var e=this,t=arguments;return new Promise((function(r,o){var i=n.apply(e,t);function a(e){ae(i,r,o,a,s,"next",e)}function s(e){ae(i,r,o,a,s,"throw",e)}a(void 0)}))},function(e,t){return o.apply(this,arguments)})}],r&&ue(t.prototype,r),Object.defineProperty(t,"prototype",{writable:!1}),e}();function he(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function ye(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var de=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),ye(this,"oldPage",void 0),ye(this,"oldApp",void 0),ye(this,"oldComponent",void 0),ye(this,"isAlreadyInit",!1)}var t,r;return t=e,r=[{key:"init",value:function(e,t,r){if(!this.isAlreadyInit){var n=this;this.oldPage=Page,this.oldApp=App,this.oldComponent=Component,Page=function(t){return e.notify(t),n.oldPage(t)},App=function(e){return t.notify(e),n.oldApp(e)},Component=function(e){return r.notify(e),n.oldComponent(e)},this.isAlreadyInit=!0}}}],r&&he(t.prototype,r),Object.defineProperty(t,"prototype",{writable:!1}),e}();const ve=new de;function ge(e){return ge="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},ge(e)}function me(){return me="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(e,t,r){var n=be(e,t);if(n){var o=Object.getOwnPropertyDescriptor(n,t);return o.get?o.get.call(arguments.length<3?e:r):o.value}},me.apply(this,arguments)}function be(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=ke(e)););return e}function we(e,t){return we=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},we(e,t)}function Pe(e,t){if(t&&("object"===ge(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return Te(e)}function Te(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function ke(e){return ke=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},ke(e)}function Oe(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function Se(e,t,r){return t&&Oe(e.prototype,t),r&&Oe(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function _e(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Le(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var Ae=Se((function e(){_e(this,e),Le(this,"distinct_id",""),Le(this,"user_id",""),Le(this,"open_id",""),Le(this,"initial_id",""),Le(this,"commProperties",{}),Le(this,"commGroupProperties",{}),Le(this,"sourceDistinctId",void 0),Le(this,"latestTrafficSourceType",void 0)})),Ce=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&we(e,t)}(o,e);var t,r,n=(t=o,r=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}(),function(){var e,n=ke(t);if(r){var o=ke(this).constructor;e=Reflect.construct(n,arguments,o)}else e=n.apply(this,arguments);return Pe(this,e)});function o(e){var t;return _e(this,o),Le(Te(t=n.call(this,e)),"sysPageListener",void 0),Le(Te(t),"sysAppListener",void 0),Le(Te(t),"sysComponentListener",void 0),Le(Te(t),"scrollListener",void 0),Le(Te(t),"sourceListener",void 0),Le(Te(t),"tabItemListener",void 0),Le(Te(t),"store",new Ae),t.sysPageListener=e.sysPageListener,t.sysAppListener=e.sysAppListener,t.sysComponentListener=e.sysComponentListener,t.scrollListener=e.scrollListener,t.sourceListener=e.sourceListener,t.tabItemListener=e.tabItemListener,t}return Se(o,[{key:"init",value:function(e){ve.init(this.sysPageListener,this.sysAppListener,this.sysComponentListener),me(ke(o.prototype),"init",this).call(this,e),this.scrollListener.init(this),this.sourceListener.init(this),this.tabItemListener.init(this)}},{key:"getTrackData",value:function(e){var t=me(ke(o.prototype),"getTrackData",this).call(this,e),r=this.systemInfoGetter;t.lib.$model=r.getModel(),t.lib.$app_id=r.getAppId(),t.lib.$pixel_ratio=r.getPixelRatio(),t.lib.$language=r.getLanguage(),t.lib.$app_version=r.getAppVersion(),t.lib.$storage=r.getStorage(),t.lib.$current_battery=r.getCurrentBattery(),t.lib.$system_version=r.getSystemVersion(),t.lib.$system_platform=r.getSystemPlatform(),t.lib.$brand=r.getBrand(),t.lib.$font_size_setting=r.getFontSizeSetting(),t.lib.$version_code=r.getVersionCode();var n=r.getMiniVersion();n&&(t.lib.$mini_version=n);var i=r.getNetworkType();return i&&(t.lib.$network_type=i),t.properties.$is_visit_from_outside=r.getVisitFromOutside(),t.properties.$referrer_app_id=r.getReferrerAppId(),t.properties.$is_collected=r.getIsCollected(),t.properties.page_list=r.getPageList(),t.properties.position_list=r.getPositionList(),t.properties.index_module_key_list=r.getIndexModuleKeyList(),t.properties.module_position_sort_list=r.getModulePositionSortList(),t}}]),o}(ne);function xe(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function Ee(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var Ie=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),Ee(this,"observers",[]),Ee(this,"debounceNotify",void 0)}var t,r;return t=e,(r=[{key:"onScrollChange",value:function(){this.debounceNotify&&this.debounceNotify()}},{key:"init",value:function(e){this.debounceNotify=M(this.notify,e.scrollDelayTime,this),this.resetInit(),e.scrollListener.register(this)}},{key:"register",value:function(e){this.observers.push(e)}},{key:"remove",value:function(e){var t=this.observers.findIndex((function(t){return t===e}));-1!=t&&this.observers.splice(t,1)}},{key:"notify",value:function(){this.observers.forEach((function(e){return e.onStayChange()}))}},{key:"resetInit",value:function(){this.debounceNotify&&this.debounceNotify()}}])&&xe(t.prototype,r),Object.defineProperty(t,"prototype",{writable:!1}),e}();function je(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function Be(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var Re=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),Be(this,"serverUrl",""),Be(this,"dataSendTimeout",3e3)}var t,r;return t=e,(r=[{key:"encryption",value:function(e){return function(e){var t,r,n,o,i,a="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",s=0,u=0,c="",l=[];if(!e)return e;e=function(e){var t,r,n,o,i="";for(t=r=0,o=(e=(e+"").replace(/\r\n/g,"\n").replace(/\r/g,"\n")).length,n=0;n<o;n++){var a=e.charCodeAt(n),s=null;a<128?r++:s=a>127&&a<2048?String.fromCharCode(a>>6|192,63&a|128):String.fromCharCode(a>>12|224,a>>6&63|128,63&a|128),null!==s&&(r>t&&(i+=e.substring(t,r)),i+=s,t=r=n+1)}return r>t&&(i+=e.substring(t,e.length)),i}(e);do{t=(i=e.charCodeAt(s++)<<16|e.charCodeAt(s++)<<8|e.charCodeAt(s++))>>18&63,r=i>>12&63,n=i>>6&63,o=63&i,l[u++]=a.charAt(t)+a.charAt(r)+a.charAt(n)+a.charAt(o)}while(s<e.length);switch(c=l.join(""),e.length%3){case 1:c=c.slice(0,-2)+"==";break;case 2:c=c.slice(0,-1)+"="}return c}(encodeURIComponent(JSON.stringify(e)))}},{key:"send",value:function(e){return this.sendRequest(this.serverUrl,e)}},{key:"sendSing",value:function(e){return this.send([e])}},{key:"sendRequest",value:function(e,t){var r=this,n=this.encryption(t);return e=$(e,{},{data:n}),new Promise((function(t,o){my.request({url:e,method:"POST",data:{data:n},headers:{"content-type":"application/x-www-form-urlencoded"},timeout:r.dataSendTimeout,success:function(e){t(e)},fail:function(e){o(e)}})}))}},{key:"getUniversalId",value:function(e,t){var r=this.encryption(t);return e=$(e,{},{data:r}),new Promise((function(t,n){my.request({url:e,method:"POST",data:{data:r},headers:{"content-type":"application/x-www-form-urlencoded"},success:function(e){return t(e)},fail:function(e){return n(e)}})}))}}])&&je(t.prototype,r),Object.defineProperty(t,"prototype",{writable:!1}),e}();function De(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function Me(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?De(Object(r),!0).forEach((function(t){Ge(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):De(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function Ue(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function Ge(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var Ne={1e3:"首页十二宫格及更多",1002:"小程序收藏应用入口",1005:"顶部搜索框的搜索结果页",1007:"单人聊天会话中的小程序消息卡片（分享）",1011:"扫描二维码",1014:"小程序模版消息（服务提醒）",1020:"生活号 profile 页相关小程序列表",1023:"系统桌面图标",1037:"小程序打开小程序",1038:"从另一个小程序返回",1200:"市民中心（原城市服务频道）",1201:"芝麻信用频道",1202:"出行(原车主服务频道)",1209:"支付宝会员频道",1300:"第三方APP打开",1400:"付费流量(通过商家数字推广平台，灯火等投放的广告)",1401:"卡包",1402:"支付宝-我的",1403:"支付成功页","0000":"其他渠道场景渠道。"},He=function(){function e(){var t=this;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),Ge(this,"appId",void 0),Ge(this,"model",void 0),Ge(this,"pixelRatio",void 0),Ge(this,"windowWidth",void 0),Ge(this,"windowHeight",void 0),Ge(this,"language",void 0),Ge(this,"version",void 0),Ge(this,"storage",void 0),Ge(this,"currentBattery",void 0),Ge(this,"system",void 0),Ge(this,"platform",void 0),Ge(this,"screenHeight",void 0),Ge(this,"brand",void 0),Ge(this,"fontSizeSetting",void 0),Ge(this,"scrollTop",void 0),Ge(this,"scrollHeight",void 0),Ge(this,"referrerAppId",void 0),Ge(this,"isCollected",0),Ge(this,"versionCode",void 0),Ge(this,"app",void 0),Ge(this,"runScene",void 0),Ge(this,"networkType",void 0),Ge(this,"sourceChannel",void 0),Ge(this,"visitFromOutside",void 0),Ge(this,"dataPoll",void 0),Ge(this,"isUrlJustUpdate",!1),Ge(this,"currentUrl",""),Ge(this,"preUrl",void 0),Ge(this,"tempUrl",void 0),Ge(this,"launchTime",Date.now()),Ge(this,"_detainedTime",void 0),Ge(this,"_duration_time",0),Ge(this,"enterPageTime",0),Ge(this,"launchQuery",{}),Ge(this,"onLoadQuery",{}),Ge(this,"pageTitleConfig",{}),Ge(this,"timeGap",0),Ge(this,"timer",null);var r=my.getSystemInfoSync();this.model=r.model,this.pixelRatio=r.pixelRatio,this.windowWidth=r.windowWidth,this.windowHeight=r.windowHeight,this.language=r.language,this.storage=r.storage,this.currentBattery=r.currentBattery,this.system=r.system,this.platform=r.platform,this.screenHeight=r.screenHeight,this.brand=r.brand,this.fontSizeSetting=r.fontSizeSetting,this.app=r.app,this._detainedTime=Date.now(),this.versionCode=r.version;try{if(function(e,t){for(var r=my.SDKVersion.split("."),n="2.7.17".split("."),o=Math.max(r.length,n.length),i=0;i<o;i++){var a=parseInt(r[i]||"0"),s=parseInt(n[i]||"0");if(a>s)return 1;if(a<s)return-1}return 0}()>=0){var n=my.getAccountInfoSync();this.appId=n.miniProgram.appId,this.runScene=n.miniProgram.envVersion,this.version=n.miniProgram.version}else this.appId=my.getAppIdSync().appId,my.getRunScene({success:function(e){t.runScene=e.envVersion,t.dataPoll&&t.dataPoll.modifyAny((function(e){e.lib.$mini_version=t.runScene}))}})}catch(e){console.log("基础库 2.7.17 及以上版本 才能使用 getAccountInfoSync, 1.20 支持getAppIdSync")}my.getNetworkType({success:function(e){t.networkType=e.networkType,t.dataPoll&&t.dataPoll.modifyAny((function(e){e.lib.$network_type=t.networkType}))}}),my.onAppShow((function(e){e.scene&&(t.sourceChannel=Ne[e.scene]||e.scene,t.visitFromOutside=!(1037!=e.scene&&1038!=e.scene),t.dataPoll&&t.dataPoll.modifyAny((function(e){e.properties.$latest_traffic_source_type=t.sourceChannel,e.properties.$is_visit_from_outside=t.visitFromOutside})))})),this.fetchCollected(),this.timer=setInterval((function(){t.fetchCollected()}),6e5),this.getServiceTime().then((function(e){t.timeGap=Date.now()-e}))}var t,r;return t=e,r=[{key:"init",value:function(e){e.launchListener&&e.launchListener.register(this),e.pageLoadListener.register(this),e.scrollListener.register(this),e.pageListener.register(this),this.dataPoll=e.dataPoll,this.pageTitleConfig=e.pageTitleConfig}},{key:"getLaunchTime",value:function(){return this.launchTime}},{key:"getTimestamp",value:function(){return Date.now()-this.timeGap}},{key:"onBeforeLaunch",value:function(e){e.referrerInfo&&e.referrerInfo.appId&&(this.referrerAppId=e.referrerInfo.appId),e.query&&(this.launchQuery=e.query)}},{key:"onLaunch",value:function(){this.launchTime=this.getTimestamp()}},{key:"onBeforePageLoad",value:function(e){this.onLoadQuery=e,this.currentUrl=this.getCurrentUrl(),this.isUrlJustUpdate=!0;var t=this.onLoadQuery.statistical_from;t&&"undefined"!==t&&(this.launchQuery.statistical_from=t)}},{key:"generateRandomId",value:function(){return R()}},{key:"getTerminial",value:function(){return{alipay:d,DINGTALK:"dingtalk.mini"}[this.app]||d}},{key:"getAppId",value:function(){return this.appId||""}},{key:"getIsCollected",value:function(){return this.isCollected}},{key:"getModel",value:function(){return this.model}},{key:"getPixelRatio",value:function(){return this.pixelRatio}},{key:"getLanguage",value:function(){return this.language}},{key:"getAppVersion",value:function(){return this.version}},{key:"getStorage",value:function(){return this.storage}},{key:"getCurrentBattery",value:function(){return this.currentBattery}},{key:"getSystemVersion",value:function(){return this.system}},{key:"getSystemPlatform",value:function(){return this.platform}},{key:"getBrand",value:function(){return this.brand}},{key:"getFontSizeSetting",value:function(){return this.fontSizeSetting}},{key:"getLib",value:function(){return"alipay.mini"}},{key:"getViewportWidth",value:function(){return this.windowWidth||0}},{key:"getViewportHeight",value:function(){return this.windowHeight||0}},{key:"getScreenHeight",value:function(){return this.scrollHeight||this.screenHeight||0}},{key:"getScrollTop",value:function(){return this.scrollTop||0}},{key:"getTitle",value:function(){var e=this.getUrl();if(this.pageTitleConfig[e]||this.pageTitleConfig.defaultTitle)return this.pageTitleConfig[e]||this.pageTitleConfig.defaultTitle}},{key:"getReferrer",value:function(){return this.preUrl||""}},{key:"getReferrerAppId",value:function(){return this.referrerAppId}},{key:"getReportIncident",value:function(e,t){var r,n,o,i,s,u,c,l={tap:a,firstAppear:h,appear:h,touchEnd:y}[e.type];if(l===a){n=(r=Me(Me(Me({},e.target.targetDataset||{}),e.target.dataset||{}),e.currentTarget.dataset||{})).sensors,o=e.currentTarget.tagName;var f=Object.keys(r).sort().map((function(e){return void 0===r[e]||/[\u4e00-\u9fa5]/.test(r[e])?e:/^\d+\.?\d*$/.test(r[e])?Number(r[e])>1e3||/\d+\.\d+/.test(r[e])?e:"".concat(e,"=").concat(r[e]):B(r[e])||/[a-z\d]{10,}/.test(r[e])?e:"".concat(e,"=").concat(r[e])})).filter((function(e){return!!e})).join("&");s=K(r,"position_sign"),u=K(r,"index_module_key",!0),c=K(r,"module_position_sort");var p=getCurrentPages().slice(-1)[0]||{};p.$lastPositionSign=s||"",p.$lastIndexModuleKey=u||"",p.$lastModulePositionSort=c||"",i="".concat(this.getTerminial(),"_").concat(this.getUrl(),"_").concat(t,"_").concat(e.currentTarget.tagName).concat(f?"_"+f:"").concat(e.currentTarget.id?"_"+e.currentTarget.id:"").concat(s?"_"+s:"")}else if(l===y){if(e.detail&&"autoplay"===e.detail.source)return;n=(r=e.currentTarget.dataset).sensorsTouch||r.sensorstouch,o=e.currentTarget.tagName}else l===h&&(r=e.currentTarget.dataset,n=e.currentTarget.dataset.sensorsExposure||h,o=e.currentTarget.tagName);if(r){var d=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t={};for(var r in e){var n=/sensors_.+/.exec(r);if(n){var o=n[0].slice(8),i=F(o),a=e[r];T.includes(o)?q(t,r,a):t[i]=a}}return t}(r);s&&(d.positionSignId=s),u&&(d.indexModuleKey=u),c&&(d.modulePositionSort=c);var v={eventType:l,key:n||l,$element_type:o,$element_path:i,customProperties:d};return l===a&&(v.$page_x=e.detail.pageX,v.$page_y=e.detail.pageY,v.$client_x=e.target.offsetLeft,v.$client_y=e.target.offsetTop),l===h&&e.disappearTimeStamp&&(v.$appear_duration=e.disappearTimeStamp-e.timeStamp),v}}},{key:"getMiniVersion",value:function(){return this.runScene}},{key:"getNetworkType",value:function(){return this.networkType}},{key:"getLatestTrafficSourceType",value:function(){return this.sourceChannel||""}},{key:"getLatestReferrer",value:function(){return""}},{key:"getVisitFromOutside",value:function(){return this.visitFromOutside}},{key:"getUrl",value:function(){return this.currentUrl}},{key:"getUrlPath",value:function(){return this.getUrl()}},{key:"getCurrentUrl",value:function(){try{var e=getCurrentPages();return e[e.length-1]?e[e.length-1].route:"plugin://"}catch(e){return""}}},{key:"getPageDurationTime",value:function(){return this._duration_time}},{key:"onBeforeShow",value:function(){this.isUrlJustUpdate||(this.currentUrl=this.getCurrentUrl()),this.isUrlJustUpdate=!1,this.tempUrl!==this.preUrl&&this.tempUrl!==this.getUrl()&&(this.preUrl=this.tempUrl),this._detainedTime=this.enterPageTime=Date.now(),this._duration_time=0}},{key:"onBeforeHide",value:function(){this.tempUrl=this.getUrl(),this._duration_time=this.enterPageTime?Date.now()-this.enterPageTime:0}},{key:"onScrollChange",value:function(e){this.scrollTop=e.scrollTop,this.scrollHeight=e.scrollHeight;var t=Date.now();t-this._detainedTime<12e4&&(this._duration_time+=t-this._detainedTime),this._detainedTime=t}},{key:"getLaunchQuery",value:function(){return this.launchQuery||{}}},{key:"getOnLoadQuery",value:function(){return this.onLoadQuery||{}}},{key:"getServiceTime",value:function(){return new Promise((function(e,t){my.getServerTime({success:function(t){e(t.time)},fail:function(t){console.warn("获取服务时间失败",t),e(Date.now())}})}))}},{key:"getVersionCode",value:function(){return this.versionCode}},{key:"getPageList",value:function(){return getCurrentPages().map((function(e){return e?e.route:"plugin://"}))}},{key:"getLastList",value:function(e){var t=0,r=getCurrentPages();return r.map((function(n,o){return n&&n[e]?(t=o,n[e]):r[t]&&r[t][e]||""}))}},{key:"getPositionList",value:function(){return this.getLastList("$lastPositionSign")}},{key:"getIndexModuleKeyList",value:function(){return this.getLastList("$lastIndexModuleKey")}},{key:"getModulePositionSortList",value:function(){return this.getLastList("$lastModulePositionSort")}},{key:"fetchCollected",value:function(){var e=this;my.canIUse("isCollected")&&my.isCollected({success:function(t){e.isCollected=t.isCollected?1:0}})}}],r&&Ue(t.prototype,r),Object.defineProperty(t,"prototype",{writable:!1}),e}();function $e(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}var Fe,Ve=function(){function e(){var t,r;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),r=[],(t="observers")in this?Object.defineProperty(this,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):this[t]=r}var t,r;return t=e,(r=[{key:"register",value:function(e){this.observers.push(e)}},{key:"remove",value:function(e){var t=this.observers.findIndex((function(t){return t===e}));-1!=t&&this.observers.splice(t,1)}},{key:"notify",value:function(){console.warn("监听器通知方法需要重构")}}])&&$e(t.prototype,r),Object.defineProperty(t,"prototype",{writable:!1}),e}();function We(e){return We="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},We(e)}function Xe(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Ke(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function qe(e,t){return qe=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},qe(e,t)}function ze(e,t){if(t&&("object"===We(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return Qe(e)}function Qe(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Je(e){return Je=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},Je(e)}function Ze(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}!function(e){e[e.show=0]="show",e[e.hide=1]="hide"}(Fe||(Fe={}));var Ye=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&qe(e,t)}(a,e);var t,r,n,o,i=(n=a,o=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}(),function(){var e,t=Je(n);if(o){var r=Je(this).constructor;e=Reflect.construct(t,arguments,r)}else e=t.apply(this,arguments);return ze(this,e)});function a(){var e;Xe(this,a);for(var t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];return Ze(Qe(e=i.call.apply(i,[this].concat(r))),"isAlreadyHide",!1),e}return t=a,r=[{key:"onPage",value:function(e){this.listenerPageOnShow(e),this.listenerPageOnHide(e)}},{key:"init",value:function(e){e.sysPageListener.register(this),e.pageUnloadListener.register(this)}},{key:"listenerPageOnShow",value:function(e){var t=e.onShow||function(){},r=this;e.onShow=function(){try{r.notify(Fe.show),r.isAlreadyHide=!1}catch(e){console.warn("AlipayMiniPageListener -> listenerPageOnShow",e)}t.call.apply(t,[this].concat(Array.prototype.slice.call(arguments)))}}},{key:"listenerPageOnHide",value:function(e){var t=e.onHide||function(){},r=this;e.onHide=function(){try{r.isAlreadyHide||(r.notify(Fe.hide),r.isAlreadyHide=!0)}catch(e){console.warn("AlipayMiniPageListener -> listenerPageOnHide"+e)}t.call.apply(t,[this].concat(Array.prototype.slice.call(arguments)))}}},{key:"onPageUnload",value:function(){this.isAlreadyHide||(this.notify(Fe.hide),this.isAlreadyHide=!0)}},{key:"notify",value:function(e){this.observers.forEach((function(t){e===Fe.show&&t.onBeforeShow&&t.onBeforeShow(),e===Fe.hide&&t.onBeforeHide&&t.onBeforeHide()})),this.observers.forEach((function(t){e===Fe.show&&t.onPageShow&&t.onPageShow(),e===Fe.hide&&t.onPageHide&&t.onPageHide()})),this.observers.forEach((function(t){e===Fe.show&&t.onAfterPageShow&&t.onAfterPageShow(),e===Fe.hide&&t.onAfterPageHide&&t.onAfterPageHide()}))}}],r&&Ke(t.prototype,r),Object.defineProperty(t,"prototype",{writable:!1}),a}(Ve);function et(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}var rt=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e)}var t,r;return t=e,(r=[{key:"get",value:function(e){return my.getStorageSync({key:e}).data}},{key:"set",value:function(e,t){my.setStorageSync({key:e,data:t})}},{key:"remove",value:function(e){my.removeStorageSync({key:e})}}])&&et(t.prototype,r),Object.defineProperty(t,"prototype",{writable:!1}),e}();function nt(e){return nt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},nt(e)}function ot(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function it(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function at(e,t){return at=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},at(e,t)}function st(e,t){if(t&&("object"===nt(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return ut(e)}function ut(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function ct(e){return ct=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},ct(e)}function lt(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var ft={onLoad:1,onShow:1,onHide:1,onReady:1,onUnload:1,onTitleClick:1,onPullDownRefresh:1,onReachBottom:1,onPageScroll:1,onResize:1,onTabItemTap:1,onOptionMenuClick:1,onPopMenuClick:1,onPullIntercept:1,onAddToFavorites:1,onShareAppMessage:1,onShareTimeline:1,eventHandler:1,data:1},pt=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&at(e,t)}(a,e);var t,r,n,o,i=(n=a,o=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}(),function(){var e,t=ct(n);if(o){var r=ct(this).constructor;e=Reflect.construct(t,arguments,r)}else e=t.apply(this,arguments);return st(this,e)});function a(){var e;ot(this,a);for(var t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];return lt(ut(e=i.call.apply(i,[this].concat(r))),"observers",[]),e}return t=a,r=[{key:"onPage",value:function(e){this.listenerCustomMethod(e)}},{key:"onComponent",value:function(e){var t=this;e.methods&&function(){var r=t,n=function(t){if(!U(e.methods[t]))return"continue";var n=e.methods[t];e.methods[t]=function(){try{arguments[0]&&"tap"===arguments[0].type&&r.notify(arguments[0],t)}catch(e){console.warn(e)}return n.call.apply(n,[this].concat(Array.prototype.slice.call(arguments)))}};for(var o in e.methods)n(o)}()}},{key:"init",value:function(e){e.sysPageListener.register(this),e.sysComponentListener.register(this)}},{key:"listenerCustomMethod",value:function(e){var t=this,r=function(r){if(ft[r]||!U(e[r]))return"continue";var n=e[r];e[r]=function(){try{arguments[0]&&"tap"===arguments[0].type&&t.notify(arguments[0],r)}catch(e){console.warn("AlipayMiniClickListener -> init",e)}return n.call.apply(n,[this].concat(Array.prototype.slice.call(arguments)))}};for(var n in e)r(n)}},{key:"notify",value:function(e,t){this.observers.forEach((function(r){return r.onClickChange(e,t)}))}}],r&&it(t.prototype,r),Object.defineProperty(t,"prototype",{writable:!1}),a}(Ve);function ht(e){return ht="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},ht(e)}function yt(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function dt(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function vt(e,t){return vt=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},vt(e,t)}function gt(e,t){if(t&&("object"===ht(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}function mt(e){return mt=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},mt(e)}var bt=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&vt(e,t)}(a,e);var t,r,n,o,i=(n=a,o=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}(),function(){var e,t=mt(n);if(o){var r=mt(this).constructor;e=Reflect.construct(t,arguments,r)}else e=t.apply(this,arguments);return gt(this,e)});function a(){return yt(this,a),i.apply(this,arguments)}return t=a,(r=[{key:"init",value:function(e){var t=this;my.onAppHide((function(){t.notify()}))}},{key:"notify",value:function(){this.observers.forEach((function(e){return e.onUnload()}))}}])&&dt(t.prototype,r),Object.defineProperty(t,"prototype",{writable:!1}),a}(Ve);function wt(e){return wt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},wt(e)}function Pt(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Tt(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function kt(e,t){return kt=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},kt(e,t)}function Ot(e,t){if(t&&("object"===wt(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}function St(e){return St=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},St(e)}var _t=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&kt(e,t)}(a,e);var t,r,n,o,i=(n=a,o=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}(),function(){var e,t=St(n);if(o){var r=St(this).constructor;e=Reflect.construct(t,arguments,r)}else e=t.apply(this,arguments);return Ot(this,e)});function a(){return Pt(this,a),i.apply(this,arguments)}return t=a,(r=[{key:"notify",value:function(e){this.observers.forEach((function(t){return t.onApp(e)}))}}])&&Tt(t.prototype,r),Object.defineProperty(t,"prototype",{writable:!1}),a}(Ve);function Lt(e){return Lt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Lt(e)}function At(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Ct(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function xt(e,t){return xt=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},xt(e,t)}function Et(e,t){if(t&&("object"===Lt(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}function It(e){return It=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},It(e)}var jt=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&xt(e,t)}(a,e);var t,r,n,o,i=(n=a,o=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}(),function(){var e,t=It(n);if(o){var r=It(this).constructor;e=Reflect.construct(t,arguments,r)}else e=t.apply(this,arguments);return Et(this,e)});function a(){return At(this,a),i.apply(this,arguments)}return t=a,(r=[{key:"notify",value:function(e){this.observers.forEach((function(t){return t.onPage(e)}))}}])&&Ct(t.prototype,r),Object.defineProperty(t,"prototype",{writable:!1}),a}(Ve);function Bt(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}var Rt=function(){function e(){var t,r;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),r=[],(t="observers")in this?Object.defineProperty(this,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):this[t]=r}var t,r;return t=e,(r=[{key:"register",value:function(e){this.observers.push(e)}},{key:"remove",value:function(e){var t=this.observers.findIndex((function(t){return t===e}));-1!=t&&this.observers.splice(t,1)}},{key:"notify",value:function(e){this.observers.forEach((function(t){return t.onComponent(e)}))}}])&&Bt(t.prototype,r),Object.defineProperty(t,"prototype",{writable:!1}),e}();function Dt(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}var Mt=function(){function e(){var t,r;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),r=[],(t="observers")in this?Object.defineProperty(this,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):this[t]=r}var t,r;return t=e,r=[{key:"onPage",value:function(e){this.listenerOnPageScroll(e)}},{key:"init",value:function(e){e.sysPageListener.register(this)}},{key:"register",value:function(e){this.observers.push(e)}},{key:"remove",value:function(e){var t=this.observers.findIndex((function(t){return t===e}));-1!=t&&this.observers.splice(t,1)}},{key:"notify",value:function(e){this.observers.forEach((function(t){return t.onScrollChange(e)}))}},{key:"listenerOnPageScroll",value:function(e){var t=e.onPageScroll||function(){},r=this;e.onPageScroll=function(e){try{r.notify(e)}catch(e){console.warn("AlipayMiniScrollListener -> listenerOnPageScroll",e)}t.call.apply(t,[this].concat(Array.prototype.slice.call(arguments)))}}}],r&&Dt(t.prototype,r),Object.defineProperty(t,"prototype",{writable:!1}),e}();function Ut(e){return Ut="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Ut(e)}function Gt(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Nt(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function Ht(e,t){return Ht=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},Ht(e,t)}function $t(e,t){if(t&&("object"===Ut(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return Ft(e)}function Ft(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Vt(e){return Vt=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},Vt(e)}function Wt(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var Xt=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Ht(e,t)}(a,e);var t,r,n,o,i=(n=a,o=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}(),function(){var e,t=Vt(n);if(o){var r=Vt(this).constructor;e=Reflect.construct(t,arguments,r)}else e=t.apply(this,arguments);return $t(this,e)});function a(){var e;Gt(this,a);for(var t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];return Wt(Ft(e=i.call.apply(i,[this].concat(r))),"observers",[]),e}return t=a,r=[{key:"init",value:function(e){e.sysAppListener.register(this)}},{key:"notify",value:function(e,t){"BEFORE"===e&&this.observers.forEach((function(e){return e.onBeforeLaunch&&e.onBeforeLaunch(t)})),"AFTER"===e&&this.observers.forEach((function(e){return e.onLaunch&&e.onLaunch(t)}))}},{key:"listenerOnLaunch",value:function(e){var t=e.onLaunch||function(){},r=this;e.onLaunch=function(e){r.notify("BEFORE",e),t.call.apply(t,[this].concat(Array.prototype.slice.call(arguments))),r.notify("AFTER",e)}}},{key:"onApp",value:function(e){this.listenerOnLaunch(e)}}],r&&Nt(t.prototype,r),Object.defineProperty(t,"prototype",{writable:!1}),a}(Ve);function Kt(e){return Kt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Kt(e)}function qt(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function zt(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function Qt(e,t){return Qt=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},Qt(e,t)}function Jt(e,t){if(t&&("object"===Kt(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}function Zt(e){return Zt=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},Zt(e)}var Yt=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Qt(e,t)}(a,e);var t,r,n,o,i=(n=a,o=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}(),function(){var e,t=Zt(n);if(o){var r=Zt(this).constructor;e=Reflect.construct(t,arguments,r)}else e=t.apply(this,arguments);return Jt(this,e)});function a(){return qt(this,a),i.apply(this,arguments)}return t=a,r=[{key:"init",value:function(e){e.sysPageListener.register(this)}},{key:"listenerOnShareAppMessage",value:function(e){var t=e.onShareAppMessage||function(){},r=this;e.onShareAppMessage=function(e){var n=t.call.apply(t,[this].concat(Array.prototype.slice.call(arguments)))||{};try{r.notify({options:e,result:n})}catch(e){console.warn("AlipayMiniShareListener -> listenerOnShareAppMessage",e)}return n}}},{key:"onPage",value:function(e){return this.listenerOnShareAppMessage(e)}},{key:"notify",value:function(e){this.observers.forEach((function(t){return t.onShare(e)}))}}],r&&zt(t.prototype,r),Object.defineProperty(t,"prototype",{writable:!1}),a}(Ve);function er(e){return er="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},er(e)}function tr(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function rr(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function nr(e,t){return nr=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},nr(e,t)}function or(e,t){if(t&&("object"===er(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}function ir(e){return ir=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},ir(e)}var ar=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&nr(e,t)}(a,e);var t,r,n,o,i=(n=a,o=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}(),function(){var e,t=ir(n);if(o){var r=ir(this).constructor;e=Reflect.construct(t,arguments,r)}else e=t.apply(this,arguments);return or(this,e)});function a(){return tr(this,a),i.apply(this,arguments)}return t=a,r=[{key:"init",value:function(e){e.sysPageListener.register(this)}},{key:"onPage",value:function(e){this.listenerOnLoad(e)}},{key:"listenerOnLoad",value:function(e){var t=e.onLoad||function(){},r=this;e.onLoad=function(e){try{r.notify(e)}catch(e){console.warn("AlipayMiniPageLoadListener -> listenerPageOnLoad",e)}t.call.apply(t,[this].concat(Array.prototype.slice.call(arguments)))}}},{key:"notify",value:function(e){this.observers.forEach((function(t){return t.onBeforePageLoad&&t.onBeforePageLoad(e)})),this.observers.forEach((function(t){return t.onPageLoad&&t.onPageLoad(e)}))}}],r&&rr(t.prototype,r),Object.defineProperty(t,"prototype",{writable:!1}),a}(Ve);function sr(e){return sr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},sr(e)}function ur(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function cr(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function lr(e,t){return lr=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},lr(e,t)}function fr(e,t){if(t&&("object"===sr(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}function pr(e){return pr=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},pr(e)}var hr=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&lr(e,t)}(a,e);var t,r,n,o,i=(n=a,o=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}(),function(){var e,t=pr(n);if(o){var r=pr(this).constructor;e=Reflect.construct(t,arguments,r)}else e=t.apply(this,arguments);return fr(this,e)});function a(){return ur(this,a),i.apply(this,arguments)}return t=a,(r=[{key:"init",value:function(e){e.pageLoadListener.register(this)}},{key:"onPageLoad",value:function(e){e&&e.distinctId&&this.notify({id:e.distinctId,latestTrafficSourceType:e.sourceTerminal,latestReferrer:e.item_view_before})}},{key:"notify",value:function(e){this.observers.forEach((function(t){return t.onSource(e)}))}}])&&cr(t.prototype,r),Object.defineProperty(t,"prototype",{writable:!1}),a}(Ve);function yr(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}var dr=function(){function e(){var t,r;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),r=void 0,(t="para")in this?Object.defineProperty(this,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):this.para=r,this.para=null}var t,r;return t=e,(r=[{key:"track",value:function(e){}},{key:"init",value:function(e){}},{key:"login",value:function(e){}},{key:"identify",value:function(e){}},{key:"appendCommProperties",value:function(e){}},{key:"replaceCommProperties",value:function(e,t){}},{key:"removeCommProperties",value:function(){}},{key:"removeCommPropertiesGroup",value:function(){}},{key:"appendPageProperties",value:function(e){}},{key:"removePageProperties",value:function(){}},{key:"appendCommPropertiesToPage",value:function(){}},{key:"appendCycleProperties",value:function(e){}},{key:"removeCycleProperties",value:function(){}}])&&yr(t.prototype,r),Object.defineProperty(t,"prototype",{writable:!1}),e}();function vr(e){return vr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},vr(e)}function gr(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function mr(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function br(e,t){return br=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},br(e,t)}function wr(e,t){if(t&&("object"===vr(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}function Pr(e){return Pr=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},Pr(e)}var Tr=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&br(e,t)}(a,e);var t,r,n,o,i=(n=a,o=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}(),function(){var e,t=Pr(n);if(o){var r=Pr(this).constructor;e=Reflect.construct(t,arguments,r)}else e=t.apply(this,arguments);return wr(this,e)});function a(){return gr(this,a),i.apply(this,arguments)}return t=a,r=[{key:"init",value:function(e){e.sysPageListener.register(this),e.sysComponentListener.register(this)}},{key:"onPage",value:function(e){this.listenerPageExposure(e)}},{key:"onComponent",value:function(e){this.listenerComponentExposure(e)}},{key:"listenerPageExposure",value:function(e){var t=e.onFirstAppear||function(){},r=this;e.onFirstAppear=function(e){try{r.notify(e)}catch(e){console.warn("AlipayMiniExposureListener -> listenerPageExposure",e)}t.call.apply(t,[this].concat(Array.prototype.slice.call(arguments)))}}},{key:"listenerComponentExposure",value:function(e){var t=e.methods||{},r=t.onFirstAppear||function(){},n=this;t.onFirstAppear=function(e){try{n.notify(e)}catch(e){console.warn("AlipayMiniExposureListener -> listenerComponentExposure",e)}r.call.apply(r,[this].concat(Array.prototype.slice.call(arguments)))},e.methods=t}},{key:"notify",value:function(e){this.observers.forEach((function(t){return t.onExposure(e)}))}}],r&&mr(t.prototype,r),Object.defineProperty(t,"prototype",{writable:!1}),a}(Ve);function kr(e){return kr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},kr(e)}function Or(){Or=function(){return e};var e={},t=Object.prototype,r=t.hasOwnProperty,n="function"==typeof Symbol?Symbol:{},o=n.iterator||"@@iterator",i=n.asyncIterator||"@@asyncIterator",a=n.toStringTag||"@@toStringTag";function s(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{s({},"")}catch(e){s=function(e,t,r){return e[t]=r}}function u(e,t,r,n){var o=t&&t.prototype instanceof f?t:f,i=Object.create(o.prototype),a=new k(n||[]);return i._invoke=function(e,t,r){var n="suspendedStart";return function(o,i){if("executing"===n)throw new Error("Generator is already running");if("completed"===n){if("throw"===o)throw i;return{value:void 0,done:!0}}for(r.method=o,r.arg=i;;){var a=r.delegate;if(a){var s=w(a,r);if(s){if(s===l)continue;return s}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if("suspendedStart"===n)throw n="completed",r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n="executing";var u=c(e,t,r);if("normal"===u.type){if(n=r.done?"completed":"suspendedYield",u.arg===l)continue;return{value:u.arg,done:r.done}}"throw"===u.type&&(n="completed",r.method="throw",r.arg=u.arg)}}}(e,r,a),i}function c(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}e.wrap=u;var l={};function f(){}function p(){}function h(){}var y={};s(y,o,(function(){return this}));var d=Object.getPrototypeOf,v=d&&d(d(O([])));v&&v!==t&&r.call(v,o)&&(y=v);var g=h.prototype=f.prototype=Object.create(y);function m(e){["next","throw","return"].forEach((function(t){s(e,t,(function(e){return this._invoke(t,e)}))}))}function b(e,t){function n(o,i,a,s){var u=c(e[o],e,i);if("throw"!==u.type){var l=u.arg,f=l.value;return f&&"object"==kr(f)&&r.call(f,"__await")?t.resolve(f.__await).then((function(e){n("next",e,a,s)}),(function(e){n("throw",e,a,s)})):t.resolve(f).then((function(e){l.value=e,a(l)}),(function(e){return n("throw",e,a,s)}))}s(u.arg)}var o;this._invoke=function(e,r){function i(){return new t((function(t,o){n(e,r,t,o)}))}return o=o?o.then(i,i):i()}}function w(e,t){var r=e.iterator[t.method];if(void 0===r){if(t.delegate=null,"throw"===t.method){if(e.iterator.return&&(t.method="return",t.arg=void 0,w(e,t),"throw"===t.method))return l;t.method="throw",t.arg=new TypeError("The iterator does not provide a 'throw' method")}return l}var n=c(r,e.iterator,t.arg);if("throw"===n.type)return t.method="throw",t.arg=n.arg,t.delegate=null,l;var o=n.arg;return o?o.done?(t[e.resultName]=o.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,l):o:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,l)}function P(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function T(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function k(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(P,this),this.reset(!0)}function O(e){if(e){var t=e[o];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var n=-1,i=function t(){for(;++n<e.length;)if(r.call(e,n))return t.value=e[n],t.done=!1,t;return t.value=void 0,t.done=!0,t};return i.next=i}}return{next:S}}function S(){return{value:void 0,done:!0}}return p.prototype=h,s(g,"constructor",h),s(h,"constructor",p),p.displayName=s(h,a,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===p||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,h):(e.__proto__=h,s(e,a,"GeneratorFunction")),e.prototype=Object.create(g),e},e.awrap=function(e){return{__await:e}},m(b.prototype),s(b.prototype,i,(function(){return this})),e.AsyncIterator=b,e.async=function(t,r,n,o,i){void 0===i&&(i=Promise);var a=new b(u(t,r,n,o),i);return e.isGeneratorFunction(r)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},m(g),s(g,a,"Generator"),s(g,o,(function(){return this})),s(g,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=[];for(var r in e)t.push(r);return t.reverse(),function r(){for(;t.length;){var n=t.pop();if(n in e)return r.value=n,r.done=!1,r}return r.done=!0,r}},e.values=O,k.prototype={constructor:k,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(T),!e)for(var t in this)"t"===t.charAt(0)&&r.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function n(r,n){return a.type="throw",a.arg=e,t.next=r,n&&(t.method="next",t.arg=void 0),!!n}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],a=i.completion;if("root"===i.tryLoc)return n("end");if(i.tryLoc<=this.prev){var s=r.call(i,"catchLoc"),u=r.call(i,"finallyLoc");if(s&&u){if(this.prev<i.catchLoc)return n(i.catchLoc,!0);if(this.prev<i.finallyLoc)return n(i.finallyLoc)}else if(s){if(this.prev<i.catchLoc)return n(i.catchLoc,!0)}else{if(!u)throw new Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return n(i.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=e,a.arg=t,i?(this.method="next",this.next=i.finallyLoc,l):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),l},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),T(r),l}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if("throw"===n.type){var o=n.arg;T(r)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,r){return this.delegate={iterator:O(e),resultName:t,nextLoc:r},"next"===this.method&&(this.arg=void 0),l}},e}function Sr(e,t,r,n,o,i,a){try{var s=e[i](a),u=s.value}catch(e){return void r(e)}s.done?t(u):Promise.resolve(u).then(n,o)}function _r(e){return function(){var t=this,r=arguments;return new Promise((function(n,o){var i=e.apply(t,r);function a(e){Sr(i,n,o,a,s,"next",e)}function s(e){Sr(i,n,o,a,s,"throw",e)}a(void 0)}))}}function Lr(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=function(e,t){if(e){if("string"==typeof e)return Ar(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Ar(e,t):void 0}}(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var n=0,o=function(){};return{s:o,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,a=!0,s=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return a=e.done,e},e:function(e){s=!0,i=e},f:function(){try{a||null==r.return||r.return()}finally{if(s)throw i}}}}function Ar(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function Cr(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function xr(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var Er=new Map([["2088532636361975",!0],["2088342505212574",!0]]),Ir=D(),jr=function(){function e(t){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),xr(this,"para",void 0),xr(this,"sysAppListener",void 0),xr(this,"dataPoll",void 0),xr(this,"requestHandler",void 0),xr(this,"stayListener",void 0),xr(this,"systemInfoGetter",void 0),xr(this,"send",void 0),xr(this,"_debounceUpload",M(this.upload,500,this)),this.para=t.para,this.dataPoll=t.dataPoll,this.sysAppListener=t.sysAppListener,this.requestHandler=t.requestHandler,t.pageLoadListener.register(this),t.pageListener.register(this),t.clickListener.register(this),t.stayListener.register(this),this.stayListener=t.stayListener,this.systemInfoGetter=t.systemInfoGetter,t.launchListener.register(this),t.unloadListener.register(this),t.exposureListener.register(this),t.appearListener.register(this),t.shareListener.register(this),t.touchListener.register(this),t.sourceListener&&t.sourceListener.register(this),t.tabItemListener&&t.tabItemListener.register(this),this.send=M(this.sendPointData,this.para.batchSendTimeout,this)}var r,n,h,y,T;return r=e,n=[{key:"install",value:function(e){var r;Ir===t&&(null===(r=this.sysAppListener)||void 0===r||r.notify(e))}},{key:"init",value:function(e){try{this.para.init(e),this.send=M(this.sendPointData,this.para.batchSendTimeout,this)}catch(e){console.warn(e)}}},{key:"appendPointData",value:function(e){var t=this.para.getTrackData(e);if(!Er.get(t.distinct_id))return this.dataPoll.append(t),t}},{key:"getReportIncidentInfo",value:function(e){if("string"==typeof e.key)return[e];var t,r=[],n=Lr(e.key);try{for(n.s();!(t=n.n()).done;){var o=t.value,i=V(e);i.key=o,r.push(i)}}catch(e){n.e(e)}finally{n.f()}return r}},{key:"track",value:function(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],r=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];return this.upload(e,{isSend:t,isDebounce:r})}},{key:"upload",value:function(e,t){var r=[];try{var n,o=Lr(this.getReportIncidentInfo(e));try{for(o.s();!(n=o.n()).done;){var i=n.value,a=this.appendPointData(i);a&&r.push(a)}}catch(e){o.e(e)}finally{o.f()}t.isSend&&(t.isDebounce?this.send&&this.send():this.sendPointData({isLast:t.isLast}))}catch(e){console.warn("SensorsImpl -> track",e)}return r}},{key:"sendPointData",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{isClone:!1,isLast:!1};if(this.para.bindServerUrl||this.para.store.user_id){var r=this.systemInfoGetter.getTerminial(),n=this.para.store,o=n.distinct_id,i=n.open_id;if((r!==d||o&&i&&o===i)&&(r!==v||o)){var a=[g,m,b,w,P];if(!a.includes(r)||o){var s=this.dataPoll.getList(),u=s.keys,c=s.list;if(0!==c.length){t.isClone&&(c=V(c));var l=this.requestHandler.send(c,t.isLast);l&&l.then((function(t){e.para.showLog&&console.log("上报数据",c),e.dataPoll.remove(u)})).catch((function(t){e.dataPoll.reset(u),e.para.showLog&&console.log("上报失败",t)}))}}}}}},{key:"onPageLoad",value:function(){try{var e=this.systemInfoGetter.getOnLoadQuery();if(e.dc_st&&(e.dc_uid||e.dc_did)){var t=this.appendPointData({eventType:p,key:p});t&&(t.event_millitime=this.systemInfoGetter.getLaunchTime()),this.send&&this.send()}}catch(e){console.warn(e)}}},{key:"onPageShow",value:function(){try{this.para.autoTrack.pageShow&&this.track({eventType:i,key:i}),this.para.autoTrack.pageStay&&this.stayListener.resetInit()}catch(e){console.warn(e)}}},{key:"onPageHide",value:function(){try{this.para.autoTrack.pageLeave&&this.track({eventType:u,key:"$PageLeave"})}catch(e){console.warn(e)}}},{key:"onClickChange",value:function(e,t){try{var r=this.systemInfoGetter.getReportIncident(e,t);r&&this.para.autoTrack.pageClick&&this.track(r,r.key!==a)}catch(e){console.warn(e)}}},{key:"onTabItemClick",value:function(e){var t=this,r=this.para,n=r.autoTrack.pageClick,o=r.tabBarPositionMap;n&&o&&o[e]&&setTimeout((function(){t.track({key:"$TabItemClick",eventType:a,customProperties:{positionSign:o[e],positionSignId:o[e]}})}),0)}},{key:"onStayChange",value:function(e){try{if(this.para.autoTrack.pageStay){var t={eventType:s,key:s};e&&(t.customProperties={$screen_height:this.systemInfoGetter.getScreenHeight(e),$screen_top:this.systemInfoGetter.getScrollTop(e)}),this.track(t,!1)}}catch(e){console.warn(e)}}},{key:"onLaunch",value:function(){try{this.para.autoTrack.appLaunch&&this.track({eventType:c,key:c}),this.para.clearLatestTrafficSource()}catch(e){console.warn(e)}}},{key:"onUnload",value:function(){try{this.para.autoTrack.appHide&&this.upload({eventType:l,key:l},{isSend:!0,isLast:!0})}catch(e){console.warn(e)}}},{key:"_addParamsToPageShare",value:function(e){var t=e.result;if(t.path||(t.path=this.systemInfoGetter.getUrl()),t.path.includes("dc_st"))return t;var r={dc_st:Date.now(),dc_did:this.para.store.distinct_id};this.para.store.user_id&&(r.dc_uid=this.para.store.user_id);var n=this.para.getCommProperties("activityId","clue");n&&(r.dc_acp=n);var o=Object.keys(r).map((function(e){return"".concat(e,"=").concat(r[e])})).join("&");if(t.path)-1!==t.path.indexOf("?")?t.path+="&".concat(o):t.path+="?".concat(o);else{var i=this.systemInfoGetter.getOnLoadQuery()||{};t.path="".concat(this.systemInfoGetter.getUrl(),"?").concat(Object.keys(i).map((function(e){return"".concat(e,"=").concat(i[e])})).join("&"),"&").concat(o)}return t}},{key:"onShare",value:function(e){try{if(this._addParamsToPageShare(e),this.para.autoTrack.pageShare){var t=this.appendPointData({eventType:f,key:f});t&&(t.properties.$referrer=this.systemInfoGetter.getUrl(),t.properties.$url=e.result.path),this.sendPointData()}}catch(e){console.warn(e)}}},{key:"login",value:(T=_r(Or().mark((function e(t){var r,n,i;return Or().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(e.prev=0,null!=t&&""!==t&&"0"!=t){e.next=3;break}return e.abrupt("return",console.warn("记录登录用户ID不能为空"));case 3:return this.para.store.is_login=!0,r=this.para.store,n=r.union_id,i=r.open_id,e.next=7,this.dataPoll.updateUniversalId(n||i,t);case 7:if(t!=this.para.store.user_id){e.next=9;break}return e.abrupt("return");case 9:this.para.store.user_id&&this.sendPointData(),this.para.login(t),this.dataPoll.modifyUserId(t),this.track({eventType:o,key:o}),this.userBind(),e.next=19;break;case 16:e.prev=16,e.t0=e.catch(0),console.warn(e.t0);case 19:case"end":return e.stop()}}),e,this,[[0,16]])}))),function(e){return T.apply(this,arguments)})},{key:"onExposure",value:function(e){try{var t=this.systemInfoGetter.getReportIncident(e);t&&this.para.autoTrack.pageExposure&&this.track(t,!1)}catch(e){console.warn(e)}}},{key:"onAppear",value:function(e){try{var t=this.systemInfoGetter.getReportIncident(e);t&&this.para.autoTrack.pageAppear&&this.track(t,!1)}catch(e){console.warn(e)}}},{key:"onTouch",value:function(e){try{var t=this.systemInfoGetter.getReportIncident(e);t&&this.track(t,!1)}catch(e){console.warn(e)}}},{key:"onSource",value:function(e){if(void 0===e.id||null===e.id||""===e.id)return console.warn("来源匿名ID不能为空");if(e.id!==this.para.store.sourceDistinctId||e.latestTrafficSourceType!==this.para.store.latestTrafficSourceType||e.latestReferrer!==this.para.store.latestReferrer)try{this.para.identifySource(e),this.para.store.user_id&&e.id!==this.para.store.sourceDistinctId&&this.userResourceId(),this.appendCommProperties({$source_terminal:e.latestTrafficSourceType})}catch(e){console.warn(e)}}},{key:"userResourceId",value:function(){var e=this;try{if(!this.para.store.sourceDistinctId)return;var t={user_id:this.para.store.user_id,distinct_id:this.para.store.sourceDistinctId};if(this.para.bindServerUrl){var r=this.requestHandler.sendRequest(this.para.bindServerUrl,t);r&&r.then((function(){e.para.showLog&&console.log("绑定用户id和来源匿名id",t)}))}}catch(e){console.warn(e)}}},{key:"userBind",value:function(){var e=this;try{var t={user_id:this.para.store.user_id,distinct_id:this.para.store.distinct_id};if(this.para.bindServerUrl){var r=this.requestHandler.sendRequest(this.para.bindServerUrl,t);r&&r.then((function(){e.para.showLog&&console.log("绑定用户id和匿名id",t)})),this.userResourceId()}}catch(e){console.warn(e)}}},{key:"identify",value:(y=_r(Or().mark((function e(t){return Or().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(e.prev=0,null!=t&&""!==t){e.next=3;break}return e.abrupt("return",console.warn("自定义匿名ID不能为空"));case 3:return e.next=5,this.dataPoll.updateUniversalId(t);case 5:if(t!==this.para.store.distinct_id||t!==this.para.store.open_id){e.next=7;break}return e.abrupt("return");case 7:this.para.identify(t),this.dataPoll.modifyDisctincId(t),e.next=14;break;case 11:e.prev=11,e.t0=e.catch(0),console.warn(e.t0);case 14:case"end":return e.stop()}}),e,this,[[0,11]])}))),function(e){return y.apply(this,arguments)})},{key:"setIdentifyUnion",value:(h=_r(Or().mark((function e(t){var r;return Or().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=t.unionid,e.prev=1,e.next=4,this.dataPoll.updateUniversalId(r);case 4:if(!this.para.store.distinct_id){e.next=6;break}return e.abrupt("return");case 6:if(null!=r&&""!==r){e.next=8;break}return e.abrupt("return",console.warn("unionid不能为空"));case 8:if(r!==this.para.store.distinct_id||r!==this.para.store.union_id){e.next=10;break}return e.abrupt("return");case 10:try{this.para.identifyUnion(t),this.dataPoll.modifyDisctincUnionId(t)}catch(e){console.warn(e)}e.next=16;break;case 13:e.prev=13,e.t0=e.catch(1),console.warn(e.t0);case 16:case"end":return e.stop()}}),e,this,[[1,13]])}))),function(e){return h.apply(this,arguments)})},{key:"appendCommProperties",value:function(e,t){try{if(!B(e))return console.warn("公共属性必须为对象");this.para.appendCommProperties(e,t),this.para.appendPageProperties(e)}catch(e){console.warn(e)}}},{key:"replaceCommProperties",value:function(e,t){try{if(!B(e))return console.warn("公共属性必须为对象");this.para.replaceCommProperties(e,t),this.para.appendPageProperties(e)}catch(e){console.warn(e)}}},{key:"removeCommProperties",value:function(){try{var e;(e=this.para).removeCommProperties.apply(e,arguments)}catch(e){console.warn(e)}}},{key:"removeCommPropertiesGroup",value:function(){try{var e;(e=this.para).removeCommPropertiesGroup.apply(e,arguments)}catch(e){console.warn(e)}}},{key:"appendPageProperties",value:function(e){try{if(!B(e))return console.warn("附加公共属性必须为对象");this.para.appendPageProperties(e)}catch(e){console.warn(e)}}},{key:"removePageProperties",value:function(){try{var e;(e=this.para).removePageProperties.apply(e,arguments)}catch(e){console.warn(e)}}},{key:"getCommProperties",value:function(e,t){try{return this.para.getCommProperties(e,t)}catch(e){console.warn(e)}}},{key:"appendCommPropertiesToPage",value:function(){try{var e;(e=this.para).appendCommPropertiesToPage.apply(e,arguments)}catch(e){console.warn(e)}}},{key:"appendCycleProperties",value:function(e){this.para.appendCycleProperties(e)}},{key:"removeCycleProperties",value:function(){try{var e;(e=this.para).removeCycleProperties.apply(e,arguments)}catch(e){console.warn(e)}}}],n&&Cr(r.prototype,n),Object.defineProperty(r,"prototype",{writable:!1}),e}();function Br(e){return Br="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Br(e)}function Rr(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Dr(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function Mr(e,t){return Mr=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},Mr(e,t)}function Ur(e,t){if(t&&("object"===Br(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}function Gr(e){return Gr=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},Gr(e)}var Nr=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Mr(e,t)}(a,e);var t,r,n,o,i=(n=a,o=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}(),function(){var e,t=Gr(n);if(o){var r=Gr(this).constructor;e=Reflect.construct(t,arguments,r)}else e=t.apply(this,arguments);return Ur(this,e)});function a(){return Rr(this,a),i.apply(this,arguments)}return t=a,r=[{key:"init",value:function(e){e.sysPageListener.register(this)}},{key:"onPage",value:function(e){this.listenerPageOnUnload(e)}},{key:"listenerPageOnUnload",value:function(e){var t=e.onUnload||function(){},r=this;e.onUnload=function(){try{r.notify()}catch(e){console.warn("AlipayMiniPageListener -> listenerPageOnUnload"+e)}t.call.apply(t,[this].concat(Array.prototype.slice.call(arguments)))}}},{key:"notify",value:function(){this.observers.forEach((function(e){e.onPageUnload&&e.onPageUnload()})),this.observers.forEach((function(e){e.onAfterPageUnload&&e.onAfterPageUnload()}))}}],r&&Dr(t.prototype,r),Object.defineProperty(t,"prototype",{writable:!1}),a}(Ve);function Hr(e){return Hr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Hr(e)}function $r(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Fr(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function Vr(e,t){return Vr=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},Vr(e,t)}function Wr(e,t){if(t&&("object"===Hr(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}function Xr(e){return Xr=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},Xr(e)}var Kr=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Vr(e,t)}(a,e);var t,r,n,o,i=(n=a,o=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}(),function(){var e,t=Xr(n);if(o){var r=Xr(this).constructor;e=Reflect.construct(t,arguments,r)}else e=t.apply(this,arguments);return Wr(this,e)});function a(){return $r(this,a),i.apply(this,arguments)}return t=a,r=[{key:"init",value:function(e){e.sysPageListener.register(this),e.sysComponentListener.register(this)}},{key:"onPage",value:function(e){this.listenerPageOnTouch(e)}},{key:"onComponent",value:function(e){this.listenerComponentOnTouch(e)}},{key:"listenerPageOnTouch",value:function(e){var t=e.onTouch||function(){},r=this;e.onTouch=function(e){try{r.notify(e)}catch(e){console.warn("AlipayTouchListener -> listenerOnTouch",e)}t.call.apply(t,[this].concat(Array.prototype.slice.call(arguments)))}}},{key:"listenerComponentOnTouch",value:function(e){var t=e.methods||{},r=t.onTouch||function(){},n=this;t.onTouch=function(e){try{n.notify(e)}catch(e){console.warn("AlipayTouchListener -> listenerOnTouch",e)}r.call.apply(r,[this].concat(Array.prototype.slice.call(arguments)))}}},{key:"notify",value:function(e){this.observers.forEach((function(t){return t.onTouch(e)}))}}],r&&Fr(t.prototype,r),Object.defineProperty(t,"prototype",{writable:!1}),a}(Ve);function qr(e){return qr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},qr(e)}function zr(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Qr(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function Jr(e,t){return Jr=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},Jr(e,t)}function Zr(e,t){if(t&&("object"===qr(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return Yr(e)}function Yr(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function en(e){return en=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},en(e)}function tn(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var rn=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Jr(e,t)}(a,e);var t,r,n,o,i=(n=a,o=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}(),function(){var e,t=en(n);if(o){var r=en(this).constructor;e=Reflect.construct(t,arguments,r)}else e=t.apply(this,arguments);return Zr(this,e)});function a(){var e;zr(this,a);for(var t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];return tn(Yr(e=i.call.apply(i,[this].concat(r))),"map",{}),tn(Yr(e),"maxTimeStramp",1e4),e}return t=a,r=[{key:"init",value:function(e){e.sysPageListener.register(this),e.sysComponentListener.register(this),e.pageListener.register(this)}},{key:"onPage",value:function(e){this.listenerPage(e)}},{key:"onComponent",value:function(e){this.listenerComponent(e)}},{key:"handleNotify",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Date.now(),r=this.map[e];r&&(r.disappearTimeStamp=t,this.notify(r),delete this.map[e])}},{key:"listenerPage",value:function(e){var t=e.onAppear||function(){},r=this;e.onAppear=function(e){try{var n=JSON.stringify(e.currentTarget);r.map[n]=e,setTimeout((function(){r.handleNotify(n)}),r.maxTimeStramp)}catch(e){console.warn("AlipayMiniAppearListener -> listenerPage",e)}t.call.apply(t,[this].concat(Array.prototype.slice.call(arguments)))};var n=e.onDisappear||function(){};e.onDisappear=function(e){try{var t=JSON.stringify(e.currentTarget);r.handleNotify(t,e.timeStamp)}catch(e){console.warn("AlipayMiniAppearListener -> listenerPage",e)}n.call.apply(n,[this].concat(Array.prototype.slice.call(arguments)))}}},{key:"listenerComponent",value:function(e){var t=e.methods||{},r=t.onAppear||function(){},n=this;t.onAppear=function(e){try{var t=JSON.stringify(e.currentTarget);n.map[t]=e,setTimeout((function(){n.handleNotify(t)}),n.maxTimeStramp)}catch(e){console.warn("AlipayMiniAppearListener -> listenerComponent",e)}r.call.apply(r,[this].concat(Array.prototype.slice.call(arguments)))};var o=t.onDisappear||function(){};t.onDisappear=function(e){try{var t=JSON.stringify(e.currentTarget);n.handleNotify(t,e.timeStamp)}catch(e){console.warn("AlipayMiniAppearListener -> listenerPage",e)}o.call.apply(o,[this].concat(Array.prototype.slice.call(arguments)))},e.methods=t}},{key:"onPageHide",value:function(){for(var e in this.map)this.handleNotify(e)}},{key:"notify",value:function(e){this.observers.forEach((function(t){return t.onAppear(e)}))}}],r&&Qr(t.prototype,r),Object.defineProperty(t,"prototype",{writable:!1}),a}(Ve);function nn(e){return nn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},nn(e)}function on(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function an(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function sn(e,t){return sn=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},sn(e,t)}function un(e,t){if(t&&("object"===nn(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return cn(e)}function cn(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function ln(e){return ln=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},ln(e)}function fn(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var pn=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&sn(e,t)}(a,e);var t,r,n,o,i=(n=a,o=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}(),function(){var e,t=ln(n);if(o){var r=ln(this).constructor;e=Reflect.construct(t,arguments,r)}else e=t.apply(this,arguments);return un(this,e)});function a(){var e;on(this,a);for(var t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];return fn(cn(e=i.call.apply(i,[this].concat(r))),"observers",[]),e}return t=a,r=[{key:"onPage",value:function(e){this.listenerTabItemTap(e)}},{key:"init",value:function(e){e.sysPageListener.register(this)}},{key:"listenerTabItemTap",value:function(e){var t=this,r=e.onTabItemTap;e.onTabItemTap=function(e){U(r)&&r.apply(this,arguments),"user"===e.from&&1===getCurrentPages().length&&t.notify(e.pagePath)}}},{key:"notify",value:function(e){this.observers.forEach((function(t){t.onTabItemClick(e)}))}}],r&&an(t.prototype,r),Object.defineProperty(t,"prototype",{writable:!1}),a}(Ve),hn=D(),yn=new dr;if(hn===e)try{if(!my.sensors){var dn=new rt,vn=new pe({storageHandler:dn}),gn=new Re,mn=new ar,bn=new Nr,wn=new hr,Pn=new Ye,Tn=new pt,kn=new Ie,On=new He,Sn=new Xt,_n=new bt,Ln=new _t,An=new Rt,Cn=new jt,xn=new Mt,En=new Tr,In=new rn,jn=new Yt,Bn=new Kr,Rn=new pn;yn=new jr({para:new Ce({dataPoll:vn,requestHandler:gn,pageListener:Pn,clickListener:Tn,stayListener:kn,systemInfoGetter:On,storageHandler:dn,launchListener:Sn,unloadListener:_n,sysAppListener:Ln,sysPageListener:Cn,sysComponentListener:An,scrollListener:xn,shareListener:jn,pageLoadListener:mn,pageUnloadListener:bn,sourceListener:wn,exposureListener:En,appearListener:In,touchListener:Bn,tabItemListener:Rn}),dataPoll:vn,requestHandler:gn,pageListener:Pn,clickListener:Tn,stayListener:kn,systemInfoGetter:On,launchListener:Sn,unloadListener:_n,shareListener:jn,pageLoadListener:mn,sourceListener:wn,exposureListener:En,touchListener:Bn,appearListener:In,tabItemListener:Rn}),my.sensors=yn}yn=my.sensors}catch(e){console.warn(e),my.sensors=yn}const Dn=yn})();var o=n.Z;export{o as default};