<template>
    <div class="routing">
        <router-link :to="{name: 'PageViewA'}">$PageView A</router-link>
        <router-link :to="{name: 'PageViewB'}">$PageView B</router-link>
        <router-link :to="{name: '<PERSON>Click'}">$PageClick</router-link>
        <router-link :to="{name: 'PageStay'}">$PageStay</router-link>
        <router-link :to="{name: 'MpHide'}">$MpHide</router-link>
        <router-link :to="{name: 'PageExposure'}">$PageExposure</router-link>
        <router-link :to="{name: 'Touch'}">$Touch</router-link>
    </div>
</template>

<script lang="ts">
    "use script";
    import { defineComponent } from "vue";

    export default defineComponent({
        name: "Routing",
        setup() {

        }
    });
</script>

<style scoped lang="scss">
    .routing {
        display: flex;
        flex-direction: column;
        margin: 10% 20%;
    }
</style>