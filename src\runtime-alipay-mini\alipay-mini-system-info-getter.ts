import AlipayMiniSensorsParaImpl from "./alipay-mini-sensors-para-impl";
import { eventEnum, terminalEnum } from "../runtime-core/enum";
import { generateUUID, compareVersion, queryAttr, isObject, generateDataSetAttributes } from "@/utils";
import { storageKey } from "@/runtime-core/sensors-para-impl";

type SystemInfo = {
  model: string; // 手机型号。
  pixelRatio: number; // 设备像素比。
  windowWidth: number; // 窗口宽度。
  windowHeight: number; // 窗口高度。
  language: string; // 支付宝设置的语言。分别有以下值：zh-Hans（简体中文）、en（English）、zh-Hant（繁体中文（台湾））、zh-HK（繁体中文（香港））。
  version: string; // 支付宝版本号。
  storage: string; // 设备磁盘容量。
  currentBattery: string; // 当前电量百分比。
  system: string; // 系统版本。
  platform: string; // 系统名：Android，iOS / iPhone OS 。
  screenHeight: number; // 屏幕高度。
  brand: string; // 手机品牌。
  fontSizeSetting: number; // 用户设置字体大小。
  app: string; // 当前运行的客户端。若当前为支付宝，则有效值为 "alipay"。
  clientVersion: string; // 核心库版本
};

// 来源渠道映射
const sourceChannelMap: any = {
  "1000": "首页十二宫格及更多",
  "1001": "应用中心-更多",
  "1002": "小程序收藏应用入口",
  "1003": "支付宝客户端首页",
  "1005": "顶部搜索框的搜索结果页",
  "1007": "单人聊天会话中的小程序消息卡片（分享）",
  "1008": "智能助理",
  "1011": "扫描二维码",
  "1014": "小程序商家消息（服务消息）",
  "1015": "出行频道-订机票酒店",
  "1020": "生活号 profile 页相关小程序列表",
  "1022": "直播",
  "1023": "系统桌面图标",
  "1037": "小程序打开小程序",
  "1038": "从另一个小程序返回",
  "1039": "消费圈",
  "1200": "市民中心（原城市服务频道）",
  "1201": "芝麻信用频道",
  "1202": "出行(原车主服务频道)",
  "1205": "支付宝就业小程序",
  "1208": "支付宝学生特惠小程序",
  "1209": "支付宝会员频道",
  "1215": "医疗健康频道",
  "1300": "第三方APP打开",
  "1301": "碰一下打开小程序",
  "1305": "支付宝 push 消息（同 1014)",
  "1400": "付费流量(通过商家数字推广平台，灯火等投放的广告)",
  "1401": "卡包",
  "1402": "支付宝-我的",
  "1403": "支付成功页",
  "0000": "其他渠道场景渠道。",
};

/**
 * 系统信息提取 小程序端
 */
export default class AlipayMiniSystemInfoGetter
  implements SystemInfoGetter, AlipayMiniScrollObserver, PageLoadObserver, PageObserver, LaunchObserver
{
  private appId?: string; // 小程序id
  private model: string; // 手机型号。
  private pixelRatio: number; // 设备像素比。
  private windowWidth: number; // 窗口宽度。
  private windowHeight: number; // 窗口高度。
  private language: string; // 支付宝设置的语言。分别有以下值：zh-Hans（简体中文）、en（English）、zh-Hant（繁体中文（台湾））、zh-HK（繁体中文（香港））。
  private version?: string; // 支付宝版本号。
  private storage: string; // 设备磁盘容量。
  private currentBattery: string; // 当前电量百分比。
  private system: string; // 系统版本。
  private platform: string; // 系统名：Android，iOS / iPhone OS 。
  private screenHeight: number; // 屏幕高度。
  private brand: string; // 手机品牌。
  private fontSizeSetting: number; // 用户设置字体大小。
  private scrollTop?: number; // 滚动条位置
  private scrollHeight?: number; // 页面文档高度
  private referrerAppId?: string; // 推荐来源小程序appid
  private isCollected: 0 | 1 = 0; // 是否收藏
  private versionCode: string; //支付宝版本号，由于之前app_version被小程序版本号占用，已无法改回来，mini_version不符合语义，只能新加该字段
  /**
   * 当前运行的客户端。若当前为支付宝，则有效值为 "alipay"。不同的客户端，对应的有效值如下：
   * 支付宝：alipay
   * UC浏览器：UC
   * 夸克浏览器：QUARK
   * 阿里健康：AK
   * 高德：amap
   * 优酷：YK
   * 钉钉：DINGTALK
   */
  private app: string;
  private runScene?: string; // 运行版本
  private networkType?: string; // 网络类型
  private sourceChannel?: string; // 来源渠道
  private visitFromOutside?: boolean; // 是否端外访问（小程序之外的渠道）
  private dataPoll?: DataPoll;
  private isUrlJustUpdate = false; // 是否刚刚更新url
  private currentUrl = ""; // 当前页面url
  private preUrl?: string; // 上一个页面路径
  private tempUrl?: string; // 临时路径
  private launchTime = Date.now(); // 程序启动时间
  private _detainedTime: number; // 上次鼠标移动时间
  private _duration_time: number = 0; // 停留时间
  private enterPageTime: number = 0;
  private wifiSsid?:string;
  private wifiBssid?:string;
  launchQuery: Record<string, string> = {}; // 程序启动参数
  onLoadQuery: Record<string, string> = {}; // 页面启动参数
  pageTitleConfig: Record<string, any> = {};
  timeGap = 0; // 服务器时间和系统时间的差距
  timer: NodeJS.Timeout | null = null; // 轮询支付宝收藏接口
  private storageHandler?: StorageHandler;
  constructor() {
    const systemInfo: SystemInfo = my.getSystemInfoSync();
    this.model = systemInfo.model;
    this.pixelRatio = systemInfo.pixelRatio;
    this.windowWidth = systemInfo.windowWidth;
    this.windowHeight = systemInfo.windowHeight;
    this.language = systemInfo.language;
    this.storage = systemInfo.storage;
    this.currentBattery = systemInfo.currentBattery;
    this.system = systemInfo.system;
    this.platform = systemInfo.platform;
    this.screenHeight = systemInfo.screenHeight;
    this.brand = systemInfo.brand;
    this.fontSizeSetting = systemInfo.fontSizeSetting;
    this.app = systemInfo.app;
    this._detainedTime = Date.now();
    this.versionCode = systemInfo.version;
    try {
      if (compareVersion(my.SDKVersion, "2.7.17") >= 0) {
        const accountInfo = my.getAccountInfoSync();
        this.appId = accountInfo.miniProgram.appId;
        this.runScene = accountInfo.miniProgram.envVersion;
        this.version = accountInfo.miniProgram.version;
      } else {
        this.appId = my.getAppIdSync().appId;
        my.getRunScene({
          success: (result: any) => {
            this.runScene = result.envVersion;
            this.dataPoll &&
              this.dataPoll.modifyAny((data: PointData) => {
                data.lib.$mini_version = this.runScene;
              });
          },
        });
      }
    } catch (err) {
      console.log("基础库 2.7.17 及以上版本 才能使用 getAccountInfoSync, 1.20 支持getAppIdSync");
    }
    my.getNetworkType({
      success: (res: any) => {
        this.networkType = res.networkType;
        this.dataPoll &&
          this.dataPoll.modifyAny((data: PointData) => {
            data.lib.$network_type = this.networkType;
          });
      },
    });
    my.onAppShow((args: any) => {
      if (args.scene) {
        this.sourceChannel = sourceChannelMap[args.scene] || args.scene;
        this.visitFromOutside = !!(args.scene == 1037 || args.scene == 1038);
        this.dataPoll &&
          this.dataPoll.modifyAny((data: PointData) => {
            data.properties.$latest_traffic_source_type = this.sourceChannel;
            data.properties.$is_visit_from_outside = this.visitFromOutside;
          });
      }
    });
    this.initWifiParams();
    this.initWifiMonitor();
    this.fetchCollected();
    // 修改为10分钟一次采集一次
    this.timer = setInterval(() => {
      this.fetchCollected();
    }, 600000);
    this.getServiceTime().then((timestamp) => {
      this.timeGap = Date.now() - timestamp;
    });
  }
  init(data: AlipayMiniSensorsParaImpl) {
    data.launchListener && data.launchListener.register(this);
    data.pageLoadListener.register(this);
    data.scrollListener.register(this);
    data.pageListener.register(this);
    this.storageHandler = data.storageHandler;
    this.dataPoll = data.dataPoll;
    this.pageTitleConfig = data.pageTitleConfig;
  }
  getLaunchTime(): number {
    return this.launchTime;
  }
  getTimestamp() {
    return Date.now() - this.timeGap;
  }
  onBeforeLaunch(options: any) {
    if (options.referrerInfo && options.referrerInfo.appId) {
      this.referrerAppId = options.referrerInfo.appId;
    }
    if (options.query) {
      this.launchQuery = options.query;
    }
  }
  onLaunch() {
    this.launchTime = this.getTimestamp();
  }
  onBeforePageLoad(options: any) {
    this.onLoadQuery = options;
    this.currentUrl = this.getCurrentUrl();
    this.isUrlJustUpdate = true;
    // 如果页面参数中有statistical_from字段，把字段放到程序启动参数中
    const { statistical_from } = this.onLoadQuery;
    if (statistical_from && statistical_from !== "undefined") {
      this.launchQuery.statistical_from = statistical_from;
    }
  }
  generateRandomId() {
    return generateUUID();
  }
  // 默认的终端
  getTerminial() {
    return (
      {
        alipay: terminalEnum.ALIPAY_INDI,
        DINGTALK: terminalEnum.DINGTALK,
      }[this.app] || terminalEnum.ALIPAY_INDI
    );
  }
  getAppId() {
    return this.appId || "";
  }
  getIsCollected() {
    return this.isCollected;
  }
  getModel() {
    return this.model;
  }
  getPixelRatio() {
    return this.pixelRatio;
  }
  getLanguage() {
    return this.language;
  }
  getAppVersion() {
    return this.version;
  }
  getStorage() {
    return this.storage;
  }
  getCurrentBattery() {
    return this.currentBattery;
  }
  getSystemVersion() {
    return this.system;
  }
  getSystemPlatform() {
    return this.platform;
  }
  getBrand() {
    return this.brand;
  }
  getFontSizeSetting() {
    return this.fontSizeSetting;
  }
  getLib() {
    return terminalEnum.ALIPAY_MINI;
  }
  getViewportWidth() {
    return this.windowWidth || 0;
  }
  getViewportHeight() {
    return this.windowHeight || 0;
  }
  getScreenHeight() {
    return this.scrollHeight || this.screenHeight || 0;
  }
  getScrollTop() {
    return this.scrollTop || 0;
  }
  getTitle() {
    // 根据标题配置返回标题
    const url = this.getUrl();
    if (this.pageTitleConfig[url] || this.pageTitleConfig.defaultTitle) {
      return this.pageTitleConfig[url] || this.pageTitleConfig.defaultTitle;
    }
  }
  getReferrer() {
    return this.preUrl || "";
  }
  getReferrerAppId() {
    return this.referrerAppId;
  }
  /**
   * 获取事件属性
   */
  getReportIncident(e: any, funcName: string) {
    let dataset: any, eventName, elementType, elementPath;
    let positionSign;
    let indexModuleKey;
    let modulePositionSort;
    const eventTypeMap: any = {
      tap: eventEnum.PAGE_CLICK,
      firstAppear: eventEnum.PAGE_EXPOSURE,
      appear: eventEnum.PAGE_EXPOSURE,
      touchEnd: eventEnum.TOUCH,
    };
    const eventType = eventTypeMap[e.type];
    if (eventType === eventEnum.PAGE_CLICK) {
      dataset = {
        ...(e.target.targetDataset || {}),
        ...(e.target.dataset || {}),
        ...(e.currentTarget.dataset || {}),
      };
      eventName = dataset.sensors;
      elementType = e.currentTarget.tagName;
      const attrs = Object.keys(dataset)
        .sort()
        .map((key) => {
          if (dataset[key] === undefined) return key;
          else if (/[\u4e00-\u9fa5]/.test(dataset[key])) return key;
          else if (/^\d+\.?\d*$/.test(dataset[key])) {
            if (Number(dataset[key]) > 1000 || /\d+\.\d+/.test(dataset[key])) return key;
            else return `${key}=${dataset[key]}`;
          } else if (isObject(dataset[key])) return key;
          else if (/[a-z\d]{10,}/.test(dataset[key])) return key;
          else return `${key}=${dataset[key]}`;
        })
        .filter((item) => !!item)
        .join("&");
      positionSign = queryAttr(dataset, "position_sign");
      indexModuleKey = queryAttr(dataset, "index_module_key", true);
      modulePositionSort = queryAttr(dataset, "module_position_sort");
      const lastPage = getCurrentPages().slice(-1)[0] || {};
      lastPage.$lastPositionSign = positionSign || "";
      lastPage.$lastIndexModuleKey = indexModuleKey || "";
      lastPage.$lastModulePositionSort = modulePositionSort || "";
      elementPath = `${this.getTerminial()}_${this.getUrl()}_${funcName}_${e.currentTarget.tagName}${
        attrs ? "_" + attrs : ""
      }${e.currentTarget.id ? "_" + e.currentTarget.id : ""}${positionSign ? "_" + positionSign : ""}`;
    } else if (eventType === eventEnum.TOUCH) {
      if (e.detail && e.detail.source === "autoplay") return;
      dataset = e.currentTarget.dataset;
      eventName = dataset.sensorsTouch || dataset.sensorstouch;
      elementType = e.currentTarget.tagName;
    } else if (eventType === eventEnum.PAGE_EXPOSURE) {
      dataset = e.currentTarget.dataset;
      eventName = e.currentTarget.dataset.sensorsExposure || eventEnum.PAGE_EXPOSURE;
      elementType = e.currentTarget.tagName;
    }
    if (dataset) {
      const customProperties = generateDataSetAttributes(dataset);
      // 尝试直接获取到业务属性
      positionSign && (customProperties.positionSignId = positionSign);
      indexModuleKey && (customProperties.indexModuleKey = indexModuleKey);
      modulePositionSort && (customProperties.modulePositionSort = modulePositionSort);
      const result: any = {
        eventType,
        key: eventName || eventType,
        $element_type: elementType,
        $element_path: elementPath,
        customProperties,
      };
      if (eventType === eventEnum.PAGE_CLICK) {
        result.$page_x = e.detail.pageX;
        result.$page_y = e.detail.pageY;
        result.$client_x = e.target.offsetLeft;
        result.$client_y = e.target.offsetTop;
      }
      if (eventType === eventEnum.PAGE_EXPOSURE && e.disappearTimeStamp) {
        result.$appear_duration = e.disappearTimeStamp - e.timeStamp;
      }
      return result;
    }
  }
  /**
   * 获取小程序当前运行的版本
   * develop：开发版。
   * trial：体验版。
   * release：发布版。
   * gray：灰度版。
   */
  getMiniVersion() {
    return this.runScene;
  }
  getNetworkType() {
    return this.networkType;
  }
  getLatestTrafficSourceType() {
    return this.sourceChannel || "";
  }
  getLatestReferrer(): string {
    return "";
  }
  getVisitFromOutside() {
    return this.visitFromOutside;
  }
  getUrl() {
    return this.currentUrl;
  }
  getUrlPath() {
    return this.getUrl();
  }
  getCurrentUrl() {
    try {
      const pages = getCurrentPages();
      return pages[pages.length - 1] ? pages[pages.length - 1].route : "plugin://";
    } catch (err) {
      return "";
    }
  }
  getPageDurationTime() {
    return this._duration_time;
  }
  onBeforeShow() {
    if (!this.isUrlJustUpdate) {
      this.currentUrl = this.getCurrentUrl();
    }
    this.isUrlJustUpdate = false;
    if (this.tempUrl !== this.preUrl && this.tempUrl !== this.getUrl()) {
      this.preUrl = this.tempUrl;
    }
    this._detainedTime = this.enterPageTime = Date.now();
    this._duration_time = 0;
  }
  onBeforeHide() {
    this.tempUrl = this.getUrl();
    this._duration_time = this.enterPageTime ? Date.now() - this.enterPageTime : 0;
  }
  /**
   * @description: 页面滚动
   * @param {AlipayMiniScrollInfo} args
   * @return {*}
   */
  onScrollChange(args: AlipayMiniScrollInfo) {
    this.scrollTop = args.scrollTop;
    this.scrollHeight = args.scrollHeight;
    // 更新页面停留时长
    const date = Date.now();
    if (date - this._detainedTime < 2 * 60 * 1000) {
      this._duration_time += date - this._detainedTime;
    }
    this._detainedTime = date;
  }
  getLaunchQuery(): Record<string, string> {
    return this.launchQuery || {};
  }
  getOnLoadQuery(): Record<string, string> {
    return this.onLoadQuery || {};
  }
  getServiceTime(): Promise<number> {
    return new Promise((resolve, reject) => {
      my.getServerTime({
        success: (res: any) => {
          resolve(res.time);
        },
        fail: (err: any) => {
          console.warn("获取服务时间失败", err);
          resolve(Date.now());
        },
      });
    });
  }
  getVersionCode(): string {
    return this.versionCode;
  }
  getPageList() {
    const routes = getCurrentPages();
    return routes?.map((p: any) => p && p.route ? p.route : "plugin://");
  }

  getPageVersion() {
    // 看看缓存里有没有存着的页面版本号
    const store = this.storageHandler?.get<SensorsParaStore>(storageKey);
    const commProperties = store?.commProperties || {};
    const page_version_obj = commProperties.page_version_obj ? JSON.parse(commProperties.page_version_obj) : {};
    const page_version = page_version_obj[this.currentUrl] || "";
    return page_version
  }

  getLastList(key: string) {
    let index = 0;
    const routes = getCurrentPages();
    return routes.map((p: any, idx: number) => {
      if (p && p[key]) {
        index = idx;
        return p[key];
      } else {
        return (routes[index] && routes[index][key]) || "";
      }
    });
  }
  getPositionList() {
    return this.getLastList("$lastPositionSign");
  }
  getIndexModuleKeyList() {
    return this.getLastList("$lastIndexModuleKey");
  }
  getModulePositionSortList() {
    return this.getLastList("$lastModulePositionSort");
  }
  getWifiSsid(){
    return this.wifiSsid;
  }
  getWifiBssid(){
    return this.wifiBssid;
  }
  // 支付宝小程序收藏状态
  fetchCollected() {
    if (my.canIUse("isCollected")) {
      my.isCollected({
        success: (res: { isCollected: boolean }) => {
          this.isCollected = res.isCollected ? 1 : 0;
        },
      });
    }
  }
  //获取已连接的 Wi-Fi 信息
  initWifiParams(){
    my.startWifi({
      success: ()=> {
        my.getConnectedWifi({
          success: (res:{wifi:WifiInfo})=> {
            const {wifi:{BSSID,SSID}}=res;
            this.wifiSsid=SSID;
            this.wifiBssid=BSSID;
            this.dataPoll &&
            this.dataPoll.modifyAny((data: PointData) => {
              data.lib.$wifi_bssid=BSSID;
              data.lib.$wifi_ssid=SSID;
            });
          }
        })
      }
    });
  }
  //监听网络状态变化事件
  initWifiMonitor(){
    my.onNetworkStatusChange((res:NetworkStatus)=>{
      const {isConnected,networkType}=res;
      if(isConnected&&networkType==='wifi') return this.initWifiParams();
      this.wifiBssid='';
      this.wifiSsid='';    
    });
  }
}
