import { debounce } from "@/utils";
import AlipayMiniSensorsParaImpl from "../alipay-mini-sensors-para-impl";
type debounceNotify = () => void;

/**
 * 页面停留监听器 支付宝小程序端
 */
export default class AlipayMiniStayListener implements StayListener, AlipayMiniScrollObserver {
    observers: StayObserver[] = [];
    onScrollChange() {
        this.debounceNotify && this.debounceNotify();
    }
    init(data: AlipayMiniSensorsParaImpl) {
        this.debounceNotify = debounce(this.notify, data.scrollDelayTime, this);
        this.resetInit();
        data.scrollListener.register(this);
    }
    register(observer: StayObserver) {
        this.observers.push(observer);
    }
    remove(oberser: StayObserver) {
        const index = this.observers.findIndex(item => item === oberser);
        if (index != -1) this.observers.splice(index, 1);
    }
    debounceNotify?: debounceNotify;
    notify() {
        this.observers.forEach(observer => observer.onStayChange());
    }
    resetInit() {
        this.debounceNotify && this.debounceNotify();
    }
}