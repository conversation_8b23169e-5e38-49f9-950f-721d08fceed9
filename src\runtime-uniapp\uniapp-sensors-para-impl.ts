import SensorsParaImpl, { SensorsParaConstructorArgs } from "@/runtime-core/sensors-para-impl";
import UniappSysAppListener from "./listeners/uniapp-sys-app-listener";
import UniappLaunchListener from "./listeners/uniapp-launch-listener";
import UniappScrollListener from "./listeners/uniapp-scroll-listener";
import UniappSysComponentListener from "./listeners/uniapp-sys-component-listener";
import UniappSysPageListener from "./listeners/uniapp-sys-page-listener";
import UniappSystemInfoGetter from "./uniapp-system-info-getter";
import overload from "./uniapp-mp-overload";
import { judgingEnvironment } from "@/utils";
import { uniappLib } from "../runtime-core/enum";
import UniappSysVueComponentListener from "./listeners/uniapp-sys-vue-component-listener";

const lib = judgingEnvironment();

interface ConstructorArgs extends SensorsParaConstructorArgs {
  sysAppListener: UniappSysAppListener;
  sysVueComponentListener: UniappSysVueComponentListener;
  launchListener: UniappLaunchListener;
  sysPageListener: UniappSysPageListener;
  sysComponentListener: UniappSysComponentListener;
  scrollListener: UniappScrollListener;
  tabItemListener: TabItemListener;
}

/**
 * 传感器配置数据 支付宝小程序端
 */
export default class UniappSensorsParaImpl extends SensorsParaImpl {
  sysAppListener: UniappSysAppListener;
  sysVueComponentListener: UniappSysVueComponentListener;
  launchListener: UniappLaunchListener;
  sysPageListener: UniappSysPageListener;
  sysComponentListener: UniappSysComponentListener;
  scrollListener: UniappScrollListener;
  tabItemListener: TabItemListener;
  constructor(props: ConstructorArgs) {
    super(props);
    this.sysAppListener = props.sysAppListener;
    this.sysVueComponentListener = props.sysVueComponentListener;
    this.launchListener = props.launchListener;
    this.sysPageListener = props.sysPageListener;
    this.sysComponentListener = props.sysComponentListener;
    this.scrollListener = props.scrollListener;
    this.tabItemListener = props.tabItemListener;
  }
  getTrackData(data: ReportIncidentInfo) {
    const pointData = super.getTrackData(data);
    const systemInfoGetter = this.systemInfoGetter as UniappSystemInfoGetter;
    pointData.lib.$model = systemInfoGetter.getModel();
    pointData.lib.$pixel_ratio = systemInfoGetter.getPixelRatio();
    pointData.lib.$language = systemInfoGetter.getLanguage();
    pointData.lib.$app_version = systemInfoGetter.getAppVersion();
    pointData.lib.$system_version = systemInfoGetter.getSystemVersion();
    pointData.lib.$system_platform = systemInfoGetter.getSystemPlatform();
    pointData.lib.$brand = systemInfoGetter.getBrand();
    pointData.lib.$version_code = systemInfoGetter.getVersionCode();
    pointData.lib.$wifi_ssid=systemInfoGetter.getWifiSsid();
    pointData.lib.$wifi_bssid=systemInfoGetter.getWifiBssid();
    pointData.properties.$latest_traffic_source_type = systemInfoGetter.getLatestTrafficSourceType();
    

    pointData.properties.page_list = systemInfoGetter.getPageList();
    pointData.properties.page_version = systemInfoGetter.getPageVersion()

    pointData.properties.position_list = systemInfoGetter.getPositionList();
    // 注入下载渠道属性
    pointData.lib.$download_channel = systemInfoGetter.getAppDownloadChannel();
    if (uniappLib.includes(lib as LibEnumType)) {
      pointData.lib.$app_id = systemInfoGetter.getAppId();
      pointData.lib.$current_battery = systemInfoGetter.getCurrentBattery();
      pointData.lib.$font_size_setting = systemInfoGetter.getFontSizeSetting();
      const miniVersion = systemInfoGetter.getMiniVersion();
      miniVersion && (pointData.lib.$mini_version = miniVersion);
      const networkType = systemInfoGetter.getNetworkType();
      networkType && (pointData.lib.$network_type = networkType);
    }
    return pointData;
  }
  init(data: InitData) {
    this.sysVueComponentListener.init(this);
    uniappLib.includes(lib as LibEnumType) && overload.init(this.sysPageListener, this.sysComponentListener);
    this.sysPageListener.init(this);
    this.sysComponentListener.init(this);
    super.init(data);
    this.scrollListener.init(this);
    this.tabItemListener.init(this);
  }
}
