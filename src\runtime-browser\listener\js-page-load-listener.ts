import SubjectListenerImpl from "@/runtime-core/subject-listener-impl"

export default class JsPageLoadListener extends SubjectListenerImpl<PageLoadObserver> implements PageLoadListener, PageObserver {
    init(data: SensorsPara) {
        data.pageListener.register(this);
    }
    onBeforeShow() {
        this.notify({});
    }
    notify(options: any) {
        this.observers.forEach(observer => observer.onBeforePageLoad && observer.onBeforePageLoad(options));
        this.observers.forEach(observer => observer.onPageLoad && observer.onPageLoad(options));
    }
}