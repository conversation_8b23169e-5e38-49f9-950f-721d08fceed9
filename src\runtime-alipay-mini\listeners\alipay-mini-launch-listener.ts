import SubjectListenerImpl from "@/runtime-core/subject-listener-impl";
import AlipayMiniSensorsParaImpl from "../alipay-mini-sensors-para-impl";

/**
 * 通知阶段
 */
 type NotifyPhase = 'BEFORE' | 'AFTER';

/**
 * 监听小程序初始化 支付宝小程序
 */
export default class AlipayMiniLaunchListener extends SubjectListenerImpl<LaunchObserver> implements LaunchListener, AlipayMiniSysAppObserver {
    observers: LaunchObserver[] = [];
    init(data: AlipayMiniSensorsParaImpl) {
        data.sysAppListener.register(this);
    };
    notify(phase: NotifyPhase, options: any) {
        phase === 'BEFORE' && this.observers.forEach(observer => observer.onBeforeLaunch && observer.onBeforeLaunch(options));
        phase === 'AFTER' && this.observers.forEach(observer => observer.onLaunch && observer.onLaunch(options));
    }
    /**
     * 监听App onLaunch方法
     */
    listenerOnLaunch(app: any) {
        const oldOnLaunch = app.onLaunch || function () {};
        const that = this;
        app.onLaunch = function (options: any) {
            that.notify('BEFORE', options);
            oldOnLaunch.call(this, ...arguments);
            that.notify('AFTER', options);
        }
    }
    onApp(app: any) {
        this.listenerOnLaunch(app);
    }
}