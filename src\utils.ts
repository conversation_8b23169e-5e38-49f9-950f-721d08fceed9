import { nanoid } from "nanoid";
import { libEnum, dynamicPropEnum } from "./runtime-core/enum";
import sha256 from "sha256-es";
import md5 from "md5-es";
import Fingerprint2 from "fingerprintjs2";

/**
 * 字符串转换为base64
 */
export function base64Encode(data: string) {
  const b64 = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";
  let o1,
    o2,
    o3,
    h1,
    h2,
    h3,
    h4,
    bits,
    i = 0,
    ac = 0,
    enc = "",
    tmp_arr = [];
  if (!data) {
    return data;
  }
  data = utf8Encode(data);
  do {
    o1 = data.charCodeAt(i++);
    o2 = data.charCodeAt(i++);
    o3 = data.charCodeAt(i++);

    bits = (o1 << 16) | (o2 << 8) | o3;

    h1 = (bits >> 18) & 0x3f;
    h2 = (bits >> 12) & 0x3f;
    h3 = (bits >> 6) & 0x3f;
    h4 = bits & 0x3f;
    tmp_arr[ac++] = b64.charAt(h1) + b64.charAt(h2) + b64.charAt(h3) + b64.charAt(h4);
  } while (i < data.length);

  enc = tmp_arr.join("");

  switch (data.length % 3) {
    case 1:
      enc = enc.slice(0, -2) + "==";
      break;
    case 2:
      enc = enc.slice(0, -1) + "=";
      break;
  }

  return enc;
}

/**
 * 字符串转换未utf8
 */
export function utf8Encode(string: string) {
  string = (string + "").replace(/\r\n/g, "\n").replace(/\r/g, "\n");

  let utftext = "",
    start,
    end;
  let stringl = 0,
    n;

  start = end = 0;
  stringl = string.length;

  for (n = 0; n < stringl; n++) {
    let c1 = string.charCodeAt(n);
    let enc = null;

    if (c1 < 128) {
      end++;
    } else if (c1 > 127 && c1 < 2048) {
      enc = String.fromCharCode((c1 >> 6) | 192, (c1 & 63) | 128);
    } else {
      enc = String.fromCharCode((c1 >> 12) | 224, ((c1 >> 6) & 63) | 128, (c1 & 63) | 128);
    }
    if (enc !== null) {
      if (end > start) {
        utftext += string.substring(start, end);
      }
      utftext += enc;
      start = end = n + 1;
    }
  }

  if (end > start) {
    utftext += string.substring(start, string.length);
  }

  return utftext;
}

/**
 * 检测是否对象
 */
export function isObject(obj: any) {
  return Object.prototype.toString.call(obj) === "[object Object]";
}

/**
 * 检测是否为数组
 */
export function isArray(array: any) {
  return Object.prototype.toString.call(array) === "[object Array]";
}

/**
 * 检测是否字符串
 */
export function isString(string: any) {
  return typeof string === "string";
}

/**
 * 生成uuid
 */
export function generateUUID() {
  return (
    Date.now() +
    "-" +
    Math.floor(1e7 * Math.random()) +
    "-" +
    Math.random().toString(16).replace(".", "") +
    "-" +
    String(31242 * Math.random())
      .replace(".", "")
      .slice(0, 8)
  );
}

/**
 * 生成nanoid
 */
export function generateNanoid(num: number = 32) {
  let str = "";
  try {
    str = nanoid(num);
  } catch (err) {
    console.warn("不支持nanoid生成");
    str = generateUUID();
  }
  return str;
}

/**
 * 判断运行环境，不能随意更改代码顺序
 */
export function judgingEnvironment(): LibEnumType | void {
  try {
    // 打包环境判断三个端
    const LIB_ENV = process.env.LIB_ENV;
    if (LIB_ENV === "web") return libEnum.WEB;
    if (LIB_ENV === "uniapp") {
      const isAlipayMini = typeof my !== "undefined";
      const { uniPlatform } = uni.getSystemInfoSync();
      // 按照平台优先级判断 此处先判断是否为webview或者h5页面
      if (uniPlatform === "web") return libEnum.UNIAPP_H5;
      //字节跳动小程序、快手小程序、h5里面有wx全局变量，先判断微信
      if (typeof wx !== "undefined" && uniPlatform === "mp-weixin") return libEnum.UNIAPP_WEIXIN;
      if (typeof tt !== "undefined") return libEnum.UNIAPP_BYTEDANCE;
      if (typeof ks !== "undefined") return libEnum.UNIAPP_KUAISHOU;
      if (typeof dd !== "undefined") return libEnum.UNIAPP_DING;
      if (typeof jd !== "undefined") return libEnum.UNIAPP_JD;
      if (isAlipayMini) return libEnum.UNIAPP_ALIPAY;
      if (typeof plus !== "undefined") return libEnum.UNIAPP_APP_PLUS;
      return libEnum.UNIAPP_UNKNOWN;
    }
    if (LIB_ENV === "alipay") return libEnum.ALIPAY_MINI;
  } catch (error) {
    console.warn("环境判断出错:", error);
  }
}

/**
 * 防抖函数
 */
export function debounce(func: (...args: any[]) => any, wait: number, self?: object) {
  let timeout: any = null;
  return function (...args: any) {
    if (timeout) clearTimeout(timeout);
    return new Promise((resolve) => {
      timeout = setTimeout(() => {
        resolve(func.call(self, ...args));
        timeout = null;
      }, wait);
    });
  };
}

/**
 * 节流函数
 */
export function waterTap(func: (...args: any[]) => any, delay: number, self?: any) {
  let prev = 0;
  return function (...args: any) {
    if (Date.now() - prev >= delay) {
      func.apply(self, args);
      prev = Date.now();
    }
  };
}

/**
 * 防抖节流函数
 */
export function debounceWaterTap(func: (...args: any[]) => any, wait: number, self?: object) {
  let prev = Date.now();
  let timeout: any = null;
  return function (...args: any) {
    if (Date.now() - prev >= wait) {
      func.call(self, ...args);
      prev = Date.now();
      clearTimeout(timeout);
    } else {
      if (timeout) clearTimeout(timeout);
      timeout = setTimeout(() => {
        func.call(self, ...args);
        timeout = null;
        prev = Date.now();
      }, wait);
    }
  };
}

/**
 * 判断是否方法
 */
export function isFunction(f: any) {
  if (!f) {
    return false;
  }
  const type = toString.call(f);
  return type == "[object Function]" || type == "[object AsyncFunction]";
}

/**
 * 判断是否数字
 */
export function isNumber(val: any) {
  // isNaN()函数 把空串 空格 以及NUll 按照0来处理 所以先去除，
  if (val === "" || val == null) {
    return false;
  }
  if (!isNaN(val)) {
    //对于空数组和只有一个数值成员的数组或全是数字组成的字符串，isNaN返回false，例如：'123'、[]、[2]、['123'],isNaN返回false,
    //所以如果不需要val包含这些特殊情况，则这个判断改写为if(!isNaN(val) && typeof val === 'number' )
    return true;
  } else {
    return false;
  }
}

/**
 * 生成数组请求体hash_data
 * sha256
 */
export function genderSha256(data: any) {
  if (!isObject(data)) return "";
  const keys = Object.keys(data).sort();
  let str = "";
  for (let i = 0; i < keys.length; i++) {
    const value = data[keys[i]];
    str += (str ? "&" : "") + (isObject(value) ? JSON.stringify(value) : value);
  }
  return sha256.hash(str);
}

/**
 * 生成签名
 */
export function genderSign(data: any) {
  if (!isObject(data)) return "";
  const keys = Object.keys(data).sort();
  let str = "";
  for (let i = 0; i < keys.length; i++) {
    const value = data[keys[i]];
    str += (str ? "&" : "") + (isObject(value) ? JSON.stringify(value) : value);
  }
  return md5.hash(str).toString();
}

/**
 * 包装请求接口
 */
export function packageUrl(serverUrl: string, params: any = {}, data: any) {
  const microtime = Date.now();
  const nonstr = generateUUID();
  const hash_data = genderSha256(data);
  const secret = "jjpiewjf;k029q3-1*ksk3323m";
  const sign = genderSign({
    microtime,
    nonstr,
    hash_data,
    secret,
    ...params,
  });
  return `${serverUrl}?microtime=${microtime}&nonstr=${nonstr}&hash_data=${hash_data}&sign=${sign}`;
}

/**
 * 驼峰格式化成下划线
 */
export function camelCaseFormattedUnderscore(str: string = "") {
  return str.replace(/([A-Z])/g, "_$1").toLowerCase();
}

/**
 * 下划线转驼峰
 */
export function UnderscoreToCamelCase(str: string = "") {
  return str.replace(/\_(\w)/g, (_, letter) => letter.toUpperCase());
}

/**
 * 环境判断
 * @param {string} v1 x.x.x
 * @param {string} v2 x.x.x
 * @returns {-1 | 0 | 1}
 */
export function compareVersion(v1: string, v2: string) {
  var s1 = v1.split(".");
  var s2 = v2.split(".");
  var len = Math.max(s1.length, s2.length);

  for (let i = 0; i < len; i++) {
    var num1 = parseInt(s1[i] || "0");
    var num2 = parseInt(s2[i] || "0");

    if (num1 > num2) {
      return 1;
    } else if (num1 < num2) {
      return -1;
    }
  }

  return 0;
}

/**
 * 深克隆
 * @param obj 要拷贝的对象
 */
export function deepClone(obj: any = {}) {
  if (typeof obj !== "object" || obj == null) {
    return obj;
  }
  let result: any = Array.isArray(obj) ? [] : {};
  for (let key in obj) {
    obj.hasOwnProperty(key) && (result[key] = deepClone(obj[key]));
  }
  return result;
}

/**
 * 值是否为空
 */
export function isEmpty(value: any) {
  return value === undefined || value === "" || value === null || (typeof value === "number" && isNaN(value));
}

/**
 * 获取字符串Ascll码
 */
export function charCodeStr(text: string = "") {
  let str = "";
  for (const t of text) {
    str += t.charCodeAt(0);
  }
  return str;
}

/**
 * 从对象中尝试查找某个属性
 */
export function queryAttr(obj: any, queryKey: string, needDataset: boolean = false): any {
  if (typeof obj !== "object" || obj == null) {
    return undefined;
  }
  for (let key in obj) {
    let sensorsKey;
    const regexp = /sensors_.+/.exec(key);
    if (regexp) {
      sensorsKey = camelCaseFormattedUnderscore(regexp[0].slice(8));
    }
    if (obj.hasOwnProperty(key) && (key === queryKey || (needDataset && sensorsKey === queryKey))) {
      return obj[key];
    } else if (typeof obj[key] === "object") {
      const result = queryAttr(obj[key], queryKey);
      if (result) return result;
    }
  }
  return undefined;
}

// 浏览器指纹
export function getFingerprintId() {
  const id = nanoid(32)
  return id
}

export function getRouterMode(): "history" | "hash" | "unknown" {
  const currentUrl = window.location.href;
  const hash = window.location.hash;

  // 判断是否使用了hash模式
  if (hash !== "" && hash !== "#" && currentUrl.indexOf(hash) !== -1) {
    return "hash";
  }

  // 判断是否支持History API（通常意味着可以使用history模式）
  if ("pushState" in window.history && "replaceState" in window.history) {
    return "history";
  }

  // 如果既不符合hash模式特征，又不支持History API，则可能为旧版浏览器或非SPA应用
  return "unknown";
}

/**
 * 获取页面参数
 */
export function getQuery(): Record<string, string> {
  const arr = location.search.match(/[^?|&]+\=[^&]+/g);
  return arr ? Object.fromEntries(arr.map((str) => str.split("="))) : {};
}

// 追加动态属性,属性值必须为字符串或数字
export function appendDynamicAttribute(
  sourceData: Record<string, any>,
  attrKey: string,
  props: Record<string, string | number>
) {
  if (isObject(props)) {
    const type = attrKey.replace(/.*?dynamic(.+?)Props$/, "$1").toLowerCase();
    Object.entries(props).forEach(([key, value], index) => {
      sourceData[`dynamic_${type}_key_${index + 1}`] = key;
      sourceData[`dynamic_${type}_value_${index + 1}`] = value;
    });
  }
}

export function generateDataSetAttributes(dataset: Record<string, any> = {}) {
  const customProperties: any = {};
  for (const key in dataset) {
    const regexp = /sensors_.+/.exec(key);
    if (!regexp) continue;
    const regexpKey = regexp[0].slice(8);
    const attrKey = camelCaseFormattedUnderscore(regexpKey);
    const attrValue = dataset[key];
    const isDynamicProp = dynamicPropEnum.includes(regexpKey);
    if (isDynamicProp) {
      appendDynamicAttribute(customProperties, key, attrValue);
    } else {
      customProperties[attrKey] = attrValue;
    }
  }
  return customProperties;
}
