import fse from "fs-extra";
import path from "path";
import { fileURLToPath } from "url";

//把打包后的文件输出到配置文件中的指定本地目录，避免自己需要多次将产物拷贝到使用sdk的项目目录
const fileName = fileURLToPath(import.meta.url);
const rootPath = path.resolve(fileName, "..", "..");
const configFile = path.join(rootPath, "dist.config.json");

if (fse.pathExistsSync(rootPath) && !fse.existsSync(configFile)) {
  const fileContent = {
    directory: "这里填写开发时构建sdk到哪个目录,不用手动取拷贝sdk到业务项目中",
  };
  fse
    .outputFile(configFile, JSON.stringify(fileContent))
    .then(() => {
      console.log("File has been written successfully");
    })
    .catch((err) => {
      console.error("Error writing file:", err);
    });
}
