import fse from "fs-extra";
import path from "path";
import { fileURLToPath } from "url";

const config = fileURLToPath(import.meta.url);
const configFile = path.resolve(config, "..", "..", "dist.config.json");

export default class CopyDistBundlePlugin {
  apply(compiler) {
    compiler.hooks.afterEmit.tap("CopyDistBundlePlugin", () => {
      if (fse.pathExistsSync(configFile)) {
        const fileContent = fse.readFileSync(configFile, "utf8");
        try {
          const directory = JSON.parse(fileContent)?.directory;
          if (directory && fse.pathExistsSync(directory)) {
            const { path: filePath, filename } = compiler.options.output;
            const targetFile = path.join(filePath, filename);
            fse.copySync(targetFile, path.join(directory, filename));
          }
        } catch (err) {
          console.log(err);
        }
      }
    });
  }
}
