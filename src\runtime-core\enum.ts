/*
 * @Author: itnewdata <EMAIL>
 * @Date: 2022-02-16 22:25:44
 * @LastEditors: itnewdata <EMAIL>
 * @LastEditTime: 2023-03-22 16:05:22
 * @FilePath: \data_analysis_sdk\src\runtime-core\enum.ts
 * @Description: 全局枚举
 */

/**
 * 运行环境
 */
export const libEnum: Record<string, LibEnumType> = {
  WEB: "js", // 浏览器端
  ALIPAY_MINI: "AlipayMini", // 阿里小程序端
  UNIAPP_APP_PLUS: "uniapp-app-plus", // uniapp app plus
  UNIAPP_WEIXIN: "uniapp-weixin", // uniapp weixin
  UNIAPP_BYTEDANCE: "uniapp-byte-dance", // uniapp toutiao
  UNIAPP_ALIPAY: "uniapp-alipay", // uniapp alipay
  UNIAPP_DING: "uniapp-ding",
  UNIAPP_KUAISHOU: "uniapp-kuaishou",
  UNIAPP_H5: "uniapp-h5", // uniapp h5
  UNIAPP_JD:"uniapp-jd",
  UNIAPP_UNKNOWN: "uniapp-unknown", // uniapp 未知
};

/**
 * 事件类型
 */
export const eventEnum: Record<string, EventEnumType> = {
  LOGIN: "$Login", // 登录
  PAGE_VIEW: "$PageView", // 页面浏览
  PAGE_CLICK: "$PageClick", // 页面点击
  PAGE_STAY: "$PageStay", // 页面停留
  PAGE_LEAVE: "$PageLeave", // 页面离开
  MP_LAUNCH: "$MpLaunch", // 程序启动
  MP_HIDE: "$MpHide", // 程序卸载
  PAGE_SHARE: "$PageShare", // 页面分享
  PAGE_INVITE: "$PageInvite", // 页面邀请
  ERROR: "$Error", // 错误报告
  PAGE_EXPOSURE: "$PageExposure", // 组件曝光
  TOUCH: "$Touch", // 组件拖动
  TRACK: "$Track", // 轨迹事件
  CAPTURESCREEN: "$CaptureScreen", // 屏幕截屏
};

/**
 * 运行终端
 */
export const terminalEnum: Record<string, TerminalEnumType> = {
  PC: "pc", // 电脑来源
  WAP: "wap", // 手机来源
  ALIPAY_INDI: "alipay.indi", // 个人版小程序
  ALIPAY_MINI: "alipay.mini", // 企业版小程序
  APP_V1: "app.v1", // 用户端APP
  WX_MINI: "wx.mini", // 微信小程序
  CODEWX_MINI: "codewx.mini", // 码商-微信小程序
  ALIPAY_CODE: "alipay.code", // 码商-扫码小程序
  ALIPAY_TP_LENOVO: "alipay.tp.lenovo", // 码商-联想
  ALIPAY_TP_APPLE: "alipay.tp.apple", // 苹果小程序
  ALIPAY_CODE_TRIP: "alipay.code.trip", // 码商-扫码出行小程序
  ALIPAY_MERCHANT: "alipay.merchant", // 码商-商家小程序
  ALIPAY_TRIP: "alipay.trip", // 码商-出行小程序
  ALIPAY_TP_EZULE: "alipay.tp.ezule", // 码商-e租乐
  ALIPAY_TP_VIVO: "alipay.tp.vivo", // 支付宝小程序-vivo端口
  ALIPAY_HONOR: "alipay.honor", // 支付宝小程序-荣耀小程序
  ALIPAY_HUAWEI: "alipay.huawei", // 支付宝小程序-华为小程序
  ALIPAY_XIAOMI: "alipay.tp.xiaomi", // 支付宝小程序-小米小程序
  ALIPAY_OPPO: "alipay.tp.oppo", // 支付宝小程序-OPPO小程序
  PC_BACK: "pc.back", // 运营后台
  PC_MERCHANT: "pc.merchant", // 商家后台
  TT_MINI: "tt.mini", // 抖音小程序
  TOUTIAO: "Toutiao", // 头条小程序
  DINGTALK: "dingtalk.mini", // 钉钉小程序
  KUAISHOU: "ks.mini", // 快手小程序
  H5_MINI: "h5.mini", // h5程序
  APP_GOOFISH: "app.goofish",
  JD_MINI: "jd.mini", // 京东小程序
};

export const uniappLib = [
  libEnum.UNIAPP_WEIXIN,
  libEnum.UNIAPP_BYTEDANCE,
  libEnum.UNIAPP_ALIPAY,
  libEnum.UNIAPP_DING,
  libEnum.UNIAPP_KUAISHOU,
  libEnum.UNIAPP_H5,
  libEnum.UNIAPP_JD,
];

export const dynamicPropEnum = ["dynamicStrProps", "dynamicIntProps", "dynamicFloatProps"];
