import SubjectListenerImpl from "@/runtime-core/subject-listener-impl";

/**
 * 页面突变监听器 浏览器端
 */
export default class JsSuddenChangeListener extends SubjectListenerImpl<SuddenChangeObserver> implements SuddenChangeListener, PageObserver {
    isTrackSinglePage = false;
    init(data: SensorsPara) {
        data.pageListener.register(this);
        this.isTrackSinglePage = data.isTrackSinglePage;
    }
    onPageShow(): void {
        setTimeout(() => {
            this.listenerMutation();
        }, 0)
    }
    onPageHide(): void {
        
    }
    /**
     * 监听元素突变
     */
    listenerMutation() {
        const observer = new MutationObserver(mutations => {
            this.notify(mutations);
        });
        const targets = document.querySelectorAll('.sensors_mutations');
        for (let i=0; i<targets.length; i++) observer.observe(targets[i], {
            childList: true,
            subtree: true
        });  
    }
    notify(mutations: any) {
        this.observers.forEach(observer => observer.onSuddenChange(mutations));
    }
}