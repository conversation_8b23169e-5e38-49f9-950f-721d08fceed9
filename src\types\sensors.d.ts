type RemoveProperties = {
    key: string;
    value: string[];
}

interface IdentifyUnionInfo {
    unionid: string;
    openid: string
}

/**
 * 传感器
 */
 interface Sensors {

    // 配置数据
    para: SensorsPara;

    /**
     * 提交事件 
     */
    track(data: ReportIncident, isSend?: boolean): void;

    /**
     * 初始化
     */
    init(data: InitData): void;

    /**
     * 登录
     */
    login(userId: string): void;

    /**
     * 自定义匿名 ID
     * 默认情况下，SDK 会生成匿名 ID 并可以保证该 ID 的唯一性，如果需要替换神策默认分配的匿名 ID ，可以在配置初始化 SDK 参数之后（init 接口调用之前）立即调用 identify 方法进行替换。
     */
    identify(id: string): void;

    /**
     * 设置匿名id unionid openid 
     */
    setIdentifyUnion(data: IdentifyUnionInfo): void;

    /**
     * 添加额外请求头信息
     */
    addExtraRequestHeaders(headers: Record<string, any>): void;

    /**
     * 设置公共属性
     * @param data 公共属性
     * @param key 分组key值
     */
    appendCommProperties(data: Record<string, any>, group: string): void;

    /**
     * 替换公共属性
     * @param data 公共属性
     * @param key 分组key值
     */
    replaceCommProperties(data: Record<string, any>, groupKey?: string): void; // 替换公共属性

    /**
     * 删除公共属性
     */
    removeCommProperties(...args: string[] | RemoveProperties[]): void;

    /**
     * 删除公共属性组
     */
    removeCommPropertiesGroup(...args: string[]): void;

    /**
     * 设置页面属性
     */
    appendPageProperties(data: Record<string, any>): void;

    /**
     * 删除页面属性
     */
    removePageProperties(...args: string[]): void;

    /**
     * 附加公共属性到页面
     */
    appendCommPropertiesToPage(...args: string[]): void;

    /**
     * 附加生命周期属性
     */
    appendCycleProperties(data: Record<string, any>): void;
     
    /**
     * 删除生命周期属性
     */
    removeCycleProperties(...args: string[]): void;

    /**
     * 获取生命周期属性
     */
    getCycleProperties(): Record<string, any>;
}

/**
 * 初始化配置参数
 */
 interface InitData {
    serverUrl: string; // 埋点接口
    bindServerUrl?: string; // 绑定用户接口，为空则等待登陆之后才回开始上报数据
    universalIdUrl?: string; // 获取通用id接口
    showLog?: boolean;  // 打印日志
    isTrackSinglePage?: boolean; // 是否单页面
    trackUrlMap?: Record<string, any> // 上报地址映射
    needCookies?: boolean; // 是否需要凭证信息
    customDownloadChannel?: string // uniapp下载渠道
    isIFrame?: boolean; // 是否是嵌入的iframe页面
    terminal?: TerminalEnumType; // 运行终端
    autoTrack?: {
        appLaunch?: boolean; // 是否采集 $MPLaunch 事件
        appShow?: boolean;   // 是否采集 $MPShow 事件
        appHide?: boolean;   // 是否采集 $MPHide 事件
        pageShow?: boolean;  // 是否采集 $pageview 事件
        pageLeave?: boolean; // 是否采集 $pageLeave 事件
        pageClick?: boolean;   // 是否采集 $pageClick 事件
        pageStay?: boolean; // 是否采集 $pageStay事件
        pageExposure?: boolean; // 是否采集 $pageExposure 事件
    },
    batchSendTimeout?: number; // 批量发送延迟时间
    dataSendTimeout?: number;  // 上报请求超时时间
    scrollDelayTime?: number;  // 停留触发时间
    pageTitleConfig?: object; // 页面标题设置
    format?: Function; // 格式化上报字段
    tabBarPositionMap?:Record<string,string>
}

/**
 * 上报事件
 */
 interface ReportIncident {
    key: string | string[]; // 事件名称
    name?: string; // 事件中文名称
    eventType?: string; // 事件类型
    customProperties?: object; // 自定义属性，是一个对象，之后用来注入上报事件properties属性
    commPropertieGroup?: string | string[];
    $page_x?: number; // 点击相对于页面x轴
    $page_y?: number; // 点击相对于页面y轴
    $client_x?: number; // 点击相对于文档x轴
    $client_y?: number; // 点击相对于文档y轴
    $element_type?: string; // 元素类型
    $element_class_name?: string; // 元素类名
    $element_content?: string; // 元素内容
    $element_selector?: string; // 元素位置
    $element_path?: string; // 元素路径
    $event_duration?: number; // 页面停留时长
    $user_agent?: string; // 浏览器信息
    $appear_duration?: number; // 组件显现时长
}