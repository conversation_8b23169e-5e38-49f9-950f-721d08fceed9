/*
 * @Author: itnewdata <EMAIL>
 * @Date: 2022-02-16 22:25:44
 * @LastEditors: itnewdata <EMAIL>
 * @LastEditTime: 2023-03-24 16:58:53
 * @FilePath: \data_analysis_sdk\src\runtime-browser\js-request-handler.ts
 * @Description: 请求处理器 浏览器端
 */
import { base64Encode, packageUrl } from "@/utils";
export default class JsRequestHandler implements RequestHandler {
    serverUrl: string = '';
    needCookies: boolean = false;
    dataSendTimeout: number = 3000;  // 上报请求超时时间
    extraRequestHeaders: Record<string, any> = {}; // 额外请求头
    encryption(list: PointData[]) {
        return base64Encode(encodeURIComponent(JSON.stringify(list)));
    }
    addExtraRequestHeaders(headers: Record<string, any>): void {
        this.extraRequestHeaders = headers;
    }
    sendBeacon(url: string, blob: Blob){
        const result = navigator.sendBeacon(url, blob);
        return result ? Promise.resolve(result) : Promise.reject(result);
    }
    sendFetch(url: string, pointList: string){
        const result = fetch(url, {
            method: "POST",
            body: "data=" + pointList,
            headers: {
                "Content-Type": "application/x-www-form-urlencoded",
                ...this.extraRequestHeaders
            },
            credentials: 'include',
            keepalive: true,
        }).then(res => {
            if(res.ok){
                return res.json()
            }else{
                throw new Error('请求错误')
            }
        })
        return result
    }
    send(data: PointData[], isLast: boolean = false) {
        const pointList = this.encryption(data);
        const url = packageUrl(this.serverUrl, {}, { data: pointList });
        // 关闭时候调用
        if (isLast) {
            const blob = new Blob([`data=${pointList}`], { type: 'application/x-www-form-urlencoded' });
            // 需要请求头 cookie 等凭证就用 fetch + keepalive
            // 不需要或者不兼容则只能使用 beacon 如果即需要cookie 又不兼容keepalive则该请求不会发送成功
            if (this.needCookies && 'keepalive' in Request.prototype) {
                return this.sendFetch(url, pointList)
            } else {
                return this.sendBeacon(url, blob)
            }
        } else {
            return this.sendXhr(url, pointList);
        }
    }
    sendSing(data: PointData) {
        return this.send([data]);
    }
    sendXhr(url: string, pointList: string) {
        return new Promise((resolve, reject) => {
            let xhr: any = null;
            // code for all new browsers
            if (window.XMLHttpRequest) {
                xhr = new window.XMLHttpRequest();
            }
            // code for IE5 and IE6
            else if (window.ActiveXObject) {
                xhr = new window.ActiveXObject("Microsoft.XMLHTTP");
            }
            const origin = /https?:\/\/[^\/]+/.exec(url);
            if ((origin && origin[0] === window.location.origin) || this.needCookies) {
                xhr.withCredentials = true;
            }
            xhr.open('POST', url, true);
            xhr.setRequestHeader("content-type", "application/x-www-form-urlencoded");
            for (const key in this.extraRequestHeaders) {
                xhr.setRequestHeader(key, this.extraRequestHeaders[key]);
            }
            xhr.onreadystatechange = function () {
                if (xhr.readyState === 4) {
                    if (xhr.status === 200) {
                        resolve(xhr.response);
                    } else {
                        reject({
                            response: xhr.response,
                            status: xhr.status,
                            errorText: xhr.statusText
                        });
                    }
                }
            }
            xhr.onabort = xhr.onerror = xhr.ontimeout = function (type: any) {
                reject({
                    response: xhr.response,
                    status: xhr.status,
                    errorText: xhr.statusText
                });
            }
            xhr.send("data="+pointList);
            const timer = setTimeout(() => {
                reject({response: '', status: 12, errorText: '请求超时'});
                clearTimeout(timer);
            }, this.dataSendTimeout);
        });
    }
    sendRequest(url: string, data: any) {
        data = this.encryption(data);
        url = packageUrl(url, {}, {data});
        return this.sendXhr(url, data);
    }
    getUniversalId(url: string, data: {
        channel_user_id: string;
        channel_type: string;
        user_id?: number;
    }) {
        const base64Data = this.encryption(data as any);
        url = packageUrl(url, {}, { data: base64Data });
        return this.sendXhr(url, base64Data).then(res => ({data:JSON.parse(res as string)}));
    }
}