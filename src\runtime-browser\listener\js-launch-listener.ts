import SubjectListenerImpl from "@/runtime-core/subject-listener-impl";
import { getQuery } from '@/utils';

// 程序启动缓存标识
const dcLaunch = 'DC_LAUNCH';
// 通知阶段
type NotifyPhase = 'BEFORE' | 'AFTER';

/**
 * 程序启动监听器 浏览器端
 */
export default class JsLaunchListener extends SubjectListenerImpl<LaunchObserver> implements LaunchListener {
    init(data: SensorsPara) {
        this.listenerOnLaunch();
    };
    listenerOnLaunch() {
        const buffer = sessionStorage.getItem(dcLaunch);
        if (!buffer) {
            const options = getQuery();
            this.notify('BEFORE', options);
            sessionStorage.setItem(dcLaunch, Date.now().toString());
            this.notify('AFTER', options);
        }
    }
    notify(phase: NotifyPhase, options: any) {
        phase === 'BEFORE' && this.observers.forEach(observer => observer.onBeforeLaunch && observer.onBeforeLaunch(options));
        phase === 'AFTER' && this.observers.forEach(observer => observer.onLaunch && observer.onLaunch(options));
    }
}